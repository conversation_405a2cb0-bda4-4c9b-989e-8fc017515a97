/* 首页样式 */

.page-container {
  padding-bottom: 40rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-container.login-page {
  justify-content: center;
  align-items: center;
}

/* 用户信息区域 */
.user-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  padding: 40rpx 30rpx;
  margin: 20rpx;
  border-radius: 20rpx;
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.welcome-text {
  font-size: 26rpx;
  opacity: 0.9;
}

.health-score {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.score-circle {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  background-color: rgba(255, 255, 255, 0.2);
}

.score-number {
  font-size: 28rpx;
  font-weight: 600;
}

.score-text {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 未登录提示 */
.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  margin: 40rpx 20rpx;
  width: calc(100% - 80rpx);
  max-width: 600rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fffe 100%);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(76, 175, 80, 0.1);
}

.login-prompt::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(76, 175, 80, 0.05) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
  z-index: 0;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.prompt-icon {
  width: 140rpx;
  height: 140rpx;
  margin-bottom: 40rpx;
  opacity: 0.8;
  font-size: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
  border-radius: 50%;
  border: 2rpx solid rgba(76, 175, 80, 0.1);
  position: relative;
  z-index: 1;
}

.prompt-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 50rpx;
  text-align: center;
  line-height: 1.6;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.login-prompt .btn {
  position: relative;
  z-index: 1;
}

/* 数据概览 */
.dashboard-section {
  margin: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  padding-left: 8rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
}

.view-more {
  font-size: 26rpx;
  color: #4CAF50;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.stat-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.stat-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.stat-content {
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #4CAF50;
  margin-bottom: 8rpx;
}

.stat-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.stat-detail {
  font-size: 22rpx;
  margin-bottom: 4rpx;
}

/* 快捷操作 */
.quick-actions-section {
  margin: 20rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.action-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 12rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  font-weight: 500;
}

/* 最近活动 */
.activity-section {
  margin: 20rpx;
}

.activity-list {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon-emoji {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  border-radius: 50%;
  background-color: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.activity-icon image {
  width: 24rpx;
  height: 24rpx;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.activity-desc {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  line-height: 1.4;
}

.activity-time {
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}

.loading-text {
  margin-left: 20rpx;
  color: #666;
  font-size: 26rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
  font-size: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #ccc;
  line-height: 1.5;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .user-section {
    flex-direction: column;
    text-align: center;
  }
  
  .user-info {
    margin-bottom: 30rpx;
  }
  
  .health-score {
    flex-direction: row;
    align-items: center;
  }
  
  .score-circle {
    margin-right: 20rpx;
    margin-bottom: 0;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .stat-card,
  .action-item,
  .activity-list,
  .login-prompt {
    background-color: #2d2d2d;
    color: #fff;
  }
  
  .stat-title,
  .action-name,
  .activity-desc {
    color: #fff;
  }
  
  .stat-desc,
  .activity-time {
    color: #ccc;
  }
  
  .activity-icon {
    background-color: #404040;
  }
}