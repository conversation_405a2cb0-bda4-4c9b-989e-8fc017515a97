const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: process.env.DB_PORT || 3308,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'rfid',
  database: process.env.DB_NAME || 'smart_fridge',
  charset: 'utf8mb4',
  collation: 'utf8mb4_unicode_ci',
  timezone: '+08:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  multipleStatements: false,
  stringifyObjects: false,
  supportBigNumbers: true,
  bigNumberStrings: false,
  typeCast: function (field, next) {
    if (field.type === 'TINY' && field.length === 1) {
      return (field.string() === '1'); // 1 = true, 0 = false
    }
    return next();
  }
};

// 创建连接池
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 执行查询的通用方法
const query = async (sql, params = []) => {
  try {
    const [results] = await pool.execute(sql, params);
    return results;
  } catch (error) {
    console.error('数据库查询错误:', error);
    throw error;
  }
};

// 执行事务
const transaction = async (callback) => {
  const connection = await pool.getConnection();
  
  try {
    await connection.beginTransaction();
    
    const result = await callback(connection);
    
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
};

// 测试数据库连接
const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    return false;
  }
};

// 初始化数据库连接
const initDatabase = async () => {
  const isConnected = await testConnection();
  
  if (!isConnected) {
    console.error('数据库连接失败，请检查配置');
    process.exit(1);
  }
  
  // 检查必要的表是否存在
  try {
    await query('SELECT 1 FROM users LIMIT 1');
    console.log('✅ 数据库表检查通过');
  } catch (error) {
    console.warn('⚠️  数据库表可能不存在，请运行初始化脚本');
  }
};

module.exports = {
  pool,
  query,
  transaction,
  testConnection,
  initDatabase
};