const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || '*************',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'sXkj@2019!',
  database: process.env.DB_NAME || 'smart_fridge',
  charset: 'utf8mb4',
  timezone: '+08:00',
  multipleStatements: false,
  supportBigNumbers: true,
  bigNumberStrings: false,
  // 连接超时和重连配置
  connectTimeout: 60000,
  acquireTimeout: 60000,
  timeout: 60000,
  // 类型转换
  typeCast: function (field, next) {
    if (field.type === 'TINY' && field.length === 1) {
      return (field.string() === '1'); // 1 = true, 0 = false
    }
    return next();
  }
};

// 创建连接池
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  // 连接池配置
  idleTimeout: 900000, // 15分钟空闲超时
  maxIdle: 10, // 最大空闲连接数
  // 重连配置
  reconnect: true,
  // 心跳检测
  enableKeepAlive: true,
  keepAliveInitialDelay: 0
});

// 执行查询的通用方法（带重试机制）
const query = async (sql, params = [], retries = 3) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const [results] = await pool.execute(sql, params);
      return results;
    } catch (error) {
      console.error(`数据库查询错误 (尝试 ${attempt}/${retries}):`, error.message);

      // 如果是连接错误且还有重试次数，则重试
      if ((error.code === 'ECONNRESET' || error.code === 'PROTOCOL_CONNECTION_LOST' || error.code === 'ENOTFOUND') && attempt < retries) {
        console.log(`等待 ${attempt * 1000}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, attempt * 1000));
        continue;
      }

      // 最后一次尝试失败或非连接错误，抛出异常
      throw error;
    }
  }
};

// 执行事务
const transaction = async (callback) => {
  const connection = await pool.getConnection();
  
  try {
    await connection.beginTransaction();
    
    const result = await callback(connection);
    
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
};

// 测试数据库连接
const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    return false;
  }
};

// 连接池事件监听
pool.on('connection', (connection) => {
  console.log('🔗 新的数据库连接建立:', connection.threadId);
});

pool.on('error', (error) => {
  console.error('❌ 连接池错误:', error.message);
  if (error.code === 'PROTOCOL_CONNECTION_LOST') {
    console.log('🔄 数据库连接丢失，连接池将自动重连');
  }
});

// 初始化数据库连接
const initDatabase = async () => {
  const isConnected = await testConnection();

  if (!isConnected) {
    console.error('数据库连接失败，请检查配置');
    process.exit(1);
  }

  // 检查必要的表是否存在
  try {
    await query('SELECT 1 FROM users LIMIT 1');
    console.log('✅ 数据库表检查通过');
  } catch (error) {
    console.warn('⚠️  数据库表可能不存在，请运行初始化脚本');
  }
};

module.exports = {
  pool,
  query,
  transaction,
  testConnection,
  initDatabase
};