/* 菜谱详情页面样式 */

/* Emoji图标通用样式 */
.emoji-icon {
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  display: inline-block;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
}

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 内容容器 */
.content-container {
  display: flex;
  flex-direction: column;
}

/* 菜谱头部 */
.recipe-header {
  position: relative;
  height: 500rpx;
  overflow: hidden;
}

.recipe-image {
  width: 100%;
  height: 100%;
}

.recipe-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 60rpx 30rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.recipe-info {
  flex: 1;
  color: white;
}

.recipe-name {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  display: block;
}

.recipe-description {
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 20rpx;
  display: block;
  line-height: 1.4;
}

.recipe-meta {
  display: flex;
  gap: 30rpx;
  margin-bottom: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-icon {
  width: 28rpx;
  height: 28rpx;
  filter: brightness(0) invert(1);
}

.meta-text {
  font-size: 24rpx;
  opacity: 0.9;
}

.recipe-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag-item {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  backdrop-filter: blur(10rpx);
}

.header-actions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.action-btn {
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.action-btn:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.action-btn.favorite.active {
  background-color: #ff4757;
}

.action-btn.favorite.active:active {
  background-color: #ff3742;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

/* 快速操作栏 */
.quick-actions {
  background-color: white;
  padding: 30rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.quick-btn {
  flex: 1;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  transition: all 0.3s ease;
}

.quick-btn:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

.quick-btn.primary {
  background-color: #667eea;
  border-color: #667eea;
  color: white;
}

.quick-btn.primary:active {
  background-color: #5a6fd8;
}

.quick-btn.danger {
  background-color: #ff4757;
  border-color: #ff4757;
  color: white;
}

.quick-btn.danger:active {
  background-color: #ff3742;
}

.quick-icon {
  width: 40rpx;
  height: 40rpx;
}

.quick-btn.primary .quick-icon,
.quick-btn.danger .quick-icon {
  filter: brightness(0) invert(1);
}

.quick-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 制作模式导航 */
.cooking-nav {
  background-color: white;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #e0e0e0;
}

.nav-btn {
  background-color: #667eea;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.nav-btn:active {
  background-color: #5a6fd8;
  transform: scale(0.98);
}

.nav-btn[disabled] {
  background-color: #e9ecef;
  color: #6c757d;
}

.nav-icon {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

.nav-btn[disabled] .nav-icon {
  filter: none;
  opacity: 0.5;
}

.step-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.step-current {
  font-size: 32rpx;
  font-weight: 600;
  color: #667eea;
}

.step-total {
  font-size: 28rpx;
  color: #666;
}

/* 标签页导航 */
.tab-nav {
  background-color: white;
  padding: 0 30rpx;
  display: flex;
  border-bottom: 1rpx solid #e0e0e0;
}

.tab-item {
  flex: 1;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab-item:active {
  background-color: rgba(102, 126, 234, 0.05);
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #667eea;
  border-radius: 2rpx;
}

.tab-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.tab-item.active .tab-icon {
  opacity: 1;
  filter: hue-rotate(220deg) saturate(1.5);
}

.tab-text {
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.tab-item.active .tab-text {
  color: #667eea;
  font-weight: 500;
}

/* 标签页内容 */
.tab-content {
  flex: 1;
  background-color: white;
}

/* 食材内容 */
.ingredients-content {
  padding: 30rpx;
}

.ingredients-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.ingredient-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.ingredient-item:active {
  background-color: #e9ecef;
}

.ingredient-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ingredient-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.ingredient-amount {
  font-size: 26rpx;
  color: #666;
}

.ingredient-status {
  margin-left: 20rpx;
}

.status-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 步骤内容 */
.steps-content {
  padding: 30rpx;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.step-item {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background-color: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.step-description {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.step-image {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
}

/* 制作模式内容 */
.cooking-content {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.current-step {
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.step-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.complete-btn {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.complete-btn:active {
  background-color: #218838;
  transform: scale(0.98);
}

.complete-btn.completed {
  background-color: #6c757d;
}

.complete-btn.completed:active {
  background-color: #5a6268;
}

.complete-icon {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

.step-detail {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.steps-progress {
  display: flex;
  justify-content: center;
  gap: 12rpx;
}

.progress-dot {
  width: 16rpx;
  height: 16rpx;
  background-color: #e9ecef;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.progress-dot.completed {
  background-color: #28a745;
}

.progress-dot.current {
  background-color: #667eea;
  transform: scale(1.2);
}

/* 营养内容 */
.nutrition-content {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.nutrition-summary {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.nutrition-item {
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.nutrition-label {
  font-size: 24rpx;
  color: #666;
}

.nutrition-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.detail-btn {
  background-color: #667eea;
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.detail-btn:active {
  background-color: #5a6fd8;
  transform: scale(0.98);
}

.detail-icon {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

/* 评价内容 */
.rating-content {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.rating-summary {
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  justify-content: center;
}

.rating-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.score-number {
  font-size: 60rpx;
  font-weight: 600;
  color: #667eea;
}

.score-stars {
  display: flex;
  gap: 8rpx;
}

.star-icon {
  width: 32rpx;
  height: 32rpx;
}

.star-icon.filled {
  filter: hue-rotate(45deg) saturate(1.5);
}

.score-count {
  font-size: 24rpx;
  color: #666;
}

.user-rating {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.rating-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.rating-stars {
  display: flex;
  gap: 16rpx;
}

.rating-star {
  width: 48rpx;
  height: 48rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rating-star:active {
  transform: scale(1.1);
}

.rating-star.active {
  filter: hue-rotate(45deg) saturate(1.5);
}

/* 弹窗遮罩 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 60rpx;
}

/* 分享弹窗 */
.share-modal {
  background-color: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #f8f9fa;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.3s ease;
}

.close-btn:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

.close-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.7;
}

.share-options {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.share-option {
  background-color: #f8f9fa;
  border: none;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  transition: all 0.3s ease;
}

.share-option:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

.share-icon {
  width: 48rpx;
  height: 48rpx;
}

.share-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 营养详情弹窗 */
.nutrition-modal {
  background-color: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  overflow: hidden;
}

.nutrition-detail {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.nutrition-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.nutrition-row:last-child {
  border-bottom: none;
}

.nutrition-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.nutrition-amount {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 600;
}

/* 分享画布 */
.share-canvas {
  position: fixed;
  top: -9999rpx;
  left: -9999rpx;
  width: 750rpx;
  height: 1000rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .recipe-overlay {
    padding: 40rpx 20rpx 20rpx;
  }
  
  .recipe-name {
    font-size: 36rpx;
  }
  
  .recipe-meta {
    gap: 20rpx;
  }
  
  .quick-actions {
    padding: 20rpx;
    gap: 15rpx;
  }
  
  .quick-btn {
    padding: 20rpx 16rpx;
  }
  
  .tab-content,
  .ingredients-content,
  .steps-content,
  .nutrition-content,
  .rating-content,
  .cooking-content {
    padding: 20rpx;
  }
  
  .nutrition-summary {
    grid-template-columns: 1fr;
  }
  
  .modal-overlay {
    padding: 40rpx 20rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .quick-actions,
  .cooking-nav,
  .tab-nav,
  .tab-content,
  .share-modal,
  .nutrition-modal {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .quick-btn,
  .ingredient-item,
  .step-item,
  .current-step,
  .nutrition-item,
  .rating-summary,
  .share-option,
  .close-btn {
    background-color: #404040;
    border-color: #555;
  }
  
  .quick-btn:active,
  .ingredient-item:active,
  .share-option:active,
  .close-btn:active {
    background-color: #555;
  }
  
  .recipe-name,
  .step-title,
  .modal-title,
  .nutrition-name,
  .share-text,
  .ingredient-name,
  .step-description,
  .nutrition-value,
  .rating-title {
    color: white;
  }
  
  .recipe-description,
  .meta-text,
  .ingredient-amount,
  .nutrition-label,
  .score-count,
  .loading-text,
  .tab-text {
    color: #999;
  }
  
  .tab-item.active .tab-text {
    color: #667eea;
  }
  
  .step-current {
    color: #667eea;
  }
  
  .step-total {
    color: #999;
  }
  
  .nutrition-amount {
    color: #667eea;
  }
}

/* 动画效果 */
.ingredient-item,
.step-item,
.nutrition-item {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tag-item {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.progress-dot {
  animation: pulse 0.3s ease;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.progress-dot.current {
  animation: currentPulse 2s ease-in-out infinite;
}

@keyframes currentPulse {
  0%, 100% {
    transform: scale(1.2);
    opacity: 1;
  }
  50% {
    transform: scale(1.4);
    opacity: 0.7;
  }
}