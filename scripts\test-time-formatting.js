// 测试时间格式化效果
console.log('🕒 测试时间格式化效果\n');

// 模拟app.formatDate函数
function formatDate(date, format = 'YYYY-MM-DD') {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hour = String(d.getHours()).padStart(2, '0');
  const minute = String(d.getMinutes()).padStart(2, '0');
  const second = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second);
}

// 格式化友好的日期显示
function formatFriendlyDate(dateString) {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  
  const diffDays = Math.floor((targetDate - today) / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    return '今天';
  } else if (diffDays === 1) {
    return '明天';
  } else if (diffDays === -1) {
    return '昨天';
  } else if (diffDays > 1 && diffDays <= 7) {
    return `${diffDays}天后`;
  } else if (diffDays < -1 && diffDays >= -7) {
    return `${Math.abs(diffDays)}天前`;
  } else {
    // 超过一周的显示具体日期
    return formatDate(date, 'YYYY-MM-DD');
  }
}

// 格式化友好的日期时间显示
function formatFriendlyDateTime(dateString) {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);
  
  if (diffMins < 1) {
    return '刚刚';
  } else if (diffMins < 60) {
    return `${diffMins}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays === 1) {
    return `昨天 ${formatDate(date, 'HH:mm')}`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return formatDate(date, 'MM-DD HH:mm');
  }
}

// 测试数据
const testDates = [
  // 过期时间测试
  {
    type: '过期时间',
    original: '2025-08-07T16:00:00.000Z',
    description: '未来日期'
  },
  {
    type: '过期时间',
    original: '2025-07-31T16:00:00.000Z',
    description: '过去日期'
  },
  // 购买时间测试
  {
    type: '购买时间',
    original: '2025-08-01T03:04:34.000Z',
    description: '今天'
  },
  // 添加时间测试
  {
    type: '添加时间',
    original: '2025-08-01T03:04:34.000Z',
    description: '几小时前'
  }
];

console.log('📅 日期格式化测试:');
testDates.forEach((test, index) => {
  console.log(`\n${index + 1}. ${test.type} (${test.description}):`);
  console.log(`   原始: ${test.original}`);
  
  if (test.type === '添加时间') {
    console.log(`   格式化: ${formatFriendlyDateTime(test.original)}`);
  } else {
    console.log(`   格式化: ${formatFriendlyDate(test.original)}`);
  }
});

// 测试各种时间场景
console.log('\n⏰ 时间场景测试:');

const now = new Date();
const scenarios = [
  {
    name: '今天',
    date: new Date(now.getFullYear(), now.getMonth(), now.getDate(), 12, 0, 0)
  },
  {
    name: '明天',
    date: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 12, 0, 0)
  },
  {
    name: '昨天',
    date: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 12, 0, 0)
  },
  {
    name: '3天后',
    date: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 3, 12, 0, 0)
  },
  {
    name: '5天前',
    date: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 5, 12, 0, 0)
  },
  {
    name: '2周后',
    date: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 14, 12, 0, 0)
  },
  {
    name: '1个月前',
    date: new Date(now.getFullYear(), now.getMonth() - 1, now.getDate(), 12, 0, 0)
  }
];

scenarios.forEach((scenario, index) => {
  console.log(`\n${index + 1}. ${scenario.name}:`);
  console.log(`   日期: ${formatFriendlyDate(scenario.date.toISOString())}`);
  console.log(`   时间: ${formatFriendlyDateTime(scenario.date.toISOString())}`);
});

console.log('\n✨ 格式化效果对比:');
console.log('\n修复前:');
console.log('  过期时间: 2025-08-07T16:00:00.000Z');
console.log('  购买时间: 2025-07-31T16:00:00.000Z');
console.log('  添加时间: 2025-08-01T03:04:34.000Z');

console.log('\n修复后:');
console.log('  过期时间: 6天后');
console.log('  购买时间: 昨天');
console.log('  添加时间: 几小时前');

console.log('\n🎯 优化效果:');
console.log('✅ 时间显示更加友好和直观');
console.log('✅ 支持相对时间显示（今天、明天、几天前等）');
console.log('✅ 自动处理不同时间范围的显示格式');
console.log('✅ 保持原有数据的向后兼容性');

console.log('\n💡 显示规则:');
console.log('📅 日期显示:');
console.log('  - 今天/明天/昨天');
console.log('  - 1-7天内: "X天前/后"');
console.log('  - 超过7天: "YYYY-MM-DD"');
console.log('\n⏰ 时间显示:');
console.log('  - 1分钟内: "刚刚"');
console.log('  - 1小时内: "X分钟前"');
console.log('  - 1天内: "X小时前"');
console.log('  - 昨天: "昨天 HH:mm"');
console.log('  - 1周内: "X天前"');
console.log('  - 超过1周: "MM-DD HH:mm"');
