// 登录页面逻辑
const app = getApp();

Page({
  data: {
    username: '',
    password: '',
    loading: false,
    showPassword: false
  },

  onLoad() {
    console.log('登录页面加载');
    // 检查是否已登录
    if (app.globalData.token) {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  // 输入用户名
  onUsernameInput(e) {
    this.setData({
      username: e.detail.value
    });
  },

  // 输入密码
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 切换密码显示
  togglePasswordShow() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 登录
  async handleLogin() {
    const { username, password } = this.data;

    if (!username.trim()) {
      app.showError('请输入用户名');
      return;
    }

    if (!password.trim()) {
      app.showError('请输入密码');
      return;
    }

    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      // 密码将在服务端进行验证和加密处理
      await app.login({
        username: username.trim(),
        password: password.trim()
      });

      // 登录成功，跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      });
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },

  // 跳转到注册页面
  goToRegister() {
    wx.navigateTo({
      url: '/pages/register/register'
    });
  },

  // 忘记密码
  handleForgotPassword() {
    wx.showModal({
      title: '忘记密码',
      content: '请联系管理员重置密码',
      showCancel: false
    });
  }
});