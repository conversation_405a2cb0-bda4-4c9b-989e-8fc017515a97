// 登录页面逻辑
const app = getApp();
const crypto = require('../../utils/crypto');

Page({
  data: {
    username: '',
    password: '',
    loading: false,
    showPassword: false
  },

  onLoad() {
    console.log('登录页面加载');
    // 检查是否已登录
    if (app.globalData.token) {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  // 输入用户名
  onUsernameInput(e) {
    this.setData({
      username: e.detail.value
    });
  },

  // 输入密码
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 切换密码显示
  togglePasswordShow() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 登录
  async handleLogin() {
    const { username, password } = this.data;

    if (!username.trim()) {
      app.showError('请输入用户名');
      return;
    }

    if (!password.trim()) {
      app.showError('请输入密码');
      return;
    }

    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      // 注意：在实际项目中，密码加密和验证应该在服务器端进行
      // 这里只是演示客户端加密，实际应用中不应该在客户端进行密码验证
      const encryptedPassword = crypto.encryptPassword(password.trim());

      await app.login({
        username: username.trim(),
        password: encryptedPassword
      });

      // 登录成功，跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      });
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },

  // 跳转到注册页面
  goToRegister() {
    wx.navigateTo({
      url: '/pages/register/register'
    });
  },

  // 忘记密码
  handleForgotPassword() {
    wx.showModal({
      title: '忘记密码',
      content: '请联系管理员重置密码',
      showCancel: false
    });
  }
});