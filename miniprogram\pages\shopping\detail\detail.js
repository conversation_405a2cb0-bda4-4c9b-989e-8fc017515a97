Page({
  data: {
    formData: {
      name: '',
      category: '',
      quantity: '',
      unit: '',
      price: '',
      unitPrice: '', // 单价字段
      priority: 'medium',
      notes: ''
    },
    categories: [
      '蔬菜类', '水果类', '肉类', '海鲜类', '蛋奶类', 
      '粮油类', '调料类', '零食类', '饮品类', '其他'
    ],
    units: ['个', '斤', '克', '千克', '包', '瓶', '盒', '袋'],
    selectedCategoryIndex: -1,
    selectedUnitIndex: -1,
    isEdit: false,
    itemId: null,
    totalPrice: 0
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        isEdit: true,
        itemId: options.id
      });
      this.loadItemData(options.id);
    }
  },

  loadItemData(id) {
    // 模拟加载购物项数据
    const mockData = {
      name: '苹果',
      category: '水果类',
      quantity: '2',
      unit: '斤',
      price: '8.50',
      unitPrice: '8.50', // 添加单价字段
      priority: 'medium',
      notes: '制作苹果派'
    };

    const categoryIndex = this.data.categories.indexOf(mockData.category);
    const unitIndex = this.data.units.indexOf(mockData.unit);

    this.setData({
      formData: mockData,
      selectedCategoryIndex: categoryIndex,
      selectedUnitIndex: unitIndex
    });

    console.log('详情页面数据加载完成', {
      formData: mockData,
      categoryIndex: categoryIndex,
      unitIndex: unitIndex
    });

    this.calculateTotal();
  },

  onNameInput(e) {
    this.setData({
      'formData.name': e.detail.value
    });
  },

  onCategoryChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      selectedCategoryIndex: index,
      'formData.category': this.data.categories[index]
    });
  },

  onQuantityInput(e) {
    console.log('详情页面数量输入', {
      oldValue: this.data.formData.quantity,
      newValue: e.detail.value
    });
    this.setData({
      'formData.quantity': e.detail.value
    });
    this.calculateTotal();
  },

  onUnitPriceInput(e) {
    console.log('详情页面单价输入', {
      oldValue: this.data.formData.unitPrice,
      newValue: e.detail.value
    });
    this.setData({
      'formData.unitPrice': e.detail.value
    });
    this.calculateTotal();
  },

  onUnitChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      selectedUnitIndex: index,
      'formData.unit': this.data.units[index]
    });
  },

  onPriceInput(e) {
    this.setData({
      'formData.price': e.detail.value
    });
    this.calculateTotal();
  },

  onPriorityChange(e) {
    this.setData({
      'formData.priority': e.currentTarget.dataset.priority
    });
  },

  onNotesInput(e) {
    this.setData({
      'formData.notes': e.detail.value
    });
  },

  calculateTotal() {
    const { quantity, unitPrice } = this.data.formData;
    const quantityNum = parseFloat(quantity) || 0;
    const priceNum = parseFloat(unitPrice) || 0;
    const total = quantityNum * priceNum;

    console.log('详情页面计算总价', {
      quantity: quantity,
      unitPrice: unitPrice,
      quantityNum: quantityNum,
      priceNum: priceNum,
      total: total.toFixed(2)
    });

    this.setData({
      totalPrice: total.toFixed(2)
    });
  },

  validateForm() {
    const { name, category, quantity, unit } = this.data.formData;
    
    if (!name.trim()) {
      wx.showToast({
        title: '请输入商品名称',
        icon: 'none'
      });
      return false;
    }

    if (!category) {
      wx.showToast({
        title: '请选择商品分类',
        icon: 'none'
      });
      return false;
    }

    if (!quantity || parseFloat(quantity) <= 0) {
      wx.showToast({
        title: '请输入有效数量',
        icon: 'none'
      });
      return false;
    }

    if (!unit) {
      wx.showToast({
        title: '请选择数量单位',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  onSave() {
    if (!this.validateForm()) {
      return;
    }

    wx.showLoading({ title: '保存中...' });

    // 模拟保存数据
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: this.data.isEdit ? '修改成功' : '添加成功',
        icon: 'success'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  },

  onCancel() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消编辑吗？未保存的内容将丢失。',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 删除购物项
  onDelete() {
    if (!this.data.isEdit || !this.data.itemId) {
      wx.showToast({
        title: '无法删除',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个购物项吗？删除后无法恢复。',
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteItem();
        }
      }
    });
  },

  // 执行删除操作
  async deleteItem() {
    const app = getApp();

    try {
      wx.showLoading({
        title: '删除中...',
        mask: true
      });

      // 调用真正的删除API
      await app.request({
        url: `/shopping/${this.data.itemId}`,
        method: 'DELETE'
      });

      wx.hideLoading();

      wx.showToast({
        title: '删除成功',
        icon: 'success',
        duration: 1500
      });

      // 通知列表页面刷新数据
      const pages = getCurrentPages();
      if (pages.length > 1) {
        const prevPage = pages[pages.length - 2];
        if (prevPage && prevPage.route === 'pages/shopping/list/list') {
          // 如果上一页是购物清单页面，刷新数据
          if (typeof prevPage.loadShoppingList === 'function') {
            prevPage.loadShoppingList();
          }
        }
      }

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      wx.hideLoading();
      console.error('删除购物项失败:', error);

      wx.showToast({
        title: error.message || '删除失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  }
});