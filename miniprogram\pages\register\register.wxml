<!--注册页面-->
<view class="register-container page-container">
  <view class="register-header fade-in-up">
    <view class="logo app-logo">🧊</view>
    <text class="app-name">创建账户</text>
    <text class="app-desc">加入智能冰箱管理</text>
  </view>

  <view class="register-form form-card fade-in-up">
    <view class="form-group">
      <view class="input-wrapper">
        <view class="input-icon">👤</view>
        <input
          class="form-input"
          type="text"
          placeholder="用户名"
          value="{{username}}"
          bindinput="onUsernameInput"
          maxlength="20"
        />
      </view>
    </view>

    <view class="form-group">
      <view class="input-wrapper">
        <view class="input-icon">📧</view>
        <input
          class="form-input"
          type="text"
          placeholder="邮箱"
          value="{{email}}"
          bindinput="onEmailInput"
          maxlength="50"
        />
      </view>
    </view>

    <view class="form-group">
      <view class="input-wrapper">
        <view class="input-icon">🔒</view>
        <input
          class="form-input"
          type="{{showPassword ? 'text' : 'password'}}"
          placeholder="密码"
          value="{{password}}"
          bindinput="onPasswordInput"
          maxlength="20"
        />
        <view
          class="password-toggle"
          bindtap="togglePasswordShow"
        >{{showPassword ? '👁️' : '🙈'}}</view>
      </view>
    </view>

    <view class="form-group">
      <view class="input-wrapper">
        <view class="input-icon">🔒</view>
        <input
          class="form-input"
          type="{{showConfirmPassword ? 'text' : 'password'}}"
          placeholder="确认密码"
          value="{{confirmPassword}}"
          bindinput="onConfirmPasswordInput"
          maxlength="20"
        />
        <view
          class="password-toggle"
          bindtap="toggleConfirmPasswordShow"
        >{{showConfirmPassword ? '👁️' : '🙈'}}</view>
      </view>
    </view>

    <view class="agreement-section">
      <checkbox-group bindchange="onAgreeChange">
        <label class="agreement-checkbox">
          <checkbox value="agreed" checked="{{agreed}}" />
          <view class="agreement-content">
            <text class="agreement-text">我已阅读并同意</text><text class="agreement-link" bindtap="viewUserAgreement">《用户协议》</text><text class="agreement-text">和</text><text class="agreement-link" bindtap="viewPrivacyPolicy">《隐私政策》</text>
          </view>
        </label>
      </checkbox-group>
    </view>

    <button 
      class="btn btn-primary btn-block register-btn" 
      bindtap="handleRegister"
      loading="{{loading}}"
      disabled="{{loading}}"
    >
      <view wx:if="{{loading}}" class="loading-spinner"></view>
      {{loading ? '注册中...' : '立即注册'}}
    </button>

    <view class="login-link-section">
      <text class="login-prompt">已有账户？</text>
      <text class="login-link" bindtap="goToLogin">立即登录</text>
    </view>
  </view>
</view>