/**
 * 密码加密工具
 */

/**
 * 简单的密码哈希函数（使用SHA-256模拟）
 * 注意：这是一个简化版本，实际项目中应该使用更安全的加密方式
 */
function simpleHash(str) {
  let hash = 0;
  if (str.length === 0) return hash.toString();
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  
  // 转换为正数并添加盐值
  const salt = 'miniprogram_salt_2024';
  const saltedStr = str + salt;
  let saltedHash = 0;
  
  for (let i = 0; i < saltedStr.length; i++) {
    const char = saltedStr.charCodeAt(i);
    saltedHash = ((saltedHash << 5) - saltedHash) + char;
    saltedHash = saltedHash & saltedHash;
  }
  
  return Math.abs(saltedHash).toString(16);
}

/**
 * 密码加密
 * @param {string} password 原始密码
 * @returns {string} 加密后的密码
 */
function encryptPassword(password) {
  if (!password) return '';
  
  // 添加时间戳作为额外的盐值
  const timestamp = Date.now().toString();
  const combined = password + timestamp.slice(-4); // 使用时间戳的后4位
  
  return simpleHash(combined);
}

/**
 * 验证密码（用于登录时验证）
 * @param {string} inputPassword 用户输入的密码
 * @param {string} storedHash 存储的密码哈希
 * @returns {boolean} 是否匹配
 */
function verifyPassword(inputPassword, storedHash) {
  // 注意：这是简化版本，实际应用中需要存储盐值
  // 这里只是演示，实际项目应该使用专业的密码库
  const inputHash = simpleHash(inputPassword);
  return inputHash === storedHash;
}

module.exports = {
  encryptPassword,
  verifyPassword,
  simpleHash
};
