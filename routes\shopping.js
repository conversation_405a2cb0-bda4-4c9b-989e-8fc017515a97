const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 获取购物清单
router.get('/', authenticateToken, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      category_id,
      is_purchased,
      search,
      sort = 'created_at',
      order = 'DESC'
    } = req.query;

    // 验证排序字段
    const validSortFields = ['created_at', 'updated_at', 'name', 'quantity', 'price', 'category_id'];
    const sortField = validSortFields.includes(sort) ? sort : 'created_at';
    
    const offset = (page - 1) * limit;
    let whereConditions = ['sl.user_id = ?', 'sl.is_deleted = 0'];
    let queryParams = [req.user.id];

    // 按分类筛选
    if (category_id) {
      whereConditions.push('sl.category_id = ?');
      queryParams.push(category_id);
    }

    // 按购买状态筛选
    if (is_purchased !== undefined) {
      whereConditions.push('sl.is_purchased = ?');
      queryParams.push(is_purchased === 'true' ? 1 : 0);
    }

    // 搜索功能
    if (search) {
      whereConditions.push('sl.name LIKE ?');
      queryParams.push(`%${search}%`);
    }

    // 构建查询语句
    const whereClause = whereConditions.join(' AND ');
    const orderClause = `ORDER BY sl.${sortField} ${order}`;

    const shoppingList = await query(`
      SELECT 
        sl.*,
        ic.name as category_name
      FROM shopping_list sl
      LEFT JOIN ingredient_categories ic ON sl.category_id = ic.id
      WHERE ${whereClause}
      ${orderClause}
      LIMIT ? OFFSET ?
    `, [...queryParams, parseInt(limit), offset]);

    // 获取总数和统计信息
    const [countResult, statsResult] = await Promise.all([
      query(`
        SELECT COUNT(*) as total
        FROM shopping_list sl
        WHERE ${whereClause}
      `, queryParams),
      query(`
        SELECT 
          COUNT(*) as total_items,
          SUM(CASE WHEN is_purchased = 1 THEN 1 ELSE 0 END) as purchased_items,
          SUM(CASE WHEN is_purchased = 0 THEN 1 ELSE 0 END) as pending_items,
          SUM(CASE WHEN is_purchased = 1 THEN actual_price ELSE price END) as total_amount
        FROM shopping_list
        WHERE user_id = ? AND is_deleted = 0
      `, [req.user.id])
    ]);

    const total = countResult[0].total;
    const stats = statsResult[0];

    res.json({
      success: true,
      data: {
        shopping_list: shoppingList,
        statistics: {
          total_items: stats.total_items || 0,
          purchased_items: stats.purchased_items || 0,
          pending_items: stats.pending_items || 0,
          total_amount: parseFloat(stats.total_amount || 0),
          completion_rate: stats.total_items > 0 ? Math.round((stats.purchased_items / stats.total_items) * 100) : 0
        },
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取购物清单错误:', error);
    res.status(500).json({
      success: false,
      message: '获取购物清单失败'
    });
  }
});

// 添加购物项目
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      name,
      category_id,
      quantity = 1,
      unit = '个',
      price = 0,
      notes
    } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '商品名称不能为空'
      });
    }

    // 检查是否已存在相同的未购买商品
    const existingItems = await query(
      'SELECT id FROM shopping_list WHERE user_id = ? AND name = ? AND is_purchased = 0 AND is_deleted = 0',
      [req.user.id, name]
    );

    if (existingItems.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该商品已在购物清单中'
      });
    }

    const result = await query(`
      INSERT INTO shopping_list 
      (user_id, name, category_id, quantity, unit, price, notes)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      req.user.id,
      name,
      category_id || null,
      quantity,
      unit,
      price,
      notes || null
    ]);

    // 记录用户操作
    await query(
      'INSERT INTO user_records (user_id, type, content) VALUES (?, ?, ?)',
      [
        req.user.id,
        'shopping_add',
        JSON.stringify({
          shopping_id: result.insertId,
          item_name: name,
          quantity,
          unit,
          price
        })
      ]
    );

    // 获取新添加的购物项目信息
    const newItem = await query(`
      SELECT 
        sl.*,
        ic.name as category_name
      FROM shopping_list sl
      LEFT JOIN ingredient_categories ic ON sl.category_id = ic.id
      WHERE sl.id = ?
    `, [result.insertId]);

    res.status(201).json({
      success: true,
      message: '购物项目添加成功',
      data: newItem[0]
    });
  } catch (error) {
    console.error('添加购物项目错误:', error);
    res.status(500).json({
      success: false,
      message: '添加购物项目失败'
    });
  }
});

// 更新购物项目
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      category_id,
      quantity,
      unit,
      price,
      actual_price,
      notes,
      is_purchased
    } = req.body;

    // 检查购物项目是否存在且属于当前用户
    const existingItems = await query(
      'SELECT id, name, is_purchased FROM shopping_list WHERE id = ? AND user_id = ? AND is_deleted = 0',
      [id, req.user.id]
    );

    if (existingItems.length === 0) {
      return res.status(404).json({
        success: false,
        message: '购物项目不存在'
      });
    }

    const updateFields = [];
    const updateValues = [];

    if (name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(name);
    }
    if (category_id !== undefined) {
      updateFields.push('category_id = ?');
      updateValues.push(category_id);
    }
    if (quantity !== undefined) {
      updateFields.push('quantity = ?');
      updateValues.push(quantity);
    }
    if (unit !== undefined) {
      updateFields.push('unit = ?');
      updateValues.push(unit);
    }
    if (price !== undefined) {
      updateFields.push('price = ?');
      updateValues.push(price);
    }
    if (actual_price !== undefined) {
      updateFields.push('actual_price = ?');
      updateValues.push(actual_price);
    }
    if (notes !== undefined) {
      updateFields.push('notes = ?');
      updateValues.push(notes);
    }
    if (is_purchased !== undefined) {
      updateFields.push('is_purchased = ?');
      updateValues.push(is_purchased ? 1 : 0);
      
      // 如果标记为已购买，记录购买时间
      if (is_purchased) {
        updateFields.push('purchase_date = CURRENT_TIMESTAMP');
      }
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有需要更新的字段'
      });
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id, req.user.id);

    await query(
      `UPDATE shopping_list SET ${updateFields.join(', ')} WHERE id = ? AND user_id = ?`,
      updateValues
    );

    // 如果是标记为已购买，记录操作
    if (is_purchased && !existingItems[0].is_purchased) {
      await query(
        'INSERT INTO user_records (user_id, type, content) VALUES (?, ?, ?)',
        [
          req.user.id,
          'shopping_purchase',
          JSON.stringify({
            shopping_id: id,
            item_name: existingItems[0].name,
            actual_price: actual_price || price
          })
        ]
      );
    }

    // 获取更新后的购物项目信息
    const updatedItem = await query(`
      SELECT 
        sl.*,
        ic.name as category_name
      FROM shopping_list sl
      LEFT JOIN ingredient_categories ic ON sl.category_id = ic.id
      WHERE sl.id = ?
    `, [id]);

    res.json({
      success: true,
      message: '购物项目更新成功',
      data: updatedItem[0]
    });
  } catch (error) {
    console.error('更新购物项目错误:', error);
    res.status(500).json({
      success: false,
      message: '更新购物项目失败'
    });
  }
});

// 删除购物项目
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // 检查购物项目是否存在且属于当前用户
    const items = await query(
      'SELECT id, name FROM shopping_list WHERE id = ? AND user_id = ? AND is_deleted = 0',
      [id, req.user.id]
    );

    if (items.length === 0) {
      return res.status(404).json({
        success: false,
        message: '购物项目不存在'
      });
    }

    // 软删除购物项目
    await query(
      'UPDATE shopping_list SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
      [id, req.user.id]
    );

    res.json({
      success: true,
      message: '购物项目删除成功'
    });
  } catch (error) {
    console.error('删除购物项目错误:', error);
    res.status(500).json({
      success: false,
      message: '删除购物项目失败'
    });
  }
});

// 批量操作购物项目
router.post('/batch', authenticateToken, async (req, res) => {
  try {
    const { action, ids } = req.body;

    if (!action || !ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '参数错误'
      });
    }

    const placeholders = ids.map(() => '?').join(',');
    let message = '';

    switch (action) {
      case 'delete':
        await query(
          `UPDATE shopping_list SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP 
           WHERE id IN (${placeholders}) AND user_id = ?`,
          [...ids, req.user.id]
        );
        message = '批量删除成功';
        break;

      case 'mark_purchased':
        await query(
          `UPDATE shopping_list SET is_purchased = 1, purchase_date = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP 
           WHERE id IN (${placeholders}) AND user_id = ?`,
          [...ids, req.user.id]
        );
        message = '批量标记已购买成功';
        break;

      case 'mark_unpurchased':
        await query(
          `UPDATE shopping_list SET is_purchased = 0, purchase_date = NULL, updated_at = CURRENT_TIMESTAMP 
           WHERE id IN (${placeholders}) AND user_id = ?`,
          [...ids, req.user.id]
        );
        message = '批量标记未购买成功';
        break;

      default:
        return res.status(400).json({
          success: false,
          message: '不支持的操作类型'
        });
    }

    res.json({
      success: true,
      message
    });
  } catch (error) {
    console.error('批量操作购物项目错误:', error);
    res.status(500).json({
      success: false,
      message: '批量操作失败'
    });
  }
});

// 智能生成购物清单
router.post('/generate', authenticateToken, async (req, res) => {
  try {
    const { recipe_ids, days = 7 } = req.body;

    let suggestedItems = [];

    if (recipe_ids && Array.isArray(recipe_ids) && recipe_ids.length > 0) {
      // 基于菜谱生成购物清单
      const placeholders = recipe_ids.map(() => '?').join(',');
      const recipes = await query(`
        SELECT ingredients FROM recipes 
        WHERE id IN (${placeholders}) AND is_public = 1 AND is_deleted = 0
      `, recipe_ids);

      // 收集所有需要的食材
      const allIngredients = [];
      recipes.forEach(recipe => {
        try {
          const ingredients = JSON.parse(recipe.ingredients || '[]');
          allIngredients.push(...ingredients);
        } catch (error) {
          console.error('解析菜谱食材错误:', error);
        }
      });

      // 获取用户现有食材
      const userIngredients = await query(
        'SELECT name, quantity, unit FROM user_ingredients WHERE user_id = ? AND is_deleted = 0 AND status IN (1, 2)',
        [req.user.id]
      );

      const userIngredientMap = {};
      userIngredients.forEach(item => {
        userIngredientMap[item.name] = {
          quantity: parseFloat(item.quantity),
          unit: item.unit
        };
      });

      // 计算需要购买的食材
      const ingredientNeeds = {};
      allIngredients.forEach(ingredient => {
        const name = ingredient.name;
        const needed = parseFloat(ingredient.quantity) || 1;
        const unit = ingredient.unit || '个';

        if (ingredientNeeds[name]) {
          ingredientNeeds[name].quantity += needed;
        } else {
          ingredientNeeds[name] = {
            quantity: needed,
            unit: unit
          };
        }
      });

      // 生成购物建议
      for (const [name, need] of Object.entries(ingredientNeeds)) {
        const userHas = userIngredientMap[name];
        let needToBuy = need.quantity;

        if (userHas && userHas.unit === need.unit) {
          needToBuy = Math.max(0, need.quantity - userHas.quantity);
        }

        if (needToBuy > 0) {
          // 获取常用食材信息
          const commonIngredients = await query(
            'SELECT * FROM common_ingredients WHERE name = ? AND is_deleted = 0',
            [name]
          );

          const commonIngredient = commonIngredients[0];

          suggestedItems.push({
            name: name,
            category_id: commonIngredient?.category_id || null,
            quantity: needToBuy,
            unit: need.unit,
            price: 0,
            notes: '根据菜谱自动生成',
            source: 'recipe'
          });
        }
      }
    } else {
      // 基于用户历史购买和即将过期的食材生成建议
      const [expiringSoon, frequentItems] = await Promise.all([
        // 即将过期的食材
        query(`
          SELECT name, category_id, quantity, unit
          FROM user_ingredients 
          WHERE user_id = ? AND is_deleted = 0 AND status = 2
          ORDER BY expire_date ASC
          LIMIT 10
        `, [req.user.id]),
        
        // 经常购买的商品
        query(`
          SELECT name, category_id, AVG(quantity) as avg_quantity, unit, COUNT(*) as purchase_count
          FROM shopping_list 
          WHERE user_id = ? AND is_purchased = 1 AND is_deleted = 0
          AND purchase_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
          GROUP BY name, category_id, unit
          HAVING purchase_count >= 2
          ORDER BY purchase_count DESC
          LIMIT 10
        `, [req.user.id])
      ]);

      // 添加即将过期食材的补充建议
      expiringSoon.forEach(item => {
        suggestedItems.push({
          name: item.name,
          category_id: item.category_id,
          quantity: item.quantity,
          unit: item.unit,
          price: 0,
          notes: '即将过期，建议补充',
          source: 'expiring'
        });
      });

      // 添加常购商品建议
      frequentItems.forEach(item => {
        suggestedItems.push({
          name: item.name,
          category_id: item.category_id,
          quantity: Math.ceil(item.avg_quantity),
          unit: item.unit,
          price: 0,
          notes: '常购商品建议',
          source: 'frequent'
        });
      });
    }

    // 去重处理
    const uniqueItems = [];
    const itemNames = new Set();
    
    suggestedItems.forEach(item => {
      if (!itemNames.has(item.name)) {
        itemNames.add(item.name);
        uniqueItems.push(item);
      }
    });

    res.json({
      success: true,
      message: '购物清单生成成功',
      data: {
        suggested_items: uniqueItems,
        total_items: uniqueItems.length
      }
    });
  } catch (error) {
    console.error('生成购物清单错误:', error);
    res.status(500).json({
      success: false,
      message: '生成购物清单失败'
    });
  }
});

// 添加建议的购物项目到清单
router.post('/add-suggestions', authenticateToken, async (req, res) => {
  try {
    const { items } = req.body;

    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要添加的商品列表'
      });
    }

    const addedItems = [];
    const skippedItems = [];

    for (const item of items) {
      try {
        // 检查是否已存在相同的未购买商品
        const existingItems = await query(
          'SELECT id FROM shopping_list WHERE user_id = ? AND name = ? AND is_purchased = 0 AND is_deleted = 0',
          [req.user.id, item.name]
        );

        if (existingItems.length > 0) {
          skippedItems.push({
            name: item.name,
            reason: '已存在于购物清单中'
          });
          continue;
        }

        // 添加到购物清单
        const result = await query(`
          INSERT INTO shopping_list 
          (user_id, name, category_id, quantity, unit, price, notes)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
          req.user.id,
          item.name,
          item.category_id || null,
          item.quantity || 1,
          item.unit || '个',
          item.price || 0,
          item.notes || null
        ]);

        addedItems.push({
          id: result.insertId,
          name: item.name,
          quantity: item.quantity || 1,
          unit: item.unit || '个'
        });
      } catch (error) {
        console.error(`添加商品 ${item.name} 失败:`, error);
        skippedItems.push({
          name: item.name,
          reason: '添加失败'
        });
      }
    }

    res.json({
      success: true,
      message: `成功添加 ${addedItems.length} 个商品到购物清单`,
      data: {
        added_items: addedItems,
        skipped_items: skippedItems,
        added_count: addedItems.length,
        skipped_count: skippedItems.length
      }
    });
  } catch (error) {
    console.error('批量添加购物项目错误:', error);
    res.status(500).json({
      success: false,
      message: '批量添加失败'
    });
  }
});

// 清空已购买的商品
router.delete('/purchased', authenticateToken, async (req, res) => {
  try {
    await query(
      'UPDATE shopping_list SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP WHERE user_id = ? AND is_purchased = 1',
      [req.user.id]
    );

    res.json({
      success: true,
      message: '已购买商品清空成功'
    });
  } catch (error) {
    console.error('清空已购买商品错误:', error);
    res.status(500).json({
      success: false,
      message: '清空已购买商品失败'
    });
  }
});

// 根据菜谱添加购物清单
router.post('/add-recipe', authenticateToken, async (req, res) => {
  try {
    const { recipe_id } = req.body;

    if (!recipe_id) {
      return res.status(400).json({
        success: false,
        message: '菜谱ID不能为空'
      });
    }

    // 获取菜谱信息
    const recipes = await query(
      'SELECT id, name, ingredients FROM recipes WHERE id = ? AND is_deleted = 0',
      [recipe_id]
    );

    if (recipes.length === 0) {
      return res.status(404).json({
        success: false,
        message: '菜谱不存在'
      });
    }

    const recipe = recipes[0];
    let recipeIngredients = [];

    // 解析菜谱食材
    try {
      recipeIngredients = JSON.parse(recipe.ingredients || '[]');
    } catch (error) {
      console.error('解析菜谱食材失败:', error);
      return res.status(400).json({
        success: false,
        message: '菜谱食材数据格式错误'
      });
    }

    if (recipeIngredients.length === 0) {
      return res.status(400).json({
        success: false,
        message: '该菜谱没有食材信息'
      });
    }

    // 获取用户现有食材
    const userIngredients = await query(
      'SELECT name, quantity, unit FROM user_ingredients WHERE user_id = ? AND is_deleted = 0 AND status IN (1, 2)',
      [req.user.id]
    );

    const userIngredientMap = {};
    userIngredients.forEach(item => {
      userIngredientMap[item.name.toLowerCase()] = {
        quantity: parseFloat(item.quantity) || 0,
        unit: item.unit
      };
    });

    const addedItems = [];
    const skippedItems = [];

    // 处理每个菜谱食材
    for (const ingredient of recipeIngredients) {
      const ingredientName = ingredient.name;
      const requiredQuantity = parseFloat(ingredient.quantity) || 1;
      const requiredUnit = ingredient.unit || '个';

      try {
        // 检查用户是否已有足够的食材
        const userHas = userIngredientMap[ingredientName.toLowerCase()];
        let needToBuy = requiredQuantity;

        if (userHas && userHas.unit === requiredUnit) {
          needToBuy = Math.max(0, requiredQuantity - userHas.quantity);
        }

        // 如果不需要购买，跳过
        if (needToBuy <= 0) {
          skippedItems.push({
            name: ingredientName,
            reason: '库存充足'
          });
          continue;
        }

        // 检查是否已在购物清单中
        const existingItems = await query(
          'SELECT id, quantity FROM shopping_list WHERE user_id = ? AND name = ? AND is_purchased = 0 AND is_deleted = 0',
          [req.user.id, ingredientName]
        );

        if (existingItems.length > 0) {
          // 如果已存在，更新数量
          const existingItem = existingItems[0];
          const newQuantity = parseFloat(existingItem.quantity) + needToBuy;

          await query(
            'UPDATE shopping_list SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [newQuantity, existingItem.id]
          );

          addedItems.push({
            id: existingItem.id,
            name: ingredientName,
            quantity: newQuantity,
            unit: requiredUnit,
            action: 'updated'
          });
        } else {
          // 获取食材分类
          let categoryId = null;
          try {
            const categories = await query(
              'SELECT id FROM ingredient_categories WHERE name LIKE ? LIMIT 1',
              [`%${ingredientName}%`]
            );
            if (categories.length > 0) {
              categoryId = categories[0].id;
            }
          } catch (error) {
            console.error('获取食材分类失败:', error);
          }

          // 添加新的购物项目
          const result = await query(`
            INSERT INTO shopping_list
            (user_id, name, category_id, quantity, unit, price, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `, [
            req.user.id,
            ingredientName,
            categoryId,
            needToBuy,
            requiredUnit,
            0,
            `制作菜谱：${recipe.name}`
          ]);

          addedItems.push({
            id: result.insertId,
            name: ingredientName,
            quantity: needToBuy,
            unit: requiredUnit,
            action: 'added'
          });
        }
      } catch (error) {
        console.error(`处理食材 ${ingredientName} 失败:`, error);
        skippedItems.push({
          name: ingredientName,
          reason: '处理失败'
        });
      }
    }

    // 记录用户操作
    try {
      await query(
        'INSERT INTO user_records (user_id, type, content) VALUES (?, ?, ?)',
        [
          req.user.id,
          'shopping_add_recipe',
          JSON.stringify({
            recipe_id: recipe_id,
            recipe_name: recipe.name,
            added_count: addedItems.length,
            skipped_count: skippedItems.length
          })
        ]
      );
    } catch (error) {
      console.error('记录用户操作失败:', error);
    }

    res.json({
      success: true,
      message: `成功处理菜谱《${recipe.name}》的食材，添加了 ${addedItems.length} 个购物项目`,
      data: {
        recipe_name: recipe.name,
        added_items: addedItems,
        skipped_items: skippedItems,
        added_count: addedItems.length,
        skipped_count: skippedItems.length,
        total_ingredients: recipeIngredients.length
      }
    });

  } catch (error) {
    console.error('根据菜谱添加购物清单错误:', error);
    res.status(500).json({
      success: false,
      message: '添加购物清单失败'
    });
  }
});

module.exports = router;