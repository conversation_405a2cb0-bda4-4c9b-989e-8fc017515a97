// 购物清单添加页面
const app = getApp();

Page({
  data: {
    // 表单数据
    name: '',
    category: '',
    amount: '',
    unit: '',
    priority: 1,
    notes: '',
    
    // 选择器数据
    categories: [
      { id: 'vegetables', name: '蔬菜' },
      { id: 'fruits', name: '水果' },
      { id: 'meat', name: '肉类' },
      { id: 'seafood', name: '海鲜' },
      { id: 'dairy', name: '乳制品' },
      { id: 'grains', name: '谷物' },
      { id: 'condiments', name: '调料' },
      { id: 'snacks', name: '零食' },
      { id: 'beverages', name: '饮料' },
      { id: 'others', name: '其他' }
    ],
    
    units: ['个', '斤', '克', '千克', '升', '毫升', '包', '袋', '盒', '瓶'],
    
    priorities: [
      { value: 1, name: '低', color: '#28a745' },
      { value: 2, name: '中', color: '#ffc107' },
      { value: 3, name: '高', color: '#dc3545' }
    ],
    
    // 界面状态
    showCategoryPicker: false,
    showUnitPicker: false,
    showPriorityPicker: false,
    categoryIndex: 0,
    unitIndex: 0,
    priorityIndex: 0,
    
    // 页面状态
    loading: false,
    isEdit: false,
    itemId: null
  },

  onLoad(options) {
    console.log('购物清单添加页面加载', options);
    
    if (options.id) {
      this.setData({ 
        isEdit: true,
        itemId: options.id 
      });
      this.loadItemDetail(options.id);
    }
    
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: options.id ? '编辑购物项' : '添加购物项'
    });
  },

  // 加载购物项详情
  async loadItemDetail(id) {
    try {
      this.setData({ loading: true });
      
      const res = await app.request({
        url: `/shopping/${id}`
      });
      const item = res.data;
      
      // 找到对应的索引
      const categoryIndex = this.data.categories.findIndex(cat => cat.id === item.category_id);
      const unitIndex = this.data.units.findIndex(unit => unit === item.unit);
      const priorityIndex = 0; // 默认优先级

      this.setData({
        name: item.name,
        category: item.category_id,
        amount: item.quantity ? item.quantity.toString() : '1',
        unit: item.unit || '个',
        priority: 'normal',
        notes: item.notes || '',
        categoryIndex: Math.max(0, categoryIndex),
        unitIndex: Math.max(0, unitIndex),
        priorityIndex: Math.max(0, priorityIndex),
        loading: false
      });
      
    } catch (error) {
      console.error('加载购物项详情失败:', error);
      app.showError('加载详情失败');
      this.setData({ loading: false });
    }
  },

  // 输入处理
  onNameInput(e) {
    this.setData({ name: e.detail.value });
  },

  onAmountInput(e) {
    let value = e.detail.value;

    // 格式化数量输入
    if (value) {
      // 只允许数字和小数点
      value = value.replace(/[^\d.]/g, '');

      // 确保只有一个小数点
      const parts = value.split('.');
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
      }

      // 限制小数位数为2位
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }

      // 确保数值合理
      const numValue = parseFloat(value);
      if (numValue > 9999) {
        value = '9999';
      }
    }

    this.setData({ amount: value });
  },

  onNotesInput(e) {
    this.setData({ notes: e.detail.value });
  },

  // 显示分类选择器
  showCategoryPicker() {
    this.setData({ showCategoryPicker: true });
  },

  // 分类选择
  onCategoryChange(e) {
    let index;
    if (e.detail && e.detail.value) {
      // picker-view的change事件
      index = Array.isArray(e.detail.value) ? e.detail.value[0] : e.detail.value;
    } else if (e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.value !== undefined) {
      // 确定按钮的点击事件
      index = this.data.categoryIndex;
    } else {
      return;
    }

    const category = this.data.categories[index];
    if (category) {
      this.setData({
        categoryIndex: index,
        category: category.id,
        showCategoryPicker: false
      });
    }
  },

  // 分类选择器值变化
  onCategoryPickerChange(e) {
    const index = e.detail.value[0];
    this.setData({
      categoryIndex: index
    });
  },

  // 取消分类选择
  onCategoryCancel() {
    this.setData({ showCategoryPicker: false });
  },

  // 显示单位选择器
  showUnitPicker() {
    this.setData({ showUnitPicker: true });
  },

  // 单位选择
  onUnitChange(e) {
    let index;
    if (e.detail && e.detail.value) {
      // picker-view的change事件
      index = Array.isArray(e.detail.value) ? e.detail.value[0] : e.detail.value;
    } else if (e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.value !== undefined) {
      // 确定按钮的点击事件
      index = this.data.unitIndex;
    } else {
      return;
    }

    const unit = this.data.units[index];
    if (unit) {
      this.setData({
        unitIndex: index,
        unit: unit,
        showUnitPicker: false
      });
    }
  },

  // 单位选择器值变化
  onUnitPickerChange(e) {
    const index = e.detail.value[0];
    this.setData({
      unitIndex: index
    });
  },

  // 取消单位选择
  onUnitCancel() {
    this.setData({ showUnitPicker: false });
  },

  // 显示优先级选择器
  showPriorityPicker() {
    this.setData({ showPriorityPicker: true });
  },

  // 优先级选择
  onPriorityChange(e) {
    const index = e.detail.value;
    const priority = this.data.priorities[index];
    
    this.setData({
      priorityIndex: index,
      priority: priority.value,
      showPriorityPicker: false
    });
  },

  // 取消优先级选择
  onPriorityCancel() {
    this.setData({ showPriorityPicker: false });
  },

  // 验证表单
  validateForm() {
    const { name, category, amount, unit } = this.data;

    if (!name.trim()) {
      app.showError('请输入商品名称');
      return false;
    }

    if (!category) {
      app.showError('请选择商品分类');
      return false;
    }

    if (!amount.trim()) {
      app.showError('请输入购买数量');
      return false;
    }

    if (isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      app.showError('请输入有效的数量');
      return false;
    }

    if (!unit) {
      app.showError('请选择数量单位');
      return false;
    }

    return true;
  },

  // 保存购物项
  async saveItem() {
    if (!this.validateForm()) {
      return;
    }

    try {
      this.setData({ loading: true });

      const itemData = {
        name: this.data.name.trim(),
        category_id: this.data.category,
        quantity: parseFloat(this.data.amount),
        unit: this.data.unit,
        notes: this.data.notes.trim()
      };

      if (this.data.isEdit) {
        await app.request({
          url: `/shopping/${this.data.itemId}`,
          method: 'PUT',
          data: itemData
        });
        app.showSuccess('购物项已更新');
      } else {
        await app.request({
          url: '/shopping',
          method: 'POST',
          data: itemData
        });
        app.showSuccess('购物项已添加');
      }

      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1000);

    } catch (error) {
      console.error('保存购物项失败:', error);
      app.showError('保存失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 取消操作
  cancel() {
    wx.navigateBack();
  },

  // 获取分类名称
  getCategoryName(categoryId) {
    const category = this.data.categories.find(cat => cat.id === categoryId);
    return category ? category.name : '请选择';
  },

  // 获取优先级名称和颜色
  getPriorityInfo(priority) {
    const priorityInfo = this.data.priorities.find(p => p.value === priority);
    return priorityInfo || this.data.priorities[0];
  }
});