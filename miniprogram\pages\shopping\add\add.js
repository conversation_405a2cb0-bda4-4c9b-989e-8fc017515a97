// 购物清单添加页面
const app = getApp();

Page({
  data: {
    // 表单数据
    name: '',
    category: 'vegetables', // 默认选择蔬菜分类
    amount: '1',
    unit: '个',
    price: '', // 单价
    priority: 1,
    notes: '',
    
    // 选择器数据
    categories: [
      { id: 'vegetables', name: '蔬菜', icon: '🥬' },
      { id: 'fruits', name: '水果', icon: '🍎' },
      { id: 'meat', name: '肉类', icon: '🥩' },
      { id: 'seafood', name: '海鲜', icon: '🐟' },
      { id: 'dairy', name: '乳制品', icon: '🥛' },
      { id: 'grains', name: '谷物', icon: '🌾' },
      { id: 'condiments', name: '调料', icon: '🧂' },
      { id: 'snacks', name: '零食', icon: '🍿' },
      { id: 'beverages', name: '饮料', icon: '🥤' },
      { id: 'others', name: '其他', icon: '📦' }
    ],
    
    units: ['个', '斤', '克', '千克', '升', '毫升', '包', '袋', '盒', '瓶'],
    
    priorities: [
      { value: 1, name: '低', color: '#28a745' },
      { value: 2, name: '中', color: '#ffc107' },
      { value: 3, name: '高', color: '#dc3545' }
    ],
    
    // 界面状态
    showCategoryPicker: false,
    showUnitPicker: false,
    showPriorityPicker: false,
    categoryIndex: 0,
    unitIndex: 0,
    priorityIndex: 0,
    
    // 页面状态
    loading: false,
    saving: false,
    isEdit: false,
    itemId: null,
    canSave: true, // 初始设为true，因为有默认值

    // 原始数据（用于检测变更）
    originalData: null,
    hasChanges: false,

    // 计算字段
    totalPrice: '0.00'
  },

  // 最简单的测试方法
  simpleTest() {
    console.log('简单测试被调用');
    wx.showModal({
      title: '测试',
      content: '按钮点击成功！',
      showCancel: false
    });
  },

  onLoad(options) {
    console.log('🚀 购物清单添加页面加载开始', options);
    console.log('🔧 Page对象方法检查', {
      simpleTest: typeof this.simpleTest,
      testButtonClick: typeof this.testButtonClick,
      saveItem: typeof this.saveItem
    });
    
    if (options.id) {
      this.setData({ 
        isEdit: true,
        itemId: options.id 
      });
      this.loadItemDetail(options.id);
    }
    
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: options.id ? '编辑购物项' : '添加购物项'
    });

    // 初始化表单验证和总价计算
    this.validateForm();
    const initialTotalPrice = this.calculateTotalPrice(this.data.amount, this.data.price);
    console.log('页面初始化总价计算', {
      amount: this.data.amount,
      price: this.data.price,
      initialTotalPrice: initialTotalPrice
    });
    this.setData({
      totalPrice: initialTotalPrice
    });

    // 调试信息
    console.log('页面初始化完成', {
      name: this.data.name,
      category: this.data.category,
      amount: this.data.amount,
      unit: this.data.unit,
      canSave: this.data.canSave,
      totalPrice: this.data.totalPrice
    });
  },

  // 加载购物项详情
  async loadItemDetail(id) {
    try {
      this.setData({ loading: true });
      
      const res = await app.request({
        url: `/shopping/${id}`
      });
      const item = res.data;
      
      // 找到对应的索引
      const categoryIndex = this.data.categories.findIndex(cat => cat.id === item.category_id);
      const unitIndex = this.data.units.findIndex(unit => unit === item.unit);
      const priorityIndex = 0; // 默认优先级

      // 保存原始数据用于变更检测
      const originalData = {
        name: item.name,
        category: item.category_id,
        amount: item.quantity ? item.quantity.toString() : '1',
        unit: item.unit || '个',
        price: item.price ? item.price.toString() : '',
        priority: 'normal',
        notes: item.notes || ''
      };

      this.setData({
        name: originalData.name,
        category: originalData.category,
        amount: originalData.amount,
        unit: originalData.unit,
        price: originalData.price,
        priority: originalData.priority,
        notes: originalData.notes,
        categoryIndex: Math.max(0, categoryIndex),
        unitIndex: Math.max(0, unitIndex),
        priorityIndex: Math.max(0, priorityIndex),
        loading: false,
        originalData: originalData, // 保存原始数据
        hasChanges: false
      });
      
    } catch (error) {
      console.error('加载购物项详情失败:', error);
      app.showError('加载详情失败');
      this.setData({ loading: false });
    }
  },

  // 输入处理
  onNameInput(e) {
    this.setData({ name: e.detail.value });
    this.validateForm();
  },

  onAmountInput(e) {
    let value = e.detail.value;

    // 格式化数量输入
    if (value) {
      // 只允许数字和小数点
      value = value.replace(/[^\d.]/g, '');

      // 确保只有一个小数点
      const parts = value.split('.');
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
      }

      // 限制小数位数为2位
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }

      // 确保数值合理
      const numValue = parseFloat(value);
      if (numValue > 9999) {
        value = '9999';
      }
    }

    const newTotalPrice = this.calculateTotalPrice(value, this.data.price);
    console.log('数量输入更新', {
      oldAmount: this.data.amount,
      newAmount: value,
      price: this.data.price,
      oldTotalPrice: this.data.totalPrice,
      newTotalPrice: newTotalPrice
    });

    this.setData({
      amount: value,
      totalPrice: newTotalPrice
    });
    this.validateForm();
  },

  // 价格输入
  onPriceInput(e) {
    let value = e.detail.value;

    // 格式化价格输入
    if (value) {
      // 只允许数字和小数点
      value = value.replace(/[^\d.]/g, '');

      // 确保只有一个小数点
      const parts = value.split('.');
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
      }

      // 限制小数位数为2位
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }

      // 确保数值合理
      const numValue = parseFloat(value);
      if (numValue > 99999) {
        value = '99999';
      }
    }

    const newTotalPrice = this.calculateTotalPrice(this.data.amount, value);
    console.log('价格输入更新', {
      amount: this.data.amount,
      oldPrice: this.data.price,
      newPrice: value,
      oldTotalPrice: this.data.totalPrice,
      newTotalPrice: newTotalPrice
    });

    this.setData({
      price: value,
      totalPrice: newTotalPrice
    });
    this.validateForm();
  },

  onNotesInput(e) {
    this.setData({ notes: e.detail.value });
    this.validateForm();
  },

  // 检测数据是否发生变更
  checkDataChanges() {
    if (!this.data.isEdit || !this.data.originalData) {
      return false;
    }

    const current = {
      name: this.data.name,
      category: this.data.category,
      amount: this.data.amount,
      unit: this.data.unit,
      price: this.data.price,
      priority: this.data.priority,
      notes: this.data.notes
    };

    const original = this.data.originalData;

    // 比较每个字段
    const hasChanges =
      current.name !== original.name ||
      current.category !== original.category ||
      current.amount !== original.amount ||
      current.unit !== original.unit ||
      current.price !== original.price ||
      current.priority !== original.priority ||
      current.notes !== original.notes;

    console.log('数据变更检测', {
      current: current,
      original: original,
      hasChanges: hasChanges
    });

    this.setData({ hasChanges });
    return hasChanges;
  },

  // 表单验证
  validateForm() {
    const { name, category, amount, unit } = this.data;
    const canSave = name.trim() && category && amount && parseFloat(amount) > 0 && unit;

    // 检测数据变更
    this.checkDataChanges();

    console.log('表单验证结果', {
      name: name,
      category: category,
      amount: amount,
      unit: unit,
      canSave: canSave,
      hasChanges: this.data.hasChanges
    });

    // 设置表单是否可以保存的状态
    this.setData({ canSave });

    return canSave;
  },

  // 计算总价
  calculateTotalPrice(amount, price) {
    // 处理空值和无效值
    const quantity = amount && !isNaN(parseFloat(amount)) ? parseFloat(amount) : 0;
    const unitPrice = price && !isNaN(parseFloat(price)) ? parseFloat(price) : 0;
    const total = (quantity * unitPrice).toFixed(2);

    console.log('计算总价详细', {
      原始amount: amount,
      原始price: price,
      解析后quantity: quantity,
      解析后unitPrice: unitPrice,
      计算结果total: total,
      amount类型: typeof amount,
      price类型: typeof price
    });

    return total;
  },

  // 获取总价（兼容旧方法）
  getTotalPrice() {
    return this.calculateTotalPrice(this.data.amount, this.data.price);
  },

  // 显示分类选择器
  showCategoryPicker() {
    this.setData({ showCategoryPicker: true });
  },

  // 分类选择
  onCategoryChange(e) {
    let index;
    if (e.detail && e.detail.value) {
      // picker-view的change事件
      index = Array.isArray(e.detail.value) ? e.detail.value[0] : e.detail.value;
    } else if (e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.value !== undefined) {
      // 确定按钮的点击事件
      index = this.data.categoryIndex;
    } else {
      return;
    }

    const category = this.data.categories[index];
    if (category) {
      this.setData({
        categoryIndex: index,
        category: category.id,
        showCategoryPicker: false
      });
      this.validateForm();
    }
  },

  // 分类选择器值变化
  onCategoryPickerChange(e) {
    const index = e.detail.value[0];
    this.setData({
      categoryIndex: index
    });
  },

  // 取消分类选择
  onCategoryCancel() {
    this.setData({ showCategoryPicker: false });
  },

  // 显示单位选择器
  showUnitPicker() {
    console.log('显示单位选择器');
    this.setData({ showUnitPicker: true });
  },

  // 单位选择
  onUnitChange(e) {
    console.log('单位选择', e);
    let index;
    if (e.detail && e.detail.value) {
      // picker-view的change事件
      index = Array.isArray(e.detail.value) ? e.detail.value[0] : e.detail.value;
    } else if (e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.value !== undefined) {
      // 确定按钮的点击事件
      index = this.data.unitIndex;
    } else {
      return;
    }

    const unit = this.data.units[index];
    if (unit) {
      console.log('选择的单位:', unit);
      this.setData({
        unitIndex: index,
        unit: unit,
        showUnitPicker: false
      });
      this.validateForm();
    }
  },

  // 单位选择器值变化
  onUnitPickerChange(e) {
    const index = e.detail.value[0];
    this.setData({
      unitIndex: index
    });
  },

  // 取消单位选择
  onUnitCancel() {
    this.setData({ showUnitPicker: false });
  },

  // 显示优先级选择器
  showPriorityPicker() {
    this.setData({ showPriorityPicker: true });
  },

  // 优先级选择
  onPriorityChange(e) {
    const index = e.currentTarget.dataset.index;
    if (index === undefined || index === null) {
      console.error('优先级选择: 无效的索引');
      return;
    }

    const priority = this.data.priorities[index];
    if (priority) {
      this.setData({
        priorityIndex: index,
        priority: priority.value
      });
      this.validateForm();
    }
  },

  // 取消优先级选择
  onPriorityCancel() {
    this.setData({ showPriorityPicker: false });
  },

  // 测试按钮点击
  testButtonClick() {
    console.log('🧪 测试按钮被点击了！');
    wx.showToast({
      title: '测试按钮工作正常',
      icon: 'success',
      duration: 1500
    });
  },

  // 简化的保存方法测试
  simpleSave() {
    console.log('🔥 simpleSave被调用');
    wx.showModal({
      title: '保存测试',
      content: '简化保存方法被调用了！',
      showCancel: false
    });
  },

  // 测试网络请求
  async testNetworkRequest() {
    console.log('🌐 测试网络请求开始');

    try {
      // 检查app对象
      const app = getApp();
      console.log('📱 app对象检查', {
        app: !!app,
        request: typeof app.request,
        showSuccess: typeof app.showSuccess,
        showError: typeof app.showError
      });

      // 测试简单的网络请求
      const testData = {
        name: '测试商品',
        category_id: 'vegetables',
        quantity: 1,
        unit: '个',
        price: 10,
        priority: 'normal',
        notes: '测试备注'
      };

      console.log('📤 准备发送测试数据', testData);

      const result = await app.request({
        url: '/shopping',
        method: 'POST',
        data: testData
      });

      console.log('✅ 网络请求成功', result);
      wx.showModal({
        title: '网络测试成功',
        content: '数据保存成功！',
        showCancel: false
      });

    } catch (error) {
      console.error('❌ 网络请求失败', error);
      wx.showModal({
        title: '网络测试失败',
        content: `错误: ${error.message || '未知错误'}`,
        showCancel: false
      });
    }
  },





  // 保存购物项
  async saveItem() {
    // 立即显示反馈，确认方法被调用
    console.log('🚀 saveItem方法开始执行！');

    wx.showToast({
      title: 'saveItem被调用',
      icon: 'success',
      duration: 2000
    });

    // 检查页面状态
    console.log('🔍 页面状态检查', {
      showCategoryPicker: this.data.showCategoryPicker,
      showUnitPicker: this.data.showUnitPicker,
      showPriorityPicker: this.data.showPriorityPicker,
      saving: this.data.saving,
      canSave: this.data.canSave
    });

    console.log('🔥 saveItem被调用了！', {
      isEdit: this.data.isEdit,
      name: this.data.name,
      category: this.data.category,
      amount: this.data.amount,
      unit: this.data.unit,
      canSave: this.data.canSave,
      saving: this.data.saving,
      showCategoryPicker: this.data.showCategoryPicker,
      showUnitPicker: this.data.showUnitPicker
    });

    // 如果是编辑模式，检查数据是否有变更
    if (this.data.isEdit) {
      const hasChanges = this.checkDataChanges();

      if (!hasChanges) {
        // 数据没有变更，询问用户是否确定保存
        const result = await wx.showModal({
          title: '提示',
          content: '数据没有更改，是否确定保存？',
          confirmText: '确定保存',
          cancelText: '取消'
        });

        if (!result.confirm) {
          console.log('用户取消保存');
          return;
        }
      }
    }

    // 检查必填字段
    const { name, category, amount, unit } = this.data;

    console.log('📋 表单数据检查', {
      name: name,
      nameLength: name ? name.length : 0,
      nameTrimmed: name ? name.trim() : '',
      category: category,
      amount: amount,
      unit: unit
    });

    if (!name || !name.trim()) {
      console.log('❌ 商品名称验证失败');
      wx.showToast({
        title: '请输入商品名称',
        icon: 'none'
      });
      return;
    }

    if (!category) {
      wx.showToast({
        title: '请选择商品分类',
        icon: 'none'
      });
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      wx.showToast({
        title: '请输入有效数量',
        icon: 'none'
      });
      return;
    }

    if (!unit) {
      console.log('❌ 数量单位验证失败');
      wx.showToast({
        title: '请选择数量单位',
        icon: 'none'
      });
      return;
    }

    console.log('✅ 所有表单验证通过，开始保存数据');

    try {
      this.setData({ saving: true });
      console.log('开始保存数据', this.data);

      const itemData = {
        name: this.data.name.trim(),
        category_id: this.data.category,
        quantity: parseFloat(this.data.amount),
        unit: this.data.unit,
        price: this.data.price ? parseFloat(this.data.price) : null,
        priority: this.data.priority,
        notes: this.data.notes.trim()
      };

      console.log('准备提交的数据:', itemData);

      if (this.data.isEdit) {
        await app.request({
          url: `/shopping/${this.data.itemId}`,
          method: 'PUT',
          data: itemData
        });
        app.showSuccess('购物项已更新');
      } else {
        await app.request({
          url: '/shopping',
          method: 'POST',
          data: itemData
        });
        app.showSuccess('购物项已添加');
      }

      // 通知列表页面刷新数据
      const pages = getCurrentPages();
      if (pages.length > 1) {
        const prevPage = pages[pages.length - 2];
        if (prevPage && prevPage.route === 'pages/shopping/list/list') {
          // 如果上一页是购物清单页面，刷新数据
          if (typeof prevPage.loadShoppingList === 'function') {
            prevPage.loadShoppingList();
          }
        }
      }

      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1000);

    } catch (error) {
      console.error('保存购物项失败:', error);
      app.showError('保存失败: ' + (error.message || '未知错误'));
    } finally {
      this.setData({ saving: false });
    }
  },



  // 取消操作
  cancel() {
    wx.navigateBack();
  },

  // 删除购物项
  async deleteItem() {
    if (!this.data.isEdit || !this.data.itemId) {
      wx.showToast({
        title: '无法删除',
        icon: 'none'
      });
      return;
    }

    const result = await wx.showModal({
      title: '确认删除',
      content: '确定要删除这个购物项吗？',
      confirmText: '删除',
      confirmColor: '#ff4757'
    });

    if (!result.confirm) {
      return;
    }

    try {
      this.setData({ saving: true });

      await app.request({
        url: `/shopping/${this.data.itemId}`,
        method: 'DELETE'
      });

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

      // 通知列表页面刷新数据
      const pages = getCurrentPages();
      if (pages.length > 1) {
        const prevPage = pages[pages.length - 2];
        if (prevPage && prevPage.route === 'pages/shopping/list/list') {
          // 如果上一页是购物清单页面，刷新数据
          if (typeof prevPage.loadShoppingList === 'function') {
            prevPage.loadShoppingList();
          }
        }
      }

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      console.error('删除购物项失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    } finally {
      this.setData({ saving: false });
    }
  },

  // 获取分类名称
  getCategoryName(categoryId) {
    const category = this.data.categories.find(cat => cat.id === categoryId);
    return category ? category.name : '请选择';
  },

  // 获取优先级名称和颜色
  getPriorityInfo(priority) {
    const priorityInfo = this.data.priorities.find(p => p.value === priority);
    return priorityInfo || this.data.priorities[0];
  }
});