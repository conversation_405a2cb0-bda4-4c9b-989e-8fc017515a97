// 购物清单添加页面
const app = getApp();

Page({
  data: {
    // 表单数据
    name: '',
    category: 'vegetables', // 默认选择蔬菜分类
    amount: '1',
    unit: '个',
    price: '', // 单价
    priority: 1,
    notes: '',
    
    // 选择器数据
    categories: [
      { id: 'vegetables', name: '蔬菜', icon: '🥬' },
      { id: 'fruits', name: '水果', icon: '🍎' },
      { id: 'meat', name: '肉类', icon: '🥩' },
      { id: 'seafood', name: '海鲜', icon: '🐟' },
      { id: 'dairy', name: '乳制品', icon: '🥛' },
      { id: 'grains', name: '谷物', icon: '🌾' },
      { id: 'condiments', name: '调料', icon: '🧂' },
      { id: 'snacks', name: '零食', icon: '🍿' },
      { id: 'beverages', name: '饮料', icon: '🥤' },
      { id: 'others', name: '其他', icon: '📦' }
    ],
    
    units: ['个', '斤', '克', '千克', '升', '毫升', '包', '袋', '盒', '瓶'],
    
    priorities: [
      { value: 1, name: '低', color: '#28a745' },
      { value: 2, name: '中', color: '#ffc107' },
      { value: 3, name: '高', color: '#dc3545' }
    ],
    
    // 界面状态
    showCategoryPicker: false,
    showUnitPicker: false,
    showPriorityPicker: false,
    categoryIndex: 0,
    unitIndex: 0,
    priorityIndex: 0,
    
    // 页面状态
    loading: false,
    saving: false,
    isEdit: false,
    itemId: null,
    canSave: true // 初始设为true，因为有默认值
  },

  onLoad(options) {
    console.log('购物清单添加页面加载', options);
    
    if (options.id) {
      this.setData({ 
        isEdit: true,
        itemId: options.id 
      });
      this.loadItemDetail(options.id);
    }
    
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: options.id ? '编辑购物项' : '添加购物项'
    });

    // 初始化表单验证
    this.validateForm();
  },

  // 加载购物项详情
  async loadItemDetail(id) {
    try {
      this.setData({ loading: true });
      
      const res = await app.request({
        url: `/shopping/${id}`
      });
      const item = res.data;
      
      // 找到对应的索引
      const categoryIndex = this.data.categories.findIndex(cat => cat.id === item.category_id);
      const unitIndex = this.data.units.findIndex(unit => unit === item.unit);
      const priorityIndex = 0; // 默认优先级

      this.setData({
        name: item.name,
        category: item.category_id,
        amount: item.quantity ? item.quantity.toString() : '1',
        unit: item.unit || '个',
        price: item.price ? item.price.toString() : '',
        priority: 'normal',
        notes: item.notes || '',
        categoryIndex: Math.max(0, categoryIndex),
        unitIndex: Math.max(0, unitIndex),
        priorityIndex: Math.max(0, priorityIndex),
        loading: false
      });
      
    } catch (error) {
      console.error('加载购物项详情失败:', error);
      app.showError('加载详情失败');
      this.setData({ loading: false });
    }
  },

  // 输入处理
  onNameInput(e) {
    this.setData({ name: e.detail.value });
    this.validateForm();
  },

  onAmountInput(e) {
    let value = e.detail.value;

    // 格式化数量输入
    if (value) {
      // 只允许数字和小数点
      value = value.replace(/[^\d.]/g, '');

      // 确保只有一个小数点
      const parts = value.split('.');
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
      }

      // 限制小数位数为2位
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }

      // 确保数值合理
      const numValue = parseFloat(value);
      if (numValue > 9999) {
        value = '9999';
      }
    }

    this.setData({ amount: value });
    this.validateForm();
  },

  // 价格输入
  onPriceInput(e) {
    let value = e.detail.value;

    // 格式化价格输入
    if (value) {
      // 只允许数字和小数点
      value = value.replace(/[^\d.]/g, '');

      // 确保只有一个小数点
      const parts = value.split('.');
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
      }

      // 限制小数位数为2位
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }

      // 确保数值合理
      const numValue = parseFloat(value);
      if (numValue > 99999) {
        value = '99999';
      }
    }

    this.setData({ price: value });
    this.validateForm();
  },

  onNotesInput(e) {
    this.setData({ notes: e.detail.value });
  },

  // 表单验证
  validateForm() {
    const { name, category, amount, unit } = this.data;
    const canSave = name.trim() && category && amount && parseFloat(amount) > 0 && unit;
    
    // 设置表单是否可以保存的状态
    this.setData({ canSave });
    
    return canSave;
  },

  // 计算总价
  getTotalPrice() {
    const { amount, price } = this.data;
    const quantity = parseFloat(amount) || 0;
    const unitPrice = parseFloat(price) || 0;
    return (quantity * unitPrice).toFixed(2);
  },

  // 显示分类选择器
  showCategoryPicker() {
    this.setData({ showCategoryPicker: true });
  },

  // 分类选择
  onCategoryChange(e) {
    let index;
    if (e.detail && e.detail.value) {
      // picker-view的change事件
      index = Array.isArray(e.detail.value) ? e.detail.value[0] : e.detail.value;
    } else if (e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.value !== undefined) {
      // 确定按钮的点击事件
      index = this.data.categoryIndex;
    } else {
      return;
    }

    const category = this.data.categories[index];
    if (category) {
      this.setData({
        categoryIndex: index,
        category: category.id,
        showCategoryPicker: false
      });
    }
  },

  // 分类选择器值变化
  onCategoryPickerChange(e) {
    const index = e.detail.value[0];
    this.setData({
      categoryIndex: index
    });
  },

  // 取消分类选择
  onCategoryCancel() {
    this.setData({ showCategoryPicker: false });
  },

  // 显示单位选择器
  showUnitPicker() {
    console.log('显示单位选择器');
    this.setData({ showUnitPicker: true });
  },

  // 单位选择
  onUnitChange(e) {
    console.log('单位选择', e);
    let index;
    if (e.detail && e.detail.value) {
      // picker-view的change事件
      index = Array.isArray(e.detail.value) ? e.detail.value[0] : e.detail.value;
    } else if (e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.value !== undefined) {
      // 确定按钮的点击事件
      index = this.data.unitIndex;
    } else {
      return;
    }

    const unit = this.data.units[index];
    if (unit) {
      console.log('选择的单位:', unit);
      this.setData({
        unitIndex: index,
        unit: unit,
        showUnitPicker: false
      });
      this.validateForm();
    }
  },

  // 单位选择器值变化
  onUnitPickerChange(e) {
    const index = e.detail.value[0];
    this.setData({
      unitIndex: index
    });
  },

  // 取消单位选择
  onUnitCancel() {
    this.setData({ showUnitPicker: false });
  },

  // 显示优先级选择器
  showPriorityPicker() {
    this.setData({ showPriorityPicker: true });
  },

  // 优先级选择
  onPriorityChange(e) {
    const index = e.currentTarget.dataset.index;
    if (index === undefined || index === null) {
      console.error('优先级选择: 无效的索引');
      return;
    }

    const priority = this.data.priorities[index];
    if (priority) {
      this.setData({
        priorityIndex: index,
        priority: priority.value
      });
    }
  },

  // 取消优先级选择
  onPriorityCancel() {
    this.setData({ showPriorityPicker: false });
  },



  // 保存购物项
  async saveItem() {
    console.log('保存按钮点击');
    
    if (!this.validateForm()) {
      console.log('表单验证失败');
      return;
    }

    try {
      this.setData({ saving: true });
      console.log('开始保存数据', this.data);

      const itemData = {
        name: this.data.name.trim(),
        category_id: this.data.category,
        quantity: parseFloat(this.data.amount),
        unit: this.data.unit,
        price: this.data.price ? parseFloat(this.data.price) : null,
        priority: this.data.priority,
        notes: this.data.notes.trim()
      };
      
      console.log('准备提交的数据:', itemData);

      if (this.data.isEdit) {
        await app.request({
          url: `/shopping/${this.data.itemId}`,
          method: 'PUT',
          data: itemData
        });
        app.showSuccess('购物项已更新');
      } else {
        await app.request({
          url: '/shopping',
          method: 'POST',
          data: itemData
        });
        app.showSuccess('购物项已添加');
      }

      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1000);

    } catch (error) {
      console.error('保存购物项失败:', error);
      app.showError('保存失败: ' + (error.message || '未知错误'));
    } finally {
      this.setData({ saving: false });
    }
  },

  // 取消操作
  cancel() {
    wx.navigateBack();
  },

  // 删除购物项
  async deleteItem() {
    if (!this.data.isEdit || !this.data.itemId) {
      wx.showToast({
        title: '无法删除',
        icon: 'none'
      });
      return;
    }

    const result = await wx.showModal({
      title: '确认删除',
      content: '确定要删除这个购物项吗？',
      confirmText: '删除',
      confirmColor: '#ff4757'
    });

    if (!result.confirm) {
      return;
    }

    try {
      this.setData({ saving: true });

      await app.request({
        url: `/shopping/${this.data.itemId}`,
        method: 'DELETE'
      });

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      console.error('删除购物项失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    } finally {
      this.setData({ saving: false });
    }
  },

  // 获取分类名称
  getCategoryName(categoryId) {
    const category = this.data.categories.find(cat => cat.id === categoryId);
    return category ? category.name : '请选择';
  },

  // 获取优先级名称和颜色
  getPriorityInfo(priority) {
    const priorityInfo = this.data.priorities.find(p => p.value === priority);
    return priorityInfo || this.data.priorities[0];
  }
});