// 食材列表页面逻辑
const app = getApp();

// 内联图片工具函数
function getDefaultIngredientImage(category, index) {
  const categoryDefaults = {
    '蔬菜': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop&crop=center',
    '水果': 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=200&h=200&fit=crop&crop=center',
    '肉类': 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=200&h=200&fit=crop&crop=center',
    '海鲜': 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=200&h=200&fit=crop&crop=center',
    '蛋奶': 'https://images.unsplash.com/photo-1563636619-e9143da7973b?w=200&h=200&fit=crop&crop=center',
    '调料': 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=200&h=200&fit=crop&crop=center',
    '零食': 'https://images.unsplash.com/photo-1621939514649-280e2ee25f60?w=200&h=200&fit=crop&crop=center',
    '饮品': 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=200&h=200&fit=crop&crop=center',
    '其他': 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=200&h=200&fit=crop&crop=center'
  };

  const defaultIngredients = [
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1542838132-92c53300491e?w=200&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=200&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=200&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=200&h=200&fit=crop&crop=center'
  ];

  // 如果有分类对应的默认图片，使用分类图片
  if (category && categoryDefaults[category]) {
    return categoryDefaults[category];
  }

  // 否则从默认图片池中随机选择
  const imageIndex = (index || 0) % defaultIngredients.length;
  return defaultIngredients[imageIndex];
}

function getFallbackImage(index) {
  const defaultIngredients = [
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1542838132-92c53300491e?w=200&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=200&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=200&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=200&h=200&fit=crop&crop=center'
  ];

  // 使用不同的图片作为备用
  const fallbackIndex = ((index || 0) + 1) % defaultIngredients.length;
  return defaultIngredients[fallbackIndex];
}

Page({
  data: {
    ingredients: [],
    categories: ['蔬菜', '水果', '肉类', '海鲜', '蛋奶', '调料', '零食', '饮品', '其他'],
    currentCategory: 'all',
    searchKeyword: '',
    sortBy: 'expire_date', // expire_date, created_at, name
    sortOrder: 'asc', // asc, desc
    loading: false,
    refreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    showFilter: false,
    filterStatus: 'all', // all, fresh, expiring, expired

    // 统计数据
    freshCount: 0,
    expiringCount: 0,
    expiredCount: 0
  },

  onLoad() {
    console.log('食材列表页面加载');
    this.checkLoginAndLoad();
  },

  onShow() {
    console.log('食材列表页面显示');
    if (app.globalData.token) {
      this.loadIngredients(true);
    }
  },

  onPullDownRefresh() {
    console.log('下拉刷新');
    this.setData({ refreshing: true });
    this.loadIngredients(true).finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    console.log('触底加载');
    if (this.data.hasMore && !this.data.loading) {
      this.loadIngredients(false);
    }
  },

  // 工具方法
  getCategoryIcon(category) {
    const icons = {
      '蔬菜': '🥬',
      '水果': '🍎',
      '肉类': '🥩',
      '海鲜': '🦐',
      '蛋奶': '🥛',
      '调料': '🧂',
      '零食': '🍿',
      '饮品': '🥤',
      '其他': '📦'
    };
    return icons[category] || '📦';
  },

  // 计算统计数据
  calculateStats() {
    const { ingredients } = this.data;
    let freshCount = 0;
    let expiringCount = 0;
    let expiredCount = 0;

    ingredients.forEach(item => {
      switch (item.statusInfo?.status) {
        case 'fresh':
          freshCount++;
          break;
        case 'expiring':
          expiringCount++;
          break;
        case 'expired':
          expiredCount++;
          break;
      }
    });

    this.setData({
      freshCount,
      expiringCount,
      expiredCount
    });
  },



  // 重置筛选器
  resetFilters() {
    this.setData({
      currentCategory: 'all',
      filterStatus: 'all',
      sortBy: 'expire_date',
      sortOrder: 'asc',
      searchKeyword: ''
    });
    this.loadIngredients(true);
  },

  // 应用筛选器
  applyFilters() {
    this.setData({ showFilter: false });
    this.loadIngredients(true);
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 检查登录状态并加载数据
  checkLoginAndLoad() {
    if (!app.globalData.token) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }
      });
      return;
    }
    this.loadIngredients(true);
  },

  // 加载食材列表
  async loadIngredients(reset = false) {
    if (this.data.loading) return;

    const page = reset ? 1 : this.data.page;
    this.setData({ loading: true });

    try {
      const params = {
        page,
        limit: this.data.pageSize,
        category_id: this.data.currentCategory === 'all' ? '' : this.data.currentCategory,
        search: this.data.searchKeyword,
        sort: this.data.sortBy,
        order: this.data.sortOrder,
        status: this.data.filterStatus === 'all' ? '' : this.data.filterStatus
      };

      const res = await app.request({
        url: '/ingredients',
        data: params
      });

      const newIngredients = res.data.ingredients.map((item, index) => ({
        ...item,
        statusInfo: app.getIngredientStatus(item.expire_date),
        // 如果没有图片，设置默认图片
        image_url: item.image_url || getDefaultIngredientImage(item.category_name, index)
      }));

      this.setData({
        ingredients: reset ? newIngredients : [...this.data.ingredients, ...newIngredients],
        hasMore: res.data.has_more,
        page: page + 1
      });

      // 计算统计数据
      this.calculateStats();

    } catch (error) {
      console.error('加载食材列表失败:', error);
      app.showError('加载失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  onSearch() {
    this.loadIngredients(true);
  },

  // 清空搜索
  onSearchClear() {
    this.setData({
      searchKeyword: ''
    });
    this.loadIngredients(true);
  },

  // 切换分类
  onCategoryChange(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      currentCategory: category
    });
    this.loadIngredients(true);
  },

  // 显示/隐藏筛选器
  toggleFilter() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 切换排序方式
  onSortChange(e) {
    const sortBy = e.currentTarget.dataset.sort;
    const sortOrder = this.data.sortBy === sortBy && this.data.sortOrder === 'asc' ? 'desc' : 'asc';
    
    this.setData({
      sortBy,
      sortOrder
    });
    this.loadIngredients(true);
  },

  // 切换状态筛选
  onStatusFilterChange(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      filterStatus: status,
      showFilter: false
    });
    this.loadIngredients(true);
  },

  // 查看食材详情
  viewIngredientDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/ingredients/detail/detail?id=${id}`
    });
  },

  // 编辑食材
  editIngredient(e) {
    e.stopPropagation();
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/ingredients/add/add?id=${id}`
    });
  },

  // 删除食材
  async deleteIngredient(e) {
    e.stopPropagation();
    const id = e.currentTarget.dataset.id;
    const name = e.currentTarget.dataset.name;

    const confirmed = await app.showConfirm({
      title: '确认删除',
      content: `确定要删除食材"${name}"吗？`
    });

    if (!confirmed) return;

    try {
      await app.request({
        url: `/ingredients/${id}`,
        method: 'DELETE'
      });

      app.showSuccess('删除成功');
      this.loadIngredients(true);
    } catch (error) {
      console.error('删除食材失败:', error);
      app.showError('删除失败');
    }
  },

  // 添加食材
  addIngredient() {
    wx.navigateTo({
      url: '/pages/ingredients/add/add'
    });
  },

  // 批量操作
  onBatchAction(e) {
    const action = e.currentTarget.dataset.action;
    
    switch (action) {
      case 'delete_expired':
        this.deleteExpiredIngredients();
        break;
      case 'export':
        this.exportIngredients();
        break;
      default:
        break;
    }
  },

  // 删除过期食材
  async deleteExpiredIngredients() {
    const confirmed = await app.showConfirm({
      title: '批量删除',
      content: '确定要删除所有过期食材吗？'
    });

    if (!confirmed) return;

    try {
      await app.request({
        url: '/ingredients/batch/delete-expired',
        method: 'POST'
      });

      app.showSuccess('删除成功');
      this.loadIngredients(true);
    } catch (error) {
      console.error('批量删除失败:', error);
      app.showError('删除失败');
    }
  },

  // 导出食材列表
  exportIngredients() {
    app.showError('导出功能开发中');
  },

  // 图片加载错误处理
  onImageError(e) {
    const index = e.currentTarget.dataset.index;
    if (index !== undefined) {
      const ingredients = this.data.ingredients;
      const item = ingredients[index];
      // 使用备用的食材图片
      ingredients[index].image_url = getFallbackImage(index);
      this.setData({ ingredients });

      console.warn(`食材图片加载失败，使用备用图片: ${item.name}`);
    }
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '我的食材清单',
      desc: '智能管理冰箱食材，避免浪费',
      path: '/pages/ingredients/list/list'
    };
  }
});