/* 消费分析页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #FFA726 0%, #FF9800 100%);
  padding: 20rpx 30rpx 30rpx;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.action-icon:active {
  opacity: 0.6;
}

/* 时间范围选择 */
.time-range-tabs {
  display: flex;
  gap: 20rpx;
}

.time-tab {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.time-tab.active {
  background-color: white;
  color: #FFA726;
  font-weight: 600;
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
}

.analysis-content {
  padding: 30rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #FFA726;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 总体统计 */
.overview-section {
  margin-bottom: 30rpx;
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.overview-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.overview-item:active {
  transform: scale(0.95);
}

.overview-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  line-height: 1;
}

.overview-label {
  font-size: 24rpx;
  color: #999;
}

.overview-divider {
  width: 1rpx;
  height: 60rpx;
  background-color: #e9ecef;
  margin: 0 20rpx;
}

/* 图表类型切换 */
.chart-tabs {
  display: flex;
  background-color: white;
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.chart-tab {
  flex: 1;
  text-align: center;
  padding: 15rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.chart-tab.active {
  background-color: #FFA726;
  color: white;
  font-weight: 600;
}

/* 图表容器 */
.chart-section,
.insights-section {
  margin-bottom: 30rpx;
}

.chart-container,
.insights-container {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  gap: 20rpx;
}

.empty-icon {
  width: 80rpx;
  height: 80rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 分类支出 */
.category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.category-item:last-child {
  border-bottom: none;
}

.category-item:active {
  background-color: #f8f9fa;
  margin: 0 -30rpx;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.category-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 12rpx;
  background-color: #f8f9fa;
  padding: 8rpx;
  box-sizing: border-box;
}

.category-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.category-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.category-amount {
  font-size: 24rpx;
  color: #FFA726;
  font-weight: 600;
}

.category-progress {
  display: flex;
  align-items: center;
  gap: 15rpx;
  min-width: 150rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background-color: #e9ecef;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #FFA726;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  min-width: 50rpx;
  text-align: right;
}

/* 趋势分析 */
.trend-chart {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 250rpx;
  padding: 20rpx 0;
  gap: 10rpx;
}

.trend-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  height: 100%;
}

.trend-bar {
  flex: 1;
  width: 30rpx;
  background-color: #f0f0f0;
  border-radius: 15rpx;
  display: flex;
  align-items: end;
  overflow: hidden;
}

.trend-fill {
  width: 100%;
  background-color: #FFA726;
  border-radius: 15rpx;
  transition: height 0.5s ease;
  min-height: 10rpx;
}

.trend-label {
  font-size: 22rpx;
  color: #999;
}

.trend-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 600;
}

.trend-change {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.trend-icon {
  width: 16rpx;
  height: 16rpx;
}

/* 支出排行 */
.expense-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.expense-item:last-child {
  border-bottom: none;
}

.expense-item:active {
  background-color: #f8f9fa;
  margin: 0 -30rpx;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
}

.expense-rank {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #FFA726;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.expense-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.expense-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.expense-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.expense-meta {
  display: flex;
  gap: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.expense-amount {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  flex-shrink: 0;
}

.amount-number {
  font-size: 32rpx;
  font-weight: 700;
  color: #FFA726;
}

.amount-label {
  font-size: 22rpx;
  color: #999;
}

/* 节省分析 */
.savings-overview {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #E8F5E8 0%, #F0F8F0 100%);
  border-radius: 16rpx;
}

.savings-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.savings-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #28a745;
  line-height: 1;
}

.savings-label {
  font-size: 24rpx;
  color: #666;
}

.savings-divider {
  width: 1rpx;
  height: 60rpx;
  background-color: #d4edda;
  margin: 0 20rpx;
}

/* 建议列表 */
.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 25rpx;
  border-radius: 16rpx;
  border-left: 6rpx solid #e9ecef;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.suggestion-item.warning {
  border-left-color: #ffc107;
  background-color: #fff8e1;
}

.suggestion-item.success {
  border-left-color: #28a745;
  background-color: #e8f5e8;
}

.suggestion-item.info {
  border-left-color: #17a2b8;
  background-color: #e1f5fe;
}

.suggestion-item:active {
  transform: scale(0.98);
}

.suggestion-icon {
  width: 40rpx;
  height: 40rpx;
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.suggestion-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.suggestion-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.suggestion-savings {
  font-size: 24rpx;
  color: #28a745;
  font-weight: 600;
}

.suggestion-action {
  padding: 12rpx 20rpx;
  background-color: #FFA726;
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.suggestion-action:active {
  background-color: #FF9800;
  transform: scale(0.95);
}

/* 消费洞察 */
.insight-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.insight-item:last-child {
  border-bottom: none;
}

.insight-item:active {
  background-color: #f8f9fa;
  margin: 0 -30rpx;
  padding: 25rpx 30rpx;
  border-radius: 12rpx;
}

.insight-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 12rpx;
  background-color: #fff3e0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.insight-icon image {
  width: 30rpx;
  height: 30rpx;
}

.insight-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.insight-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.insight-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .analysis-content {
    padding: 20rpx;
  }
  
  .overview-card,
  .chart-container,
  .insights-container {
    padding: 25rpx;
  }
  
  .overview-number,
  .savings-number {
    font-size: 42rpx;
  }
  
  .overview-divider,
  .savings-divider {
    margin: 0 15rpx;
  }
  
  .trend-chart {
    height: 220rpx;
  }
  
  .page-header {
    padding: 15rpx 20rpx 25rpx;
  }
  
  .header-title {
    font-size: 32rpx;
  }
  
  .time-range-tabs {
    gap: 15rpx;
  }
  
  .time-tab {
    padding: 10rpx 20rpx;
    font-size: 24rpx;
  }
  
  .expense-item {
    gap: 15rpx;
  }
  
  .expense-image {
    width: 70rpx;
    height: 70rpx;
  }
  
  .expense-meta {
    gap: 15rpx;
  }
  
  .savings-overview {
    padding: 25rpx;
  }
  
  .suggestion-item {
    padding: 20rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .overview-card,
  .chart-container,
  .insights-container {
    background-color: #2d2d2d;
  }
  
  .chart-tabs {
    background-color: #2d2d2d;
  }
  
  .chart-tab {
    color: #ccc;
  }
  
  .chart-tab.active {
    background-color: #FFA726;
    color: white;
  }
  
  .overview-number,
  .section-title,
  .category-name,
  .expense-name,
  .trend-value,
  .insight-title,
  .suggestion-title {
    color: white;
  }
  
  .overview-label,
  .progress-text,
  .trend-label,
  .expense-meta,
  .amount-label,
  .savings-label,
  .insight-desc,
  .suggestion-desc,
  .empty-text {
    color: #ccc;
  }
  
  .overview-divider,
  .savings-divider {
    background-color: #404040;
  }
  
  .progress-bar,
  .trend-bar {
    background-color: #404040;
  }
  
  .category-item:active,
  .expense-item:active,
  .insight-item:active {
    background-color: #404040;
  }
  
  .category-icon,
  .insight-icon {
    background-color: #404040;
  }
  
  .savings-overview {
    background: linear-gradient(135deg, #2d4a2d 0%, #3d5a3d 100%);
  }
  
  .suggestion-item {
    background-color: #404040;
  }
  
  .suggestion-item.warning {
    background-color: #4a3d2d;
  }
  
  .suggestion-item.success {
    background-color: #2d4a2d;
  }
  
  .suggestion-item.info {
    background-color: #2d3d4a;
  }
}

/* 动画效果 */
.overview-card,
.chart-container,
.insights-container {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chart-tab {
  animation: fadeInScale 0.3s ease;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.category-item,
.expense-item,
.insight-item {
  animation: slideInLeft 0.3s ease;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.trend-item {
  animation: slideInUp 0.5s ease;
}

.suggestion-item {
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 进度条动画 */
.progress-fill,
.trend-fill {
  animation: progressFill 1s ease-out;
}

@keyframes progressFill {
  from {
    width: 0;
    height: 0;
  }
}

/* 数字计数动画 */
.overview-number,
.savings-number,
.amount-number,
.trend-value {
  animation: countUp 0.8s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 排名徽章动画 */
.expense-rank {
  animation: rankBadge 0.5s ease-out;
}

@keyframes rankBadge {
  0% {
    opacity: 0;
    transform: scale(0) rotate(-180deg);
  }
  50% {
    transform: scale(1.2) rotate(-90deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* 洞察图标动画 */
.insight-icon {
  animation: iconPulse 0.6s ease-out;
}

@keyframes iconPulse {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 建议项动画 */
.suggestion-item {
  animation: suggestionSlide 0.4s ease-out;
}

@keyframes suggestionSlide {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 节省金额高亮动画 */
.savings-number {
  animation: savingsHighlight 1.5s ease-in-out infinite alternate;
}

@keyframes savingsHighlight {
  from {
    color: #28a745;
  }
  to {
    color: #20c997;
  }
}