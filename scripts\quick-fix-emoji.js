// 快速修复emoji图标显示问题
const fs = require('fs');
const path = require('path');

// 需要修复的emoji
const EMOJIS = ['⋯', '📅', '🛒', '📝', '⏰', '🔔', '📋', '📜', '✅', '⚠️', '🤍', '❤️', '📤', '✏️'];

// 处理目录
const DIRS = ['miniprogram/pages', 'miniprogram/components'];

function processDir(dir) {
  if (!fs.existsSync(dir)) {
    console.log(`目录不存在: ${dir}`);
    return;
  }
  
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      processDir(filePath);
    } else if (file.endsWith('.wxml')) {
      processFile(filePath);
    }
  });
}

function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    EMOJIS.forEach(emoji => {
      // 查找 <image src="emoji" 模式
      const pattern1 = `<image src="${emoji}"`;
      const pattern2 = `<image class="[^"]*" src="${emoji}"`;
      const pattern3 = `<image mode="[^"]*" src="${emoji}"`;
      
      if (content.includes(pattern1)) {
        // 简单替换所有包含emoji的image标签
        const regex = new RegExp(`<image[^>]*src="${emoji.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"[^>]*>`, 'g');
        content = content.replace(regex, `<text class="emoji-icon">${emoji}</text>`);
        modified = true;
        console.log(`修复 ${filePath}: ${emoji}`);
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
    }
  } catch (error) {
    console.error(`处理文件失败: ${filePath}`, error.message);
  }
}

console.log('🔧 开始快速修复emoji图标...\n');

DIRS.forEach(dir => {
  console.log(`处理目录: ${dir}`);
  processDir(dir);
});

console.log('\n✅ 修复完成！');
console.log('\n💡 接下来需要:');
console.log('1. 重新编译小程序');
console.log('2. 检查页面显示效果');
console.log('3. 确认不再有图片加载错误');
