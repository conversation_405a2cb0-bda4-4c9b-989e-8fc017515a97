// 食材分析页面
const app = getApp();

Page({
  data: {
    // 统计数据
    stats: {
      totalCount: 0,
      categoryStats: [],
      statusStats: {
        fresh: 0,
        expiring: 0,
        expired: 0
      },
      storageStats: [],
      monthlyTrend: []
    },
    
    // 页面状态
    loading: false,
    refreshing: false,
    
    // 时间范围
    timeRange: 'month',
    timeRanges: [
      { id: 'week', name: '本周' },
      { id: 'month', name: '本月' },
      { id: 'quarter', name: '本季度' },
      { id: 'year', name: '本年' }
    ],
    
    // 图表类型
    chartType: 'category',
    chartTypes: [
      { id: 'category', name: '分类统计' },
      { id: 'status', name: '状态分布' },
      { id: 'storage', name: '存储位置' },
      { id: 'trend', name: '趋势分析' }
    ]
  },

  onLoad() {
    console.log('食材分析页面加载');
    this.loadAnalysisData();
  },

  onShow() {
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  // 加载分析数据
  async loadAnalysisData() {
    try {
      this.setData({ loading: true });

      const params = {
        timeRange: this.data.timeRange
      };

      const result = await app.api.getIngredientsAnalysis(params);
      
      this.setData({
        stats: result,
        loading: false
      });

    } catch (error) {
      console.error('加载食材分析数据失败:', error);
      app.showError('加载失败');
      this.setData({ loading: false });
    }
  },

  // 刷新数据
  async refreshData() {
    try {
      this.setData({ refreshing: true });

      const params = {
        timeRange: this.data.timeRange
      };

      const result = await app.api.getIngredientsAnalysis(params);
      
      this.setData({
        stats: result,
        refreshing: false
      });

      wx.stopPullDownRefresh();

    } catch (error) {
      console.error('刷新数据失败:', error);
      app.showError('刷新失败');
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  // 切换时间范围
  switchTimeRange(e) {
    const timeRange = e.currentTarget.dataset.range;
    if (timeRange === this.data.timeRange) return;

    this.setData({ timeRange });
    this.loadAnalysisData();
  },

  // 切换图表类型
  switchChartType(e) {
    const chartType = e.currentTarget.dataset.type;
    this.setData({ chartType });
  },

  // 查看分类详情
  viewCategoryDetail(e) {
    const category = e.currentTarget.dataset.category;
    wx.navigateTo({
      url: `/pages/ingredients/list/list?category=${category}`
    });
  },

  // 查看过期食材
  viewExpiringIngredients() {
    wx.navigateTo({
      url: '/pages/ingredients/list/list?filter=expiring'
    });
  },

  // 查看已过期食材
  viewExpiredIngredients() {
    wx.navigateTo({
      url: '/pages/ingredients/list/list?filter=expired'
    });
  },

  // 查看存储位置详情
  viewStorageDetail(e) {
    const storage = e.currentTarget.dataset.storage;
    wx.navigateTo({
      url: `/pages/ingredients/list/list?storage=${storage}`
    });
  },

  // 获取状态颜色
  getStatusColor(status) {
    const colors = {
      fresh: '#28a745',
      expiring: '#ffc107',
      expired: '#dc3545'
    };
    return colors[status] || '#6c757d';
  },

  // 获取状态名称
  getStatusName(status) {
    const names = {
      fresh: '新鲜',
      expiring: '即将过期',
      expired: '已过期'
    };
    return names[status] || '未知';
  },

  // 计算百分比
  calculatePercentage(value, total) {
    if (total === 0) return 0;
    return Math.round((value / total) * 100);
  }
});