// 精确修复WXML语法错误
const fs = require('fs');
const path = require('path');

// 需要精确修复的文件和问题
const PRECISE_FIXES = [
  {
    file: 'miniprogram/pages/ingredients/add/add.wxml',
    fixes: [
      {
        search: '{{nameIndex === -1 ? \'请选择食材名称\' : commonNames[nameIndex]}}\n</text>\n          </text>',
        replace: '{{nameIndex === -1 ? \'请选择食材名称\' : commonNames[nameIndex]}}\n          </text>'
      },
      {
        search: '{{storageIndex === -1 ? \'请选择存储位置\' : storageLocations[storageIndex]}}\n</text>\n          </text>',
        replace: '{{storageIndex === -1 ? \'请选择存储位置\' : storageLocations[storageIndex]}}\n          </text>'
      },
      {
        search: '{{formData.purchaseDate || \'请选择购买日期\'}}\n</text>\n          </text>',
        replace: '{{formData.purchaseDate || \'请选择购买日期\'}}\n          </text>'
      },
      {
        search: '{{formData.expiryDate || \'请选择保质期\'}}\n</text>\n          </text>',
        replace: '{{formData.expiryDate || \'请选择保质期\'}}\n          </text>'
      }
    ]
  },
  {
    file: 'miniprogram/pages/ingredients/detail/detail.wxml',
    fixes: [
      {
        search: '{{ingredient.expire_date}}\n</text>\n        </text>',
        replace: '{{ingredient.expire_date}}\n        </text>'
      }
    ]
  },
  {
    file: 'miniprogram/pages/recipes/list/list.wxml',
    fixes: [
      {
        search: 'wx:for-item="tag"\n              {{tag}}\n</text>       {{tag}}',
        replace: 'wx:for-item="tag"\n            >\n              {{tag}}\n            </text>'
      }
    ]
  },
  {
    file: 'miniprogram/pages/shopping/add/add.wxml',
    fixes: [
      {
        search: '</text>xtarea class="item-textarea"',
        replace: '<textarea class="item-textarea"'
      }
    ]
  }
];

function applyPreciseFixes() {
  console.log('🔧 开始精确修复WXML语法错误...\n');
  
  let totalFiles = 0;
  let fixedFiles = 0;
  let totalFixes = 0;
  
  PRECISE_FIXES.forEach(({ file, fixes }) => {
    totalFiles++;
    
    if (!fs.existsSync(file)) {
      console.log(`❌ 文件不存在: ${file}`);
      return;
    }
    
    console.log(`📁 修复文件: ${file}`);
    
    try {
      let content = fs.readFileSync(file, 'utf8');
      let modified = false;
      
      fixes.forEach((fix, index) => {
        if (content.includes(fix.search)) {
          content = content.replace(fix.search, fix.replace);
          modified = true;
          totalFixes++;
          console.log(`  ✅ 修复 ${index + 1}: 语法错误已修正`);
        } else {
          console.log(`  ℹ️  修复 ${index + 1}: 未找到匹配内容，可能已修复`);
        }
      });
      
      if (modified) {
        fs.writeFileSync(file, content, 'utf8');
        fixedFiles++;
        console.log(`✅ 文件修复完成: ${file}`);
      } else {
        console.log(`ℹ️  文件无需修复: ${file}`);
      }
      
    } catch (error) {
      console.error(`❌ 修复文件失败: ${file}`, error.message);
    }
    
    console.log('');
  });
  
  console.log(`📊 精确修复结果:`);
  console.log(`  总文件数: ${totalFiles}`);
  console.log(`  修复文件数: ${fixedFiles}`);
  console.log(`  修复问题数: ${totalFixes}`);
  
  if (fixedFiles > 0) {
    console.log('\n✅ 精确修复完成！');
  } else {
    console.log('\n✅ 所有文件都已是最新状态');
  }
}

// 验证修复结果
function verifyFixes() {
  console.log('🔍 验证精确修复结果...\n');
  
  const problemPatterns = [
    /<text[^>]*>[^<]*<\/text><\/text>/g,
    /<text[^>]*>[^<]*<\/text><\/image>/g,
    /<text[^>]*>[^<]*<\/text><\/view>/g,
    /wx:for-item="[^"]*"\s*{{[^}]*}}/g
  ];
  
  let totalIssues = 0;
  
  PRECISE_FIXES.forEach(({ file }) => {
    if (!fs.existsSync(file)) return;
    
    try {
      const content = fs.readFileSync(file, 'utf8');
      const relativePath = path.relative(process.cwd(), file);
      
      problemPatterns.forEach((pattern, index) => {
        const matches = content.match(pattern);
        if (matches) {
          console.log(`❌ 仍有问题: ${relativePath}`);
          matches.forEach(match => {
            console.log(`  模式${index + 1}: ${match.substring(0, 100)}...`);
            totalIssues++;
          });
        }
      });
    } catch (error) {
      console.error(`验证失败: ${file}`, error.message);
    }
  });
  
  if (totalIssues === 0) {
    console.log('✅ 验证通过，所有语法错误已修复');
  } else {
    console.log(`⚠️  仍有 ${totalIssues} 个问题需要手动检查`);
  }
}

// 命令行参数处理
const command = process.argv[2];

if (command === 'verify') {
  verifyFixes();
} else {
  applyPreciseFixes();
}

module.exports = { applyPreciseFixes, verifyFixes, PRECISE_FIXES };
