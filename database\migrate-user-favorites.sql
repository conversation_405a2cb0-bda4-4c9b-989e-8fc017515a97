-- 用户收藏表结构迁移脚本
-- 添加缺失的字段以支持通用收藏功能

USE smart_fridge;

-- 备份现有数据
CREATE TABLE IF NOT EXISTS `user_favorites_backup` AS SELECT * FROM `user_favorites`;

-- 安全地添加新字段（如果不存在）
SET @sql = '';

-- 检查并添加type字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'smart_fridge' AND table_name = 'user_favorites' AND column_name = 'type';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_favorites ADD COLUMN `type` varchar(20) NOT NULL DEFAULT ''recipe'' COMMENT ''收藏类型'' AFTER `user_id`;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 检查并添加target_id字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'smart_fridge' AND table_name = 'user_favorites' AND column_name = 'target_id';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_favorites ADD COLUMN `target_id` int(11) NOT NULL DEFAULT 0 COMMENT ''目标ID'' AFTER `type`;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 检查并添加title字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'smart_fridge' AND table_name = 'user_favorites' AND column_name = 'title';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_favorites ADD COLUMN `title` varchar(255) NULL COMMENT ''收藏标题'' AFTER `recipe_id`;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 检查并添加description字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'smart_fridge' AND table_name = 'user_favorites' AND column_name = 'description';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_favorites ADD COLUMN `description` text NULL COMMENT ''收藏描述'' AFTER `title`;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 检查并添加image_url字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'smart_fridge' AND table_name = 'user_favorites' AND column_name = 'image_url';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_favorites ADD COLUMN `image_url` varchar(500) NULL COMMENT ''收藏图片URL'' AFTER `description`;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 检查并添加tags字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'smart_fridge' AND table_name = 'user_favorites' AND column_name = 'tags';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_favorites ADD COLUMN `tags` json NULL COMMENT ''标签'' AFTER `image_url`;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 检查并添加notes字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'smart_fridge' AND table_name = 'user_favorites' AND column_name = 'notes';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_favorites ADD COLUMN `notes` text NULL COMMENT ''用户备注'' AFTER `tags`;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 检查并添加is_deleted字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'smart_fridge' AND table_name = 'user_favorites' AND column_name = 'is_deleted';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_favorites ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT ''是否删除'' AFTER `notes`;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 检查并添加updated_at字段
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'smart_fridge' AND table_name = 'user_favorites' AND column_name = 'updated_at';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_favorites ADD COLUMN `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'' AFTER `created_at`;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 更新现有数据，将recipe_id复制到target_id
UPDATE `user_favorites` SET `target_id` = `recipe_id` WHERE `recipe_id` IS NOT NULL;

-- 从recipes表获取标题和描述信息
UPDATE `user_favorites` uf
JOIN `recipes` r ON uf.recipe_id = r.id
SET
  uf.title = r.name,
  uf.description = r.description
WHERE uf.type = 'recipe' AND uf.recipe_id IS NOT NULL;

-- 删除旧的唯一索引
DROP INDEX `uk_user_recipe` ON `user_favorites`;

-- 添加新的索引
ALTER TABLE `user_favorites`
ADD UNIQUE KEY `uk_user_type_target` (`user_id`, `type`, `target_id`, `is_deleted`),
ADD KEY `idx_type` (`type`),
ADD KEY `idx_target_id` (`target_id`),
ADD KEY `idx_is_deleted` (`is_deleted`);

-- 验证迁移结果
SELECT 
  COUNT(*) as total_records,
  COUNT(CASE WHEN type = 'recipe' THEN 1 END) as recipe_favorites,
  COUNT(CASE WHEN title IS NOT NULL THEN 1 END) as records_with_title,
  COUNT(CASE WHEN is_deleted = 0 THEN 1 END) as active_records
FROM `user_favorites`;

-- 显示表结构
DESCRIBE `user_favorites`;
