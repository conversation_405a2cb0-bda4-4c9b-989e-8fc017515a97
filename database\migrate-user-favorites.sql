-- 用户收藏表结构迁移脚本
-- 添加缺失的字段以支持通用收藏功能

USE fridge_db;

-- 备份现有数据
CREATE TABLE IF NOT EXISTS `user_favorites_backup` AS SELECT * FROM `user_favorites`;

-- 添加新字段
ALTER TABLE `user_favorites` 
ADD COLUMN `type` varchar(20) NOT NULL DEFAULT 'recipe' COMMENT '收藏类型：recipe-菜谱, ingredient-食材, other-其他' AFTER `user_id`,
ADD COLUMN `target_id` int(11) NOT NULL DEFAULT 0 COMMENT '目标ID（菜谱ID或食材ID等）' AFTER `type`,
ADD COLUMN `title` varchar(255) NULL COMMENT '收藏标题' AFTER `recipe_id`,
ADD COLUMN `description` text NULL COMMENT '收藏描述' AFTER `title`,
ADD COLUMN `image_url` varchar(500) NULL COMMENT '收藏图片URL' AFTER `description`,
ADD COLUMN `tags` json NULL COMMENT '标签（JSON格式）' AFTER `image_url`,
ADD COLUMN `notes` text NULL COMMENT '用户备注' AFTER `tags`,
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除' AFTER `notes`,
ADD COLUMN `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `created_at`;

-- 更新现有数据，将recipe_id复制到target_id
UPDATE `user_favorites` SET `target_id` = `recipe_id` WHERE `recipe_id` IS NOT NULL;

-- 从recipes表获取标题、描述和图片信息
UPDATE `user_favorites` uf 
JOIN `recipes` r ON uf.recipe_id = r.id 
SET 
  uf.title = r.name,
  uf.description = r.description,
  uf.image_url = r.image_url
WHERE uf.type = 'recipe' AND uf.recipe_id IS NOT NULL;

-- 删除旧的唯一索引
DROP INDEX `uk_user_recipe` ON `user_favorites`;

-- 添加新的索引
ALTER TABLE `user_favorites`
ADD UNIQUE KEY `uk_user_type_target` (`user_id`, `type`, `target_id`, `is_deleted`),
ADD KEY `idx_type` (`type`),
ADD KEY `idx_target_id` (`target_id`),
ADD KEY `idx_is_deleted` (`is_deleted`);

-- 验证迁移结果
SELECT 
  COUNT(*) as total_records,
  COUNT(CASE WHEN type = 'recipe' THEN 1 END) as recipe_favorites,
  COUNT(CASE WHEN title IS NOT NULL THEN 1 END) as records_with_title,
  COUNT(CASE WHEN is_deleted = 0 THEN 1 END) as active_records
FROM `user_favorites`;

-- 显示表结构
DESCRIBE `user_favorites`;
