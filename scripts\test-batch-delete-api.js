// 测试批量删除过期食材API
const axios = require('axios');

const API_BASE = 'http://127.0.0.1:3000';

async function testBatchDeleteExpiredAPI() {
  try {
    console.log('🧪 测试批量删除过期食材API...\n');
    
    // 测试API端点
    const endpoint = `${API_BASE}/api/ingredients/batch/delete-expired`;
    console.log(`📡 测试端点: ${endpoint}`);
    
    // 模拟请求（注意：需要有效的认证token）
    const testData = {};
    
    console.log('📤 发送请求数据:', testData);
    
    try {
      const response = await axios.post(endpoint, testData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test_token' // 实际使用时需要有效token
        },
        timeout: 5000
      });
      
      console.log('✅ API响应成功');
      console.log('状态码:', response.status);
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
      
    } catch (error) {
      if (error.response) {
        console.log('📥 API响应信息:');
        console.log('状态码:', error.response.status);
        console.log('响应数据:', JSON.stringify(error.response.data, null, 2));
        
        if (error.response.status === 401) {
          console.log('⚠️  认证失败 - 这是预期的，因为使用了测试token');
        } else if (error.response.status === 404) {
          console.log('❌ API端点不存在 - 需要检查路由配置');
        } else {
          console.log('⚠️  其他错误 - 需要进一步检查');
        }
      } else if (error.code === 'ECONNREFUSED') {
        console.log('❌ 连接被拒绝 - 请确保服务器正在运行');
      } else {
        console.log('❌ 请求失败:', error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 测试路由配置
function testRouteConfiguration() {
  console.log('\n🔧 路由配置验证:\n');
  
  const expectedRoutes = [
    'GET /api/ingredients - 获取食材列表',
    'GET /api/ingredients/:id - 获取食材详情',
    'POST /api/ingredients - 添加食材',
    'PUT /api/ingredients/:id - 更新食材',
    'DELETE /api/ingredients/:id - 删除食材',
    'POST /api/ingredients/batch - 批量操作食材',
    'POST /api/ingredients/batch/delete-expired - 批量删除过期食材 ⭐ 新增',
    'POST /api/ingredients/:id/image - 上传食材图片',
    'GET /api/ingredients/categories/list - 获取食材分类',
    'GET /api/ingredients/common/list - 获取常用食材',
    'POST /api/ingredients/:id/use - 使用食材'
  ];
  
  console.log('📋 预期的ingredients路由:');
  expectedRoutes.forEach((route, index) => {
    console.log(`  ${index + 1}. ${route}`);
  });
  
  console.log('\n💡 新增接口说明:');
  console.log('接口: POST /api/ingredients/batch/delete-expired');
  console.log('功能: 批量删除所有过期的食材');
  console.log('认证: 需要Bearer token');
  console.log('参数: 无需参数，自动查找用户的过期食材');
  console.log('返回: 删除的食材数量和详情');
}

// 测试数据库查询逻辑
function testDatabaseQuery() {
  console.log('\n🗄️  数据库查询逻辑验证:\n');
  
  const sqlQueries = [
    {
      purpose: '查找过期食材',
      sql: `SELECT id, name, expire_date
            FROM user_ingredients 
            WHERE user_id = ? 
              AND is_deleted = 0 
              AND expire_date < CURDATE()`
    },
    {
      purpose: '批量软删除',
      sql: `UPDATE user_ingredients 
            SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP 
            WHERE id IN (?, ?, ...) AND user_id = ?`
    },
    {
      purpose: '记录操作日志',
      sql: `INSERT INTO user_records (user_id, type, content) 
            VALUES (?, 'batch_delete_expired', ?)`
    }
  ];
  
  sqlQueries.forEach((query, index) => {
    console.log(`${index + 1}. ${query.purpose}:`);
    console.log(`   ${query.sql.replace(/\s+/g, ' ').trim()}`);
    console.log('');
  });
}

// 测试错误处理
function testErrorHandling() {
  console.log('⚠️  错误处理场景:\n');
  
  const errorScenarios = [
    {
      scenario: '用户未认证',
      expected: '401 Unauthorized',
      handling: '返回认证失败信息'
    },
    {
      scenario: '没有过期食材',
      expected: '200 OK',
      handling: '返回删除数量为0的成功响应'
    },
    {
      scenario: '数据库连接失败',
      expected: '500 Internal Server Error',
      handling: '返回服务器错误信息'
    },
    {
      scenario: '用户ID无效',
      expected: '500 Internal Server Error',
      handling: '数据库查询失败，返回错误'
    }
  ];
  
  errorScenarios.forEach((scenario, index) => {
    console.log(`${index + 1}. ${scenario.scenario}:`);
    console.log(`   预期: ${scenario.expected}`);
    console.log(`   处理: ${scenario.handling}`);
    console.log('');
  });
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始批量删除过期食材API测试...\n');
  
  testRouteConfiguration();
  testDatabaseQuery();
  testErrorHandling();
  
  console.log('='.repeat(60));
  console.log('📡 实际API测试:');
  console.log('='.repeat(60));
  
  await testBatchDeleteExpiredAPI();
  
  console.log('\n' + '='.repeat(60));
  console.log('💡 测试完成建议:');
  console.log('='.repeat(60));
  console.log('1. 确保服务器正在运行 (npm start)');
  console.log('2. 确保数据库连接正常');
  console.log('3. 使用有效的用户token进行实际测试');
  console.log('4. 在小程序中测试完整的用户流程');
  console.log('5. 检查删除操作是否正确记录到user_records表');
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testBatchDeleteExpiredAPI,
  testRouteConfiguration,
  testDatabaseQuery,
  testErrorHandling
};
