/* 食材添加页面样式 - 重构版 */
page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  /* 确保下拉框不被页面裁剪 */
  overflow-x: hidden;
  overflow-y: visible;
}

.container {
  padding: 0;
  background: transparent;
  min-height: 100vh;
  /* 确保下拉框不被裁剪 */
  overflow: visible;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 80rpx 32rpx 48rpx;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.page-title {
  font-size: 48rpx;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: 16rpx;
  position: relative;
  z-index: 1;
  text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  letter-spacing: 2rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.85);
  position: relative;
  z-index: 1;
  font-weight: 500;
}

/* 表单区块 */
.form-section {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  border-radius: 28rpx;
  margin: 0 24rpx 32rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08),
              0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  overflow: visible;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  /* 确保不会裁剪下拉框 */
  overflow: visible;
}

/* 第一个表单区块（包含下拉框）需要更高的层级 */
.form-section:first-child {
  z-index: 1000;
  /* 确保下拉框不被裁剪 */
  overflow: visible;
}

.form-section:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 16rpx 56rpx rgba(0, 0, 0, 0.12),
              0 8rpx 24rpx rgba(0, 0, 0, 0.06);
}

/* 区块标题 */
.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #2d3748;
  padding: 32rpx 32rpx 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  position: relative;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.section-title::before {
  content: '';
  position: absolute;
  left: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 36rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4rpx;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.section-title {
  padding-left: 60rpx;
}

/* 表单项 */
.form-item {
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  position: relative;
  /* 确保下拉框不被裁剪 */
  overflow: visible;
}

/* 包含下拉框的表单项需要更高的层级 */
.form-item:first-child {
  z-index: 10002;
  overflow: visible;
}

.form-item:last-child {
  border-bottom: none;
}

.form-item:hover {
  background: rgba(102, 126, 234, 0.02);
}

/* 表单标签 */
.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  gap: 8rpx;
}

.label-text {
  font-size: 32rpx;
  color: #2d3748;
  font-weight: 600;
  line-height: 1.4;
}

.required {
  color: #e53e3e;
  font-size: 32rpx;
  font-weight: 700;
}

/* 输入框基础样式 */
.form-input {
  width: 100%;
  height: 96rpx;
  background: rgba(248, 250, 252, 0.9);
  border: 2rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 20rpx;
  padding: 0 28rpx;
  font-size: 32rpx;
  color: #2d3748;
  box-sizing: border-box;
  line-height: 92rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
}

.form-input:focus {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 0 0 8rpx rgba(102, 126, 234, 0.08),
              0 4rpx 16rpx rgba(102, 126, 234, 0.12);
  transform: translateY(-2rpx);
}

.form-input::placeholder {
  color: #a0aec0;
  font-weight: 400;
}

/* 食材名称输入容器 */
.name-input-container {
  display: flex;
  gap: 16rpx;
  position: relative;
  z-index: 10001;
  overflow: visible;
}

/* 食材名称输入框 */
.name-input {
  flex: 2;
  height: 96rpx;
  background: rgba(248, 250, 252, 0.9);
  border: 2rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 20rpx;
  padding: 0 28rpx;
  font-size: 32rpx;
  color: #2d3748;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.name-input:focus {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 0 0 8rpx rgba(102, 126, 234, 0.08),
              0 4rpx 16rpx rgba(102, 126, 234, 0.12);
  transform: translateY(-2rpx);
}

.name-input::placeholder {
  color: #a0aec0;
  font-weight: 400;
}

/* 食材名称选择器 */
.name-picker-wrapper {
  flex: 1;
  height: 96rpx;
  background: rgba(248, 250, 252, 0.9);
  border: 2rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.name-picker-wrapper:active {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 0 0 8rpx rgba(102, 126, 234, 0.08),
              0 4rpx 16rpx rgba(102, 126, 234, 0.12);
  transform: translateY(-2rpx);
}

.name-picker-wrapper .picker-content {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0 28rpx;
  justify-content: space-between;
}

.picker-placeholder {
  font-size: 28rpx;
  color: #a0aec0;
  font-weight: 400;
}

.name-picker-wrapper .picker-arrow {
  position: absolute;
  right: 28rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #718096;
  font-weight: 600;
  transition: all 0.3s ease;
}

.name-picker-wrapper:active .picker-arrow {
  color: #667eea;
  transform: translateY(-50%) scale(1.1);
}

/* 食材名称下拉列表 */
.name-dropdown {
  position: absolute;
  top: calc(100% + 12rpx);
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(24rpx);
  border: 2rpx solid rgba(102, 126, 234, 0.15);
  border-radius: 20rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12),
              0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  z-index: 99999;
  max-height: 480rpx;
  overflow-y: auto;
  display: none;
  animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-16rpx) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.name-dropdown.show {
  display: block !important;
  z-index: 99999 !important;
  position: absolute !important;
}

.dropdown-item {
  padding: 24rpx 28rpx;
  font-size: 32rpx;
  color: #2d3748;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
  transition: all 0.2s ease;
  font-weight: 500;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
  color: #667eea;
}

/* 选择器样式 */
.form-picker {
  width: 100%;
  height: 96rpx;
  background: rgba(248, 250, 252, 0.9);
  border: 2rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.form-picker:active {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 0 0 8rpx rgba(102, 126, 234, 0.08),
              0 4rpx 16rpx rgba(102, 126, 234, 0.12);
  transform: translateY(-2rpx);
}

.picker-content {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0 28rpx 0 28rpx;
  font-size: 32rpx;
  color: #2d3748;
  box-sizing: border-box;
  line-height: 1;
  font-weight: 500;
  padding-right: 72rpx;
}

.picker-content .placeholder {
  color: #a0aec0;
  font-weight: 400;
}

.picker-arrow {
  position: absolute;
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #718096;
  font-weight: 600;
  transition: all 0.3s ease;
}

.form-picker:active .picker-arrow {
  color: #667eea;
  transform: translateY(-50%) scale(1.1);
}

/* 数量输入 */
.quantity-input {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.quantity-btn {
  width: 72rpx;
  height: 72rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 700;
  border: none;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.quantity-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
}

.quantity-btn:disabled {
  background: linear-gradient(135deg, #cbd5e0 0%, #a0aec0 100%);
  color: #718096;
  box-shadow: none;
}

.quantity-value {
  width: 120rpx;
  height: 72rpx;
  background: rgba(247, 250, 252, 0.8);
  border: 2rpx solid rgba(226, 232, 240, 0.8);
  border-radius: 16rpx;
  text-align: center;
  font-size: 30rpx;
  color: #2d3748;
  line-height: 68rpx;
  box-sizing: border-box;
  font-weight: 600;
}

.unit-picker {
  flex: 1;
  height: 72rpx;
  background: rgba(247, 250, 252, 0.8);
  border: 2rpx solid rgba(226, 232, 240, 0.8);
  border-radius: 16rpx;
  transition: all 0.3s ease;
  position: relative;
}

.unit-picker:active {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.95);
}

.unit-display {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  color: #2d3748;
  font-weight: 600;
  padding-right: 48rpx;
}

.unit-display::after {
  content: '▶';
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #718096;
  font-weight: 400;
}

/* 价格输入 */
.price-input {
  display: flex;
  align-items: center;
  background: rgba(247, 250, 252, 0.8);
  border: 2rpx solid rgba(226, 232, 240, 0.8);
  border-radius: 16rpx;
  padding: 0 24rpx;
  height: 88rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.price-input:focus-within {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.currency {
  font-size: 32rpx;
  color: #667eea;
  margin-right: 12rpx;
  line-height: 1;
  font-weight: 700;
}

.price-value {
  flex: 1;
  background: transparent;
  border: none;
  font-size: 30rpx;
  color: #2d3748;
  height: 100%;
  line-height: 1;
  box-sizing: border-box;
  font-weight: 600;
}

.price-value::placeholder {
  color: #a0aec0;
  font-weight: 400;
}

/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 160rpx;
  background: rgba(248, 250, 252, 0.9);
  border: 2rpx solid rgba(226, 232, 240, 0.6);
  border-radius: 20rpx;
  padding: 24rpx 28rpx;
  font-size: 32rpx;
  color: #2d3748;
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  line-height: 1.6;
}

.form-textarea:focus {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 0 0 8rpx rgba(102, 126, 234, 0.08),
              0 4rpx 16rpx rgba(102, 126, 234, 0.12);
}

.form-textarea::placeholder {
  color: #a0aec0;
  font-weight: 400;
}

.textarea-counter {
  text-align: right;
  font-size: 28rpx;
  color: #718096;
  margin-top: 16rpx;
  font-weight: 500;
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 24rpx;
  padding: 48rpx 24rpx 40rpx;
}

.btn {
  flex: 1;
  height: 96rpx;
  border-radius: 20rpx;
  font-size: 34rpx;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:active::before {
  left: 100%;
}

.btn-secondary {
  background: rgba(247, 250, 252, 0.9);
  color: #4a5568;
  border: 2rpx solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10rpx);
}

.btn-secondary:active {
  background: rgba(237, 242, 247, 0.9);
  transform: translateY(2rpx);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
}

.btn-primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.5);
}

.btn-primary:disabled {
  background: linear-gradient(135deg, #cbd5e0 0%, #a0aec0 100%);
  color: #718096;
  box-shadow: none;
}

.btn-primary[loading] {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* 占位符样式 */
.placeholder {
  color: #a0aec0 !important;
  font-weight: 400 !important;
}

/* 隐藏状态 */
.hidden {
  display: none !important;
}

/* 动画效果 */
.form-section {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(48rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .page-header {
    padding: 60rpx 24rpx 40rpx;
  }
  
  .page-title {
    font-size: 44rpx;
  }
  
  .form-section {
    margin: 0 20rpx 28rpx;
    border-radius: 24rpx;
  }
  
  .form-item {
    padding: 28rpx 24rpx;
  }
  
  .section-title {
    padding: 28rpx 24rpx 16rpx;
    padding-left: 56rpx;
    font-size: 30rpx;
  }
  
  .button-group {
    padding: 40rpx 20rpx 48rpx;
    gap: 20rpx;
  }
  
  .btn {
    height: 88rpx;
    font-size: 32rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .form-section {
    background: rgba(26, 32, 44, 0.95);
    border: 1rpx solid rgba(255, 255, 255, 0.08);
  }
  
  .section-title {
    color: #f7fafc;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
  }
  
  .label-text {
    color: #f7fafc;
  }
  
  .form-input,
  .form-picker,
  .quantity-value,
  .unit-picker,
  .price-input,
  .form-textarea {
    background: rgba(45, 55, 72, 0.8);
    border-color: rgba(113, 128, 150, 0.3);
    color: #f7fafc;
  }
  
  .name-dropdown {
    background: rgba(26, 32, 44, 0.98);
    border-color: rgba(102, 126, 234, 0.3);
  }
  
  .dropdown-item {
    color: #f7fafc;
  }
  
  .btn-secondary {
    background: rgba(45, 55, 72, 0.95);
    color: #f7fafc;
    border-color: rgba(113, 128, 150, 0.3);
  }
}

/* 微动画效果 */
.form-item {
  animation: fadeInUp 0.6s ease;
  animation-fill-mode: both;
}

.form-item:nth-child(1) { animation-delay: 0.1s; }
.form-item:nth-child(2) { animation-delay: 0.2s; }
.form-item:nth-child(3) { animation-delay: 0.3s; }
.form-item:nth-child(4) { animation-delay: 0.4s; }
.form-item:nth-child(5) { animation-delay: 0.5s; }

/* 滚动条样式 */
.name-dropdown::-webkit-scrollbar {
  width: 8rpx;
}

.name-dropdown::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.04);
  border-radius: 4rpx;
}

.name-dropdown::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 4rpx;
}

.name-dropdown::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}

/* 下拉框遮挡问题的终极修复 */
.name-input-container .name-dropdown {
  z-index: 999999 !important;
  position: absolute !important;
  top: calc(100% + 12rpx) !important;
  left: 0 !important;
  right: 0 !important;
}

/* 确保所有父容器都不会裁剪下拉框 */
.container,
.form-section,
.form-item,
.name-input-container {
  overflow: visible !important;
}

/* 特殊情况：如果仍然被遮挡，使用fixed定位 */
.name-dropdown.force-top {
  position: fixed !important;
  z-index: 999999 !important;
}