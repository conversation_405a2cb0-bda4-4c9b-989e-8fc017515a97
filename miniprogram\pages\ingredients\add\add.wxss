/* 食材添加/编辑页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.ingredient-form {
  padding: 20rpx;
}

/* 表单区域 */
.form-section {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 图片上传 */
.image-upload {
  padding: 30rpx;
}

.image-preview {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.preview-img {
  width: 100%;
  height: 100%;
}

.image-actions {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
}

.delete-img-btn {
  width: 48rpx;
  height: 48rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-img-btn image {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

.upload-placeholder {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.upload-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 16rpx;
  opacity: 0.6;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

/* 表单组 */
.form-group {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-group:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.required {
  color: #ff4757;
  margin-left: 4rpx;
}

/* 输入框 */
.input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 2rpx solid transparent;
  transition: border-color 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #4CAF50;
  background-color: white;
}

.form-input {
  flex: 1;
  font-size: 28rpx;
  background-color: transparent;
  border: none;
}

.input-actions {
  display: flex;
  gap: 16rpx;
  margin-left: 16rpx;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
}

.action-icon image {
  width: 28rpx;
  height: 28rpx;
}

/* 选择器 */
.picker-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 2rpx solid transparent;
  transition: border-color 0.3s ease;
}

.picker-wrapper:active {
  border-color: #4CAF50;
  background-color: white;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-text.placeholder {
  color: #999;
}

.picker-arrow,
.picker-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 数量输入 */
.quantity-input {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.quantity-field {
  flex: 1;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 2rpx solid transparent;
  transition: border-color 0.3s ease;
}

.quantity-field:focus {
  border-color: #4CAF50;
  background-color: white;
}

.unit-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  min-width: 120rpx;
  justify-content: center;
}

.unit-text {
  font-size: 28rpx;
  color: #333;
}

.unit-text.placeholder {
  color: #999;
}

.unit-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border: 2rpx solid transparent;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #4CAF50;
  background-color: white;
}

/* 操作按钮 */
.form-actions {
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.reset-btn {
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #ddd;
}

.save-btn {
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);
}

/* 自定义选择器 */
.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.picker-mask.show {
  opacity: 1;
  visibility: visible;
}

.custom-picker {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  z-index: 1001;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 60vh;
}

.custom-picker.show {
  transform: translateY(0);
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-cancel,
.picker-confirm {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: 500;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-item {
  padding: 30rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.picker-item:active {
  background-color: #f5f5f5;
}

.picker-item:last-child {
  border-bottom: none;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .form-section {
    margin-bottom: 16rpx;
  }
  
  .section-title {
    padding: 24rpx 24rpx 16rpx;
    font-size: 30rpx;
  }
  
  .form-group {
    padding: 24rpx;
  }
  
  .form-label {
    font-size: 26rpx;
    margin-bottom: 16rpx;
  }
  
  .form-input,
  .picker-text,
  .unit-text {
    font-size: 26rpx;
  }
  
  .form-textarea {
    font-size: 26rpx;
    min-height: 100rpx;
  }
  
  .upload-placeholder {
    width: 160rpx;
    height: 160rpx;
  }
  
  .image-preview {
    width: 160rpx;
    height: 160rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .form-section,
  .custom-picker {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .section-title,
  .form-label,
  .picker-text,
  .unit-text,
  .picker-title {
    color: white;
  }
  
  .input-wrapper,
  .picker-wrapper,
  .quantity-field,
  .unit-selector,
  .form-textarea {
    background-color: #404040;
  }
  
  .input-wrapper:focus-within,
  .picker-wrapper:active,
  .quantity-field:focus,
  .form-textarea:focus {
    background-color: #2d2d2d;
  }
  
  .form-input,
  .form-textarea {
    color: white;
  }
  
  .upload-placeholder {
    background-color: #404040;
    border-color: #555;
  }
  
  .picker-item {
    color: white;
    border-color: #404040;
  }
  
  .picker-item:active {
    background-color: #404040;
  }
  
  .reset-btn {
    background-color: #404040;
    color: #ccc;
    border-color: #555;
  }
}

/* 动画效果 */
.form-group {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.save-btn[loading] {
  opacity: 0.7;
}

.save-btn[disabled] {
  opacity: 0.5;
  pointer-events: none;
}