/* 食材添加/编辑页面样式 */

.page-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  padding: 24rpx;
  padding-bottom: 50rpx;
  box-sizing: border-box;
}

.ingredient-form {
  padding: 0;
}

/* 表单区域 */
.form-section {
  background-color: #fff;
  border-radius: 24rpx;
  margin-bottom: 28rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.03);
  position: relative;
  transition: all 0.3s ease;
}

.form-section:active {
  transform: scale(0.99);
}

.section-title {
  font-size: 34rpx;
  font-weight: 600;
  padding: 36rpx 36rpx 24rpx;
  border-bottom: 2rpx solid #f8f9fa;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  display: inline-block;
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4rpx;
  margin-right: 16rpx;
}

/* 图片上传 */
.image-upload {
  padding: 36rpx;
  display: flex;
  justify-content: center;
}

.image-preview {
  position: relative;
  width: 240rpx;
  height: 240rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border: 4rpx solid #fff;
  transition: all 0.3s ease;
}

.image-preview:active {
  transform: scale(0.98);
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
}

.delete-img-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(231, 76, 60, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(231, 76, 60, 0.4);
}

.delete-img-btn:active {
  background-color: rgba(231, 76, 60, 1);
  transform: scale(0.9);
}

.delete-icon {
  font-size: 24rpx;
  color: white;
  font-weight: 600;
}

.upload-placeholder {
  width: 240rpx;
  height: 240rpx;
  border: 3rpx dashed #cfd4da;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.upload-placeholder::before {
  content: '+';
  font-size: 80rpx;
  color: #adb5bd;
  font-weight: 300;
  line-height: 1;
  margin-bottom: 12rpx;
}

.upload-placeholder:active {
  border-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(102, 126, 234, 0.1) 100%);
  transform: scale(0.98);
}

.upload-text {
  font-size: 28rpx;
  color: #6c757d;
  font-weight: 500;
  text-align: center;
  line-height: 36rpx;
}

/* 表单组 */
.form-group {
  padding: 36rpx;
  border-bottom: 1rpx solid #f1f3f5;
  position: relative;
  transition: all 0.3s ease;
}

.form-group:last-child {
  border-bottom: none;
}

.form-group:active {
  background-color: rgba(248, 249, 250, 0.5);
}

.form-label {
  font-size: 30rpx;
  color: #495057;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.required {
  color: #e74c3c;
  margin-left: 8rpx;
  font-weight: 700;
  font-size: 28rpx;
}

/* 输入框 */
.input-wrapper {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx 28rpx;
  min-height: 84rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
}

/* 输入框光效 */
.input-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.6s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  background: #ffffff;
  box-shadow:
    0 0 0 4rpx rgba(102, 126, 234, 0.1),
    0 4rpx 16rpx rgba(102, 126, 234, 0.12);
  transform: translateY(-2rpx);
}

.input-wrapper:focus-within::before {
  left: 100%;
}

.form-input {
  flex: 1;
  font-size: 30rpx;
  line-height: 42rpx;
  height: 42rpx;
  background-color: transparent;
  border: none;
  outline: none;
  color: #343a40;
  vertical-align: middle;
}

/* 输入框placeholder样式 */
.form-input::placeholder {
  color: #adb5bd;
  font-size: 30rpx;
  line-height: 42rpx;
  vertical-align: middle;
}

/* 选择器 */
.picker-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx 28rpx;
  min-height: 84rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
}

/* 选择器光效 */
.picker-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.6s ease;
  z-index: 0;
}

/* 添加选择器右侧指示器 */
.picker-wrapper::after {
  content: '';
  position: absolute;
  right: 28rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 16rpx;
  height: 16rpx;
  border-right: 3rpx solid #adb5bd;
  border-bottom: 3rpx solid #adb5bd;
  transform: translateY(-50%) rotate(45deg);
  transition: all 0.3s ease;
  z-index: 1;
}

.picker-wrapper:active {
  border-color: #667eea;
  background: #ffffff;
  box-shadow:
    0 0 0 4rpx rgba(102, 126, 234, 0.1),
    0 4rpx 16rpx rgba(102, 126, 234, 0.12);
  transform: translateY(-2rpx);
}

.picker-wrapper:active::before {
  left: 100%;
}

.picker-wrapper:active::after {
  border-color: #667eea;
  transform: translateY(-50%) rotate(225deg);
}

.picker-text {
  font-size: 30rpx;
  color: #343a40;
  line-height: 42rpx;
}

.picker-text.placeholder {
  color: #adb5bd;
}

/* 数量输入 */
.quantity-input {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.quantity-field {
  flex: 2;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx 28rpx;
  min-height: 84rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 30rpx;
  line-height: 42rpx;
  height: 42rpx;
  color: #343a40;
  vertical-align: middle;
  box-sizing: border-box;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
  text-align: center;
  font-weight: 500;
}

/* 数量输入框placeholder样式 */
.quantity-field::placeholder {
  color: #adb5bd;
  font-size: 30rpx;
  line-height: 42rpx;
  vertical-align: middle;
}

.quantity-field:focus {
  border-color: #667eea;
  background: #ffffff;
  box-shadow:
    0 0 0 4rpx rgba(102, 126, 234, 0.1),
    0 4rpx 16rpx rgba(102, 126, 234, 0.12);
  transform: translateY(-2rpx);
}

.unit-selector {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx 28rpx;
  min-height: 84rpx;
  min-width: 140rpx;
  justify-content: center;
  box-sizing: border-box;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
}

/* 单位选择器光效 */
.unit-selector::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.6s ease;
  z-index: 0;
}

/* 单位选择器指示器 */
.unit-selector::after {
  content: '';
  position: absolute;
  right: 16rpx;
  top: 50%;
  width: 12rpx;
  height: 12rpx;
  border-right: 3rpx solid #adb5bd;
  border-bottom: 3rpx solid #adb5bd;
  transform: translateY(-50%) rotate(45deg);
  transition: all 0.3s ease;
  z-index: 1;
}

.unit-selector:active {
  border-color: #667eea;
  background: #ffffff;
  box-shadow:
    0 0 0 4rpx rgba(102, 126, 234, 0.1),
    0 4rpx 16rpx rgba(102, 126, 234, 0.12);
  transform: translateY(-2rpx);
}

.unit-selector:active::before {
  left: 100%;
}

.unit-selector:active::after {
  border-color: #667eea;
  transform: translateY(-50%) rotate(225deg);
}

.unit-text {
  font-size: 30rpx;
  color: #343a40;
  line-height: 42rpx;
}

.unit-text.placeholder {
  color: #adb5bd;
}

/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 140rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx 28rpx;
  font-size: 30rpx;
  line-height: 42rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  color: #343a40;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
  resize: none;
  font-weight: 400;
}

/* 文本域placeholder样式 */
.form-textarea::placeholder {
  color: #adb5bd;
  font-size: 30rpx;
  line-height: 42rpx;
  font-weight: 400;
}

.form-textarea:focus {
  border-color: #667eea;
  background: #ffffff;
  box-shadow:
    0 0 0 4rpx rgba(102, 126, 234, 0.1),
    0 4rpx 16rpx rgba(102, 126, 234, 0.12);
  transform: translateY(-2rpx);
}

/* 操作按钮 */
.form-actions {
  padding: 60rpx 36rpx;
  display: flex;
  flex-direction: column;
  gap: 28rpx;
}

.reset-btn {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
  border-radius: 20rpx;
  height: 96rpx;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.reset-btn:active {
  background-color: #e9ecef;
  border-color: #dee2e6;
  transform: translateY(2rpx);
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 20rpx;
  height: 96rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  box-shadow: 0 12rpx 36rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.save-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.save-btn:active {
  transform: translateY(3rpx);
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.4);
}

.save-btn:active::before {
  left: 100%;
}

/* 自定义选择器 */
.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease;
  backdrop-filter: blur(4rpx);
}

.picker-mask.show {
  opacity: 1;
  visibility: visible;
}

.custom-picker {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-radius: 36rpx 36rpx 0 0;
  z-index: 1001;
  transform: translateY(100%);
  transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-height: 75vh;
  box-shadow: 0 -20rpx 80rpx rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.custom-picker.show {
  transform: translateY(0);
  animation: slideUpBounce 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 弹出动画 */
@keyframes slideUpBounce {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  70% {
    transform: translateY(-20rpx);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.picker-content {
  padding: 20rpx 0 40rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

/* 自定义滚动条 */
.picker-content::-webkit-scrollbar {
  width: 6rpx;
}

.picker-content::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 3rpx;
}

.picker-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3rpx;
}

.picker-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 添加顶部指示器 */
.custom-picker::before {
  content: '';
  position: absolute;
  top: 16rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 8rpx;
  background: #e9ecef;
  border-radius: 4rpx;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 36rpx 24rpx;
  border-bottom: 1rpx solid #f1f3f5;
}

.picker-cancel,
.picker-confirm {
  font-size: 32rpx;
  font-weight: 600;
  padding: 16rpx 28rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.picker-cancel {
  color: #6c757d;
}

.picker-cancel:active {
  background-color: #f8f9fa;
  transform: scale(0.95);
}

.picker-confirm {
  color: #667eea;
}

.picker-confirm:active {
  background-color: rgba(102, 126, 234, 0.1);
  transform: scale(0.95);
}

.picker-title {
  font-size: 36rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.picker-item {
  margin: 16rpx 32rpx;
  padding: 32rpx;
  text-align: center;
  font-size: 32rpx;
  color: #343a40;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.picker-item:active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.2);
}

/* 选择器底部安全区域 */
.picker-safe-area {
  height: 48rpx;
  background: #ffffff;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .form-section {
    margin-bottom: 20rpx;
  }
  
  .section-title {
    padding: 28rpx 28rpx 20rpx;
    font-size: 32rpx;
  }
  
  .form-group {
    padding: 28rpx;
  }
  
  .form-label {
    font-size: 28rpx;
    margin-bottom: 16rpx;
  }
  
  .form-input,
  .picker-text,
  .unit-text {
    font-size: 28rpx;
  }
  
  .form-textarea {
    font-size: 28rpx;
    min-height: 120rpx;
  }
  
  .upload-placeholder {
    width: 200rpx;
    height: 200rpx;
  }
  
  .image-preview {
    width: 200rpx;
    height: 200rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background: #1a1a1a;
  }
  
  .form-section {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .section-title {
    border-color: #404040;
  }
  
  .form-group {
    border-color: #404040;
  }
  
  .form-label {
    color: #e9ecef;
  }
  
  .input-wrapper,
  .picker-wrapper,
  .quantity-field,
  .unit-selector,
  .form-textarea {
    background-color: #404040;
    border-color: #555;
  }
  
  .input-wrapper:focus-within,
  .picker-wrapper:active,
  .quantity-field:focus,
  .form-textarea:focus {
    background-color: #333;
    border-color: #667eea;
  }
  
  .form-input,
  .picker-text,
  .unit-text,
  .form-textarea {
    color: #f8f9fa;
  }
  
  .form-input::placeholder,
  .picker-text.placeholder,
  .unit-text.placeholder,
  .form-textarea::placeholder {
    color: #adb5bd;
  }
  
  .upload-placeholder {
    background-color: #404040;
    border-color: #555;
  }
  
  .upload-placeholder::before {
    color: #adb5bd;
  }
  
  .custom-picker {
    background-color: #2d2d2d;
  }
  
  .picker-item {
    background-color: #404040;
    color: #f8f9fa;
  }
  
  .picker-safe-area {
    background-color: #2d2d2d;
  }
  
  .picker-header {
    border-color: #404040;
  }
  
  .reset-btn {
    background-color: #404040;
    color: #e9ecef;
    border-color: #555;
  }
}

/* 动画效果 */
.form-group {
  animation: slideInUp 0.4s ease;
  animation-fill-mode: both;
}

.form-group:nth-child(2) {
  animation-delay: 0.05s;
}

.form-group:nth-child(3) {
  animation-delay: 0.1s;
}

.form-group:nth-child(4) {
  animation-delay: 0.15s;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.save-btn[loading] {
  opacity: 0.8;
}

.save-btn[disabled] {
  opacity: 0.6;
  pointer-events: none;
}