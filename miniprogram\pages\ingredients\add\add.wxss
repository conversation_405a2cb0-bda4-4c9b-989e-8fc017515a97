/* 食材添加/编辑页面样式 */

.page-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 40rpx;
}

.ingredient-form {
  padding: 0;
}

/* 表单区域 */
.form-section {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #2c3e50;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 2rpx solid #f8f9fa;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* 图片上传 */
.image-upload {
  padding: 30rpx;
}

.image-preview {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.preview-img {
  width: 100%;
  height: 100%;
}

.image-actions {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
}

.delete-img-btn {
  width: 56rpx;
  height: 56rpx;
  background-color: rgba(231, 76, 60, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.delete-img-btn:active {
  background-color: rgba(231, 76, 60, 1);
  transform: scale(0.95);
}

.delete-icon {
  font-size: 24rpx;
  color: white;
  font-weight: 600;
}

.delete-img-btn image {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

.upload-placeholder {
  width: 220rpx;
  height: 220rpx;
  border: 3rpx dashed #e9ecef;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transition: all 0.3s ease;
}

.upload-placeholder:active {
  border-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(102, 126, 234, 0.1) 100%);
}

.upload-text {
  font-size: 26rpx;
  color: #6c757d;
  font-weight: 500;
  text-align: center;
  line-height: 36rpx;
}

/* 表单组 */
.form-group {
  padding: 36rpx 32rpx;
  border-bottom: 1rpx solid #f8f9fa;
}

.form-group:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 30rpx;
  color: #495057;
  margin-bottom: 20rpx;
  display: block;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.required {
  color: #e74c3c;
  margin-left: 8rpx;
  font-weight: 700;
  font-size: 28rpx;
}

/* 输入框 */
.input-wrapper {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 20rpx;
  padding: 32rpx 28rpx;
  min-height: 96rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
}

/* 输入框光效 */
.input-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.6s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow:
    0 0 0 6rpx rgba(102, 126, 234, 0.12),
    0 8rpx 32rpx rgba(102, 126, 234, 0.15);
  transform: translateY(-2rpx);
}

.input-wrapper:focus-within::before {
  left: 100%;
}

.form-input {
  flex: 1;
  font-size: 28rpx;
  line-height: 44rpx;
  height: 44rpx;
  background-color: transparent;
  border: none;
  outline: none;
  color: #333;
  vertical-align: middle;
}

/* 输入框placeholder样式 */
.form-input::placeholder {
  color: #999;
  font-size: 28rpx;
  line-height: 44rpx;
  vertical-align: middle;
}

.input-actions {
  display: flex;
  gap: 16rpx;
  margin-left: 16rpx;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
}

.action-icon image {
  width: 28rpx;
  height: 28rpx;
}

/* 选择器 */
.picker-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 20rpx;
  padding: 32rpx 28rpx;
  min-height: 96rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

/* 选择器光效 */
.picker-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.6s ease;
  z-index: 0;
}

/* 添加选择器右侧指示器 */
.picker-wrapper::after {
  content: '';
  position: absolute;
  right: 28rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 10rpx solid #adb5bd;
  border-top: 8rpx solid transparent;
  border-bottom: 8rpx solid transparent;
  transition: all 0.3s ease;
  z-index: 1;
}

.picker-wrapper:active {
  border-color: #667eea;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow:
    0 0 0 6rpx rgba(102, 126, 234, 0.12),
    0 8rpx 32rpx rgba(102, 126, 234, 0.15);
  transform: translateY(-2rpx);
}

.picker-wrapper:active::before {
  left: 100%;
}

.picker-wrapper:active::after {
  border-left-color: #667eea;
  transform: translateY(-50%) rotate(180deg);
}

.picker-text {
  font-size: 28rpx;
  color: #333;
  line-height: 44rpx;
}

.picker-text.placeholder {
  color: #999;
}

.picker-arrow,
.picker-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 数量输入 */
.quantity-input {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.quantity-field {
  flex: 2;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 20rpx;
  padding: 32rpx 28rpx;
  min-height: 96rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 30rpx;
  line-height: 48rpx;
  height: 48rpx;
  color: #495057;
  vertical-align: middle;
  box-sizing: border-box;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  text-align: center;
  font-weight: 500;
}

/* 数量输入框placeholder样式 */
.quantity-field::placeholder {
  color: #999;
  font-size: 28rpx;
  line-height: 44rpx;
  vertical-align: middle;
}

.quantity-field:focus {
  border-color: #667eea;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow:
    0 0 0 6rpx rgba(102, 126, 234, 0.12),
    0 8rpx 32rpx rgba(102, 126, 234, 0.15);
  transform: translateY(-2rpx);
}

.unit-selector {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 20rpx;
  padding: 32rpx 28rpx;
  min-height: 96rpx;
  min-width: 160rpx;
  justify-content: center;
  box-sizing: border-box;
  border: 2rpx solid #e9ecef;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

/* 单位选择器光效 */
.unit-selector::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.6s ease;
  z-index: 0;
}

/* 单位选择器指示器 */
.unit-selector::after {
  content: '';
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 8rpx solid #adb5bd;
  border-top: 6rpx solid transparent;
  border-bottom: 6rpx solid transparent;
  transition: all 0.3s ease;
  z-index: 1;
}

.unit-selector:active {
  border-color: #667eea;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow:
    0 0 0 6rpx rgba(102, 126, 234, 0.12),
    0 8rpx 32rpx rgba(102, 126, 234, 0.15);
  transform: translateY(-2rpx);
}

.unit-selector:active::before {
  left: 100%;
}

.unit-selector:active::after {
  border-left-color: #667eea;
  transform: translateY(-50%) rotate(180deg);
}

.unit-text {
  font-size: 28rpx;
  color: #333;
  line-height: 44rpx;
}

.unit-text.placeholder {
  color: #999;
}

.unit-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 160rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 20rpx;
  padding: 32rpx 28rpx;
  font-size: 30rpx;
  line-height: 48rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  color: #495057;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  resize: none;
  font-weight: 400;
}

/* 文本域placeholder样式 */
.form-textarea::placeholder {
  color: #adb5bd;
  font-size: 30rpx;
  line-height: 48rpx;
  font-weight: 400;
}

.form-textarea:focus {
  border-color: #667eea;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow:
    0 0 0 6rpx rgba(102, 126, 234, 0.12),
    0 8rpx 32rpx rgba(102, 126, 234, 0.15);
  transform: translateY(-2rpx);
}

/* 操作按钮 */
.form-actions {
  padding: 48rpx 32rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.reset-btn {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  height: 88rpx;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.reset-btn:active {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 16rpx;
  height: 88rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.save-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
}

/* 自定义选择器 */
.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease;
  backdrop-filter: blur(4rpx);
}

.picker-mask.show {
  opacity: 1;
  visibility: visible;
}

.custom-picker {
  position: fixed;
  bottom: 0;
  left: 16rpx;
  right: 16rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 36rpx 36rpx 0 0;
  z-index: 1001;
  transform: translateY(100%);
  transition: transform 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-height: 75vh;
  box-shadow:
    0 -24rpx 96rpx rgba(0, 0, 0, 0.15),
    0 -12rpx 48rpx rgba(0, 0, 0, 0.1),
    0 -4rpx 16rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(24rpx);
  overflow: hidden;
}

.custom-picker.show {
  transform: translateY(0);
  animation: slideUpBounce 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 弹出动画 */
@keyframes slideUpBounce {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  60% {
    transform: translateY(-10rpx);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.picker-content {
  padding: 16rpx 0 40rpx;
  max-height: 520rpx;
  overflow-y: auto;
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
}

/* 自定义滚动条 */
.picker-content::-webkit-scrollbar {
  width: 6rpx;
}

.picker-content::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 3rpx;
}

.picker-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3rpx;
}

.picker-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.custom-picker picker-view-column {
  height: 480rpx;
  padding: 0;
}

/* 添加顶部指示器 */
.custom-picker::before {
  content: '';
  position: absolute;
  top: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 100rpx;
  height: 10rpx;
  background: linear-gradient(90deg, #e9ecef 0%, #adb5bd 50%, #e9ecef 100%);
  border-radius: 5rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 48rpx 40rpx 32rpx;
  border-bottom: 2rpx solid rgba(248, 249, 250, 0.8);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%);
  position: relative;
  backdrop-filter: blur(10rpx);
}

.picker-cancel,
.picker-confirm {
  font-size: 32rpx;
  font-weight: 600;
  padding: 20rpx 36rpx;
  border-radius: 24rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 140rpx;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.picker-cancel {
  color: #6c757d;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2rpx solid #dee2e6;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.picker-cancel:active {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.picker-confirm {
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 2rpx solid transparent;
  box-shadow:
    0 6rpx 20rpx rgba(102, 126, 234, 0.3),
    0 2rpx 8rpx rgba(102, 126, 234, 0.2);
}

.picker-confirm:active {
  transform: scale(0.95);
  box-shadow:
    0 3rpx 12rpx rgba(102, 126, 234, 0.4),
    0 1rpx 4rpx rgba(102, 126, 234, 0.3);
}

.picker-title {
  font-size: 36rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-align: center;
}

.picker-item {
  margin: 12rpx 24rpx;
  padding: 36rpx 32rpx;
  text-align: center;
  font-size: 34rpx;
  color: #495057;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 24rpx;
  border: 2rpx solid #f1f3f4;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  line-height: 48rpx;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.06),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5rpx;
}

/* 添加选中状态的渐变背景 */
.picker-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

/* 添加光效 */
.picker-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
  z-index: 1;
}

.picker-item:active {
  transform: scale(0.96);
  border-color: #667eea;
  color: white;
  box-shadow:
    0 12rpx 32rpx rgba(102, 126, 234, 0.25),
    0 6rpx 16rpx rgba(102, 126, 234, 0.15);
}

.picker-item:active::before {
  opacity: 1;
}

.picker-item:active::after {
  left: 100%;
}

/* 选择器底部安全区域 */
.picker-safe-area {
  height: 48rpx;
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  border-top: 1rpx solid rgba(248, 249, 250, 0.8);
}

/* 选择器项目之间的间距 */
.picker-item:last-child {
  margin-bottom: 16rpx;
}

.picker-item:first-child {
  margin-top: 16rpx;
}

.picker-item:last-child {
  border-bottom: none;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .form-section {
    margin-bottom: 16rpx;
  }
  
  .section-title {
    padding: 24rpx 24rpx 16rpx;
    font-size: 30rpx;
  }
  
  .form-group {
    padding: 24rpx;
  }
  
  .form-label {
    font-size: 26rpx;
    margin-bottom: 16rpx;
  }
  
  .form-input,
  .picker-text,
  .unit-text {
    font-size: 26rpx;
  }
  
  .form-textarea {
    font-size: 26rpx;
    min-height: 100rpx;
  }
  
  .upload-placeholder {
    width: 160rpx;
    height: 160rpx;
  }
  
  .image-preview {
    width: 160rpx;
    height: 160rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .form-section,
  .custom-picker {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .section-title,
  .form-label,
  .picker-text,
  .unit-text,
  .picker-title {
    color: white;
  }
  
  .input-wrapper,
  .picker-wrapper,
  .quantity-field,
  .unit-selector,
  .form-textarea {
    background-color: #404040;
  }
  
  .input-wrapper:focus-within,
  .picker-wrapper:active,
  .quantity-field:focus,
  .form-textarea:focus {
    background-color: #2d2d2d;
  }
  
  .form-input,
  .form-textarea {
    color: white;
  }
  
  .upload-placeholder {
    background-color: #404040;
    border-color: #555;
  }
  
  .picker-item {
    color: white;
    border-color: #404040;
  }
  
  .picker-item:active {
    background-color: #404040;
  }
  
  .reset-btn {
    background-color: #404040;
    color: #ccc;
    border-color: #555;
  }
}

/* 动画效果 */
.form-group {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.save-btn[loading] {
  opacity: 0.7;
}

.save-btn[disabled] {
  opacity: 0.5;
  pointer-events: none;
}