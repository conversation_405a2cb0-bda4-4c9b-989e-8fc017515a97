/* 食材添加/编辑页面样式 */

.page-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 40rpx;
}

.ingredient-form {
  padding: 0;
}

/* 表单区域 */
.form-section {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #2c3e50;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 2rpx solid #f8f9fa;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* 图片上传 */
.image-upload {
  padding: 30rpx;
}

.image-preview {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.preview-img {
  width: 100%;
  height: 100%;
}

.image-actions {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
}

.delete-img-btn {
  width: 56rpx;
  height: 56rpx;
  background-color: rgba(231, 76, 60, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.delete-img-btn:active {
  background-color: rgba(231, 76, 60, 1);
  transform: scale(0.95);
}

.delete-icon {
  font-size: 24rpx;
  color: white;
  font-weight: 600;
}

.delete-img-btn image {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

.upload-placeholder {
  width: 220rpx;
  height: 220rpx;
  border: 3rpx dashed #e9ecef;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transition: all 0.3s ease;
}

.upload-placeholder:active {
  border-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(102, 126, 234, 0.1) 100%);
}

.upload-text {
  font-size: 26rpx;
  color: #6c757d;
  font-weight: 500;
  text-align: center;
  line-height: 36rpx;
}

/* 表单组 */
.form-group {
  padding: 32rpx;
  border-bottom: 1rpx solid #f8f9fa;
}

.form-group:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 30rpx;
  color: #495057;
  margin-bottom: 24rpx;
  display: block;
  font-weight: 500;
}

.required {
  color: #e74c3c;
  margin-left: 6rpx;
  font-weight: 600;
}

/* 输入框 */
.input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 28rpx 24rpx;
  min-height: 88rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  background-color: white;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.form-input {
  flex: 1;
  font-size: 28rpx;
  line-height: 44rpx;
  height: 44rpx;
  background-color: transparent;
  border: none;
  outline: none;
  color: #333;
  vertical-align: middle;
}

/* 输入框placeholder样式 */
.form-input::placeholder {
  color: #999;
  font-size: 28rpx;
  line-height: 44rpx;
  vertical-align: middle;
}

.input-actions {
  display: flex;
  gap: 16rpx;
  margin-left: 16rpx;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
}

.action-icon image {
  width: 28rpx;
  height: 28rpx;
}

/* 选择器 */
.picker-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 28rpx 24rpx;
  min-height: 88rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  box-sizing: border-box;
  position: relative;
}

/* 添加选择器右侧指示器 */
.picker-wrapper::after {
  content: '';
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 8rpx solid #adb5bd;
  border-top: 6rpx solid transparent;
  border-bottom: 6rpx solid transparent;
}

.picker-wrapper:active {
  border-color: #667eea;
  background-color: white;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.picker-wrapper:active::after {
  border-left-color: #667eea;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
  line-height: 44rpx;
}

.picker-text.placeholder {
  color: #999;
}

.picker-arrow,
.picker-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 数量输入 */
.quantity-input {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.quantity-field {
  flex: 1;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 28rpx 24rpx;
  min-height: 88rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  font-size: 28rpx;
  line-height: 44rpx;
  height: 44rpx;
  color: #495057;
  vertical-align: middle;
  box-sizing: border-box;
}

/* 数量输入框placeholder样式 */
.quantity-field::placeholder {
  color: #999;
  font-size: 28rpx;
  line-height: 44rpx;
  vertical-align: middle;
}

.quantity-field:focus {
  border-color: #667eea;
  background-color: white;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.unit-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 28rpx 24rpx;
  min-height: 88rpx;
  min-width: 140rpx;
  justify-content: center;
  box-sizing: border-box;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
}

/* 单位选择器指示器 */
.unit-selector::after {
  content: '';
  position: absolute;
  right: 12rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 6rpx solid #adb5bd;
  border-top: 4rpx solid transparent;
  border-bottom: 4rpx solid transparent;
}

.unit-selector:active {
  border-color: #667eea;
  background-color: white;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.unit-selector:active::after {
  border-left-color: #667eea;
}

.unit-text {
  font-size: 28rpx;
  color: #333;
  line-height: 44rpx;
}

.unit-text.placeholder {
  color: #999;
}

.unit-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 28rpx 20rpx;
  font-size: 28rpx;
  line-height: 44rpx;
  border: 2rpx solid transparent;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
  color: #333;
}

/* 文本域placeholder样式 */
.form-textarea::placeholder {
  color: #999;
  font-size: 28rpx;
  line-height: 44rpx;
}

.form-textarea:focus {
  border-color: #4CAF50;
  background-color: white;
}

/* 操作按钮 */
.form-actions {
  padding: 48rpx 32rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.reset-btn {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  height: 88rpx;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.reset-btn:active {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 16rpx;
  height: 88rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.save-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
}

/* 自定义选择器 */
.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease;
  backdrop-filter: blur(4rpx);
}

.picker-mask.show {
  opacity: 1;
  visibility: visible;
}

.custom-picker {
  position: fixed;
  bottom: 0;
  left: 20rpx;
  right: 20rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 32rpx 32rpx 0 0;
  z-index: 1001;
  transform: translateY(100%);
  transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-height: 70vh;
  box-shadow:
    0 -20rpx 80rpx rgba(0, 0, 0, 0.12),
    0 -8rpx 32rpx rgba(0, 0, 0, 0.08),
    0 -2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
}

.custom-picker.show {
  transform: translateY(0);
  animation: slideUpBounce 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 弹出动画 */
@keyframes slideUpBounce {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  60% {
    transform: translateY(-10rpx);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.picker-content {
  padding: 0 20rpx 32rpx;
  max-height: 480rpx;
  overflow-y: auto;
}

/* 自定义滚动条 */
.picker-content::-webkit-scrollbar {
  width: 6rpx;
}

.picker-content::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 3rpx;
}

.picker-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3rpx;
}

.picker-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.custom-picker picker-view-column {
  height: 480rpx;
  padding: 0;
}

/* 添加顶部指示器 */
.custom-picker::before {
  content: '';
  position: absolute;
  top: 16rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 8rpx;
  background-color: #e9ecef;
  border-radius: 4rpx;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 24rpx;
  border-bottom: none;
  background: transparent;
  position: relative;
}

.picker-cancel,
.picker-confirm {
  font-size: 32rpx;
  font-weight: 600;
  padding: 16rpx 32rpx;
  border-radius: 20rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 120rpx;
  text-align: center;
}

.picker-cancel {
  color: #6c757d;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
}

.picker-cancel:active {
  background-color: #e9ecef;
  transform: scale(0.96);
}

.picker-confirm {
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 2rpx solid transparent;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.picker-confirm:active {
  transform: scale(0.96);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
}

.picker-title {
  font-size: 36rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-align: center;
}

.picker-item {
  margin: 8rpx 20rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  font-size: 32rpx;
  color: #495057;
  background-color: white;
  border-radius: 20rpx;
  border: 2rpx solid #f1f3f4;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  line-height: 44rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
}

/* 添加选中状态的渐变背景 */
.picker-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.picker-item:active {
  transform: scale(0.98);
  border-color: #667eea;
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.2);
}

.picker-item:active::before {
  opacity: 1;
}

/* 选择器底部安全区域 */
.picker-safe-area {
  height: 40rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

/* 选择器项目之间的间距 */
.picker-item:last-child {
  margin-bottom: 16rpx;
}

.picker-item:first-child {
  margin-top: 16rpx;
}

.picker-item:last-child {
  border-bottom: none;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .form-section {
    margin-bottom: 16rpx;
  }
  
  .section-title {
    padding: 24rpx 24rpx 16rpx;
    font-size: 30rpx;
  }
  
  .form-group {
    padding: 24rpx;
  }
  
  .form-label {
    font-size: 26rpx;
    margin-bottom: 16rpx;
  }
  
  .form-input,
  .picker-text,
  .unit-text {
    font-size: 26rpx;
  }
  
  .form-textarea {
    font-size: 26rpx;
    min-height: 100rpx;
  }
  
  .upload-placeholder {
    width: 160rpx;
    height: 160rpx;
  }
  
  .image-preview {
    width: 160rpx;
    height: 160rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .form-section,
  .custom-picker {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .section-title,
  .form-label,
  .picker-text,
  .unit-text,
  .picker-title {
    color: white;
  }
  
  .input-wrapper,
  .picker-wrapper,
  .quantity-field,
  .unit-selector,
  .form-textarea {
    background-color: #404040;
  }
  
  .input-wrapper:focus-within,
  .picker-wrapper:active,
  .quantity-field:focus,
  .form-textarea:focus {
    background-color: #2d2d2d;
  }
  
  .form-input,
  .form-textarea {
    color: white;
  }
  
  .upload-placeholder {
    background-color: #404040;
    border-color: #555;
  }
  
  .picker-item {
    color: white;
    border-color: #404040;
  }
  
  .picker-item:active {
    background-color: #404040;
  }
  
  .reset-btn {
    background-color: #404040;
    color: #ccc;
    border-color: #555;
  }
}

/* 动画效果 */
.form-group {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.save-btn[loading] {
  opacity: 0.7;
}

.save-btn[disabled] {
  opacity: 0.5;
  pointer-events: none;
}