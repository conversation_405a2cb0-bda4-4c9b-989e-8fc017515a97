// 消费分析页面
const app = getApp();

Page({
  data: {
    // 统计数据
    stats: {
      totalExpense: 0,
      monthlyExpense: 0,
      averageDaily: 0,
      categoryExpense: [],
      monthlyTrend: [],
      topExpenseItems: [],
      savingsAnalysis: {
        totalSavings: 0,
        wasteReduction: 0,
        suggestions: []
      }
    },
    
    // 页面状态
    loading: false,
    refreshing: false,
    
    // 时间范围
    timeRange: 'month',
    timeRanges: [
      { id: 'week', name: '本周' },
      { id: 'month', name: '本月' },
      { id: 'quarter', name: '本季度' },
      { id: 'year', name: '本年' }
    ],
    
    // 图表类型
    chartType: 'category',
    chartTypes: [
      { id: 'category', name: '分类支出' },
      { id: 'trend', name: '趋势分析' },
      { id: 'top', name: '支出排行' },
      { id: 'savings', name: '节省分析' }
    ]
  },

  onLoad() {
    console.log('消费分析页面加载');
    this.loadAnalysisData();
  },

  onShow() {
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  // 加载分析数据
  async loadAnalysisData() {
    try {
      this.setData({ loading: true });

      const params = {
        timeRange: this.data.timeRange
      };

      const result = await app.api.getConsumptionAnalysis(params);
      
      this.setData({
        stats: result,
        loading: false
      });

    } catch (error) {
      console.error('加载消费分析数据失败:', error);
      app.showError('加载失败');
      this.setData({ loading: false });
    }
  },

  // 刷新数据
  async refreshData() {
    try {
      this.setData({ refreshing: true });

      const params = {
        timeRange: this.data.timeRange
      };

      const result = await app.api.getConsumptionAnalysis(params);
      
      this.setData({
        stats: result,
        refreshing: false
      });

      wx.stopPullDownRefresh();

    } catch (error) {
      console.error('刷新数据失败:', error);
      app.showError('刷新失败');
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  // 切换时间范围
  switchTimeRange(e) {
    const timeRange = e.currentTarget.dataset.range;
    if (timeRange === this.data.timeRange) return;

    this.setData({ timeRange });
    this.loadAnalysisData();
  },

  // 切换图表类型
  switchChartType(e) {
    const chartType = e.currentTarget.dataset.type;
    this.setData({ chartType });
  },

  // 查看分类详情
  viewCategoryDetail(e) {
    const category = e.currentTarget.dataset.category;
    wx.navigateTo({
      url: `/pages/shopping/list/list?category=${category}`
    });
  },

  // 查看商品详情
  viewItemDetail(e) {
    const itemId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/ingredients/detail/detail?id=${itemId}`
    });
  },

  // 查看购物记录
  viewShoppingRecords() {
    wx.navigateTo({
      url: '/pages/shopping/list/list'
    });
  },

  // 格式化金额
  formatAmount(amount) {
    if (amount >= 10000) {
      return `${(amount / 10000).toFixed(1)}万`;
    } else if (amount >= 1000) {
      return `${(amount / 1000).toFixed(1)}k`;
    }
    return amount.toFixed(2);
  },

  // 计算百分比
  calculatePercentage(value, total) {
    if (total === 0) return 0;
    return Math.round((value / total) * 100);
  },

  // 获取趋势图标
  getTrendIcon(trend) {
    if (trend > 0) return '/images/icons/trend-up.png';
    if (trend < 0) return '/images/icons/trend-down.png';
    return '/images/icons/trend-flat.png';
  },

  // 获取趋势颜色
  getTrendColor(trend) {
    if (trend > 0) return '#dc3545';
    if (trend < 0) return '#28a745';
    return '#6c757d';
  },

  // 格式化日期
  formatDate(dateStr) {
    const date = new Date(dateStr);
    return `${date.getMonth() + 1}/${date.getDate()}`;
  },

  // 格式化月份
  formatMonth(dateStr) {
    const date = new Date(dateStr);
    return `${date.getFullYear()}年${date.getMonth() + 1}月`;
  }
});