// 菜谱详情页面
const app = getApp();

Page({
  data: {
    // 页面状态
    loading: true,
    recipeId: null,
    
    // 菜谱数据
    recipe: null,
    
    // 界面状态
    currentTab: 0,
    showShareModal: false,
    showNutritionModal: false,
    
    // 用户状态
    isFavorited: false,
    userRating: 0,
    
    // 制作状态
    cookingMode: false,
    currentStep: 0,
    completedSteps: [],
    
    // 标签页
    tabs: [
      { id: 0, name: '食材', emoji: '🥬' },
      { id: 1, name: '步骤', emoji: '📝' },
      { id: 2, name: '营养', emoji: '🍎' },
      { id: 3, name: '评价', emoji: '⭐' }
    ]
  },

  onLoad(options) {
    console.log('菜谱详情页面加载', options);
    
    if (options.id) {
      this.setData({ recipeId: options.id });
      this.loadRecipeDetail(options.id);
    } else {
      app.showError('菜谱ID不能为空');
      wx.navigateBack();
    }
  },

  onShow() {
    // 页面显示时刷新收藏状态
    if (this.data.recipeId) {
      this.checkFavoriteStatus();
    }
  },

  // 加载菜谱详情
  async loadRecipeDetail(id) {
    try {
      this.setData({ loading: true });

      const res = await app.request({
        url: `/recipes/${id}`
      });

      const recipe = res.data;

      this.setData({
        recipe: recipe,
        loading: false
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: recipe.name || '菜谱详情'
      });

      // 检查收藏状态
      this.checkFavoriteStatus();

    } catch (error) {
      console.error('加载菜谱详情失败:', error);
      app.showError('加载菜谱详情失败');
      this.setData({ loading: false });
    }
  },

  // 检查收藏状态
  async checkFavoriteStatus() {
    try {
      const res = await app.request({
        url: `/favorites/check`,
        data: {
          type: 'recipe',
          target_id: this.data.recipeId
        }
      });

      this.setData({
        isFavorited: res.data.is_favorited || false
      });
    } catch (error) {
      console.error('检查收藏状态失败:', error);
      // 收藏状态检查失败不影响页面显示
      this.setData({ isFavorited: false });
    }
  },

  // 标签页切换
  switchTab(e) {
    const tabId = e.currentTarget.dataset.tab;
    this.setData({ currentTab: tabId });
  },

  // 收藏/取消收藏
  async toggleFavorite() {
    try {
      const { recipeId, isFavorited } = this.data;
      
      if (isFavorited) {
        await app.api.removeFavoriteRecipe(recipeId);
        app.showSuccess('已取消收藏');
      } else {
        await app.api.addFavoriteRecipe(recipeId);
        app.showSuccess('已添加收藏');
      }
      
      this.setData({
        isFavorited: !isFavorited
      });
      
    } catch (error) {
      console.error('收藏操作失败:', error);
      app.showError('操作失败');
    }
  },

  // 评分
  rateRecipe(e) {
    const rating = e.currentTarget.dataset.rating;
    this.setData({ userRating: rating });
    this.submitRating(rating);
  },

  async submitRating(rating) {
    try {
      await app.api.rateRecipe(this.data.recipeId, rating);
      app.showSuccess('评分成功');
    } catch (error) {
      console.error('评分失败:', error);
      app.showError('评分失败');
    }
  },

  // 开始制作
  startCooking() {
    this.setData({
      cookingMode: true,
      currentStep: 0,
      completedSteps: []
    });
    
    wx.showModal({
      title: '制作模式',
      content: '已进入制作模式，按步骤完成制作吧！',
      showCancel: false
    });
  },

  // 退出制作模式
  exitCooking() {
    wx.showModal({
      title: '退出制作',
      content: '确定要退出制作模式吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            cookingMode: false,
            currentStep: 0,
            completedSteps: []
          });
        }
      }
    });
  },

  // 完成步骤
  completeStep(e) {
    const stepIndex = e.currentTarget.dataset.index;
    const completedSteps = [...this.data.completedSteps];
    
    if (completedSteps.includes(stepIndex)) {
      // 取消完成
      const index = completedSteps.indexOf(stepIndex);
      completedSteps.splice(index, 1);
    } else {
      // 标记完成
      completedSteps.push(stepIndex);
    }
    
    this.setData({ completedSteps });
    
    // 检查是否全部完成
    if (completedSteps.length === this.data.recipe.steps.length) {
      wx.showModal({
        title: '制作完成',
        content: '恭喜！您已完成所有制作步骤，快来品尝美味吧！',
        confirmText: '完成',
        cancelText: '继续',
        success: (res) => {
          if (res.confirm) {
            this.setData({
              cookingMode: false,
              currentStep: 0,
              completedSteps: []
            });
          }
        }
      });
    }
  },

  // 上一步
  prevStep() {
    const currentStep = Math.max(0, this.data.currentStep - 1);
    this.setData({ currentStep });
  },

  // 下一步
  nextStep() {
    const maxStep = this.data.recipe.steps.length - 1;
    const currentStep = Math.min(maxStep, this.data.currentStep + 1);
    this.setData({ currentStep });
  },

  // 检查食材
  async checkIngredients() {
    try {
      const result = await app.api.checkRecipeIngredients(this.data.recipeId);
      
      wx.showModal({
        title: '食材检查',
        content: `可制作：${result.canMake ? '是' : '否'}\n缺少食材：${result.missingIngredients.length}种`,
        confirmText: '查看详情',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: `/pages/ingredients/check/check?recipeId=${this.data.recipeId}`
            });
          }
        }
      });
      
    } catch (error) {
      console.error('检查食材失败:', error);
      app.showError('检查食材失败');
    }
  },

  // 添加到购物清单
  async addToShoppingList() {
    try {
      await app.api.addRecipeToShoppingList(this.data.recipeId);
      app.showSuccess('已添加到购物清单');
    } catch (error) {
      console.error('添加到购物清单失败:', error);
      app.showError('添加失败');
    }
  },

  // 显示营养详情
  showNutritionDetail() {
    this.setData({ showNutritionModal: true });
  },

  hideNutritionDetail() {
    this.setData({ showNutritionModal: false });
  },

  // 分享菜谱
  showShareModal() {
    this.setData({ showShareModal: true });
  },

  hideShareModal() {
    this.setData({ showShareModal: false });
  },

  // 分享给朋友
  shareToFriend() {
    wx.showShareMenu({
      withShareTicket: true
    });
    this.hideShareModal();
  },

  // 生成分享图片
  async generateShareImage() {
    try {
      wx.showLoading({ title: '生成中...' });
      
      // 这里可以调用canvas生成分享图片
      const shareImageUrl = await this.createShareCanvas();
      
      wx.hideLoading();
      
      wx.previewImage({
        urls: [shareImageUrl],
        current: shareImageUrl
      });
      
      this.hideShareModal();
      
    } catch (error) {
      console.error('生成分享图片失败:', error);
      wx.hideLoading();
      app.showError('生成失败');
    }
  },

  // 创建分享画布
  createShareCanvas() {
    return new Promise((resolve, reject) => {
      const ctx = wx.createCanvasContext('shareCanvas');
      const { recipe } = this.data;
      
      // 绘制背景
      ctx.setFillStyle('#ffffff');
      ctx.fillRect(0, 0, 750, 1000);
      
      // 绘制菜谱信息
      ctx.setFillStyle('#333333');
      ctx.setFontSize(36);
      ctx.fillText(recipe.name, 50, 100);
      
      ctx.setFontSize(24);
      ctx.setFillStyle('#666666');
      ctx.fillText(`制作时间：${recipe.cookTime}分钟`, 50, 150);
      ctx.fillText(`难度：${this.getDifficultyName(recipe.difficulty)}`, 300, 150);
      
      ctx.draw(false, () => {
        wx.canvasToTempFilePath({
          canvasId: 'shareCanvas',
          success: (res) => {
            resolve(res.tempFilePath);
          },
          fail: reject
        });
      });
    });
  },

  // 编辑菜谱
  editRecipe() {
    wx.navigateTo({
      url: `/pages/recipes/add/add?id=${this.data.recipeId}`
    });
  },

  // 删除菜谱
  deleteRecipe() {
    wx.showModal({
      title: '删除菜谱',
      content: '确定要删除这个菜谱吗？删除后无法恢复。',
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: async (res) => {
        if (res.confirm) {
          try {
            await app.api.deleteRecipe(this.data.recipeId);
            app.showSuccess('菜谱已删除');
            wx.navigateBack();
          } catch (error) {
            console.error('删除菜谱失败:', error);
            app.showError('删除失败');
          }
        }
      }
    });
  },

  // 预览图片
  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      urls: [url],
      current: url
    });
  },

  // 获取难度名称
  getDifficultyName(difficulty) {
    const difficulties = {
      1: '简单',
      2: '中等',
      3: '困难'
    };
    return difficulties[difficulty] || '简单';
  },

  // 获取分类名称
  getCategoryName(categoryId) {
    const categories = {
      'breakfast': '早餐',
      'lunch': '午餐',
      'dinner': '晚餐',
      'snack': '小食',
      'dessert': '甜品',
      'soup': '汤品'
    };
    return categories[categoryId] || '其他';
  },

  // 格式化营养值
  formatNutritionValue(value, unit = 'g') {
    return value ? `${value}${unit}` : `0${unit}`;
  },

  // 页面分享
  onShareAppMessage() {
    const { recipe } = this.data;
    return {
      title: `${recipe.name} - 美味菜谱分享`,
      path: `/pages/recipes/detail/detail?id=${this.data.recipeId}`,
      imageUrl: recipe.image
    };
  },

  // 页面分享到朋友圈
  onShareTimeline() {
    const { recipe } = this.data;
    return {
      title: `${recipe.name} - 美味菜谱分享`,
      imageUrl: recipe.image
    };
  }
});