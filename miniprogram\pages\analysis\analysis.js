// 数据分析页面逻辑
const app = getApp();

Page({
  data: {
    loading: false,
    refreshing: false,
    currentTab: 0,
    tabs: ['健康分析', '消费统计', '使用趋势'],
    
    // 健康分析数据
    healthData: {
      score: 0,
      level: '良好',
      suggestions: [],
      nutritionStats: {
        protein: 0,
        carbs: 0,
        fat: 0,
        vitamins: 0,
        minerals: 0
      },
      expiryStats: {
        expired: 0,
        expiringSoon: 0,
        fresh: 0
      }
    },
    
    // 消费统计数据
    expenseData: {
      totalExpense: 0,
      monthlyExpense: 0,
      avgDailyExpense: 0,
      categoryExpenses: [],
      monthlyTrend: [],
      topCategories: []
    },
    
    // 使用趋势数据
    usageData: {
      totalIngredients: 0,
      usedIngredients: 0,
      wastedIngredients: 0,
      usageRate: 0,
      wasteRate: 0,
      monthlyUsage: [],
      categoryUsage: []
    },
    
    // 图表配置
    chartOptions: {
      nutrition: {
        type: 'radar',
        data: [],
        options: {}
      },
      expense: {
        type: 'line',
        data: [],
        options: {}
      },
      usage: {
        type: 'doughnut',
        data: [],
        options: {}
      }
    },
    
    // 时间范围
    timeRange: 'month',
    timeRanges: [
      { value: 'week', label: '本周' },
      { value: 'month', label: '本月' },
      { value: 'quarter', label: '本季度' },
      { value: 'year', label: '本年' }
    ]
  },

  onLoad() {
    console.log('数据分析页面加载');
    this.loadAnalysisData();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.userInfo) {
      wx.redirectTo({
        url: '/pages/login/login'
      });
      return;
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ refreshing: true });
    this.loadAnalysisData().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 加载分析数据
  async loadAnalysisData() {
    this.setData({ loading: true });

    try {
      const promises = [
        this.loadHealthData(),
        this.loadExpenseData(),
        this.loadUsageData()
      ];

      await Promise.all(promises);

    } catch (error) {
      console.error('加载分析数据失败:', error);
      app.showError('加载数据失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载健康分析数据
  async loadHealthData() {
    try {
      const res = await app.request({
        url: '/analysis/health',
        data: {
          timeRange: this.data.timeRange
        }
      });

      const healthData = res.data;
      
      // 更新营养雷达图
      const nutritionChart = {
        type: 'radar',
        data: {
          labels: ['蛋白质', '碳水化合物', '脂肪', '维生素', '矿物质'],
          datasets: [{
            label: '营养摄入',
            data: [
              healthData.nutritionStats.protein,
              healthData.nutritionStats.carbs,
              healthData.nutritionStats.fat,
              healthData.nutritionStats.vitamins,
              healthData.nutritionStats.minerals
            ],
            backgroundColor: 'rgba(76, 175, 80, 0.2)',
            borderColor: '#4CAF50',
            borderWidth: 2
          }]
        },
        options: {
          responsive: true,
          scales: {
            r: {
              beginAtZero: true,
              max: 100
            }
          }
        }
      };

      this.setData({
        healthData,
        'chartOptions.nutrition': nutritionChart
      });

    } catch (error) {
      console.error('加载健康数据失败:', error);
      throw error;
    }
  },

  // 加载消费统计数据
  async loadExpenseData() {
    try {
      const res = await app.request({
        url: '/analysis/expense',
        data: {
          timeRange: this.data.timeRange
        }
      });

      const expenseData = res.data;
      
      // 更新消费趋势图
      const expenseChart = {
        type: 'line',
        data: {
          labels: expenseData.monthlyTrend.map(item => item.month),
          datasets: [{
            label: '月度消费',
            data: expenseData.monthlyTrend.map(item => item.amount),
            borderColor: '#2196F3',
            backgroundColor: 'rgba(33, 150, 243, 0.1)',
            borderWidth: 2,
            fill: true
          }]
        },
        options: {
          responsive: true,
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      };

      this.setData({
        expenseData,
        'chartOptions.expense': expenseChart
      });

    } catch (error) {
      console.error('加载消费数据失败:', error);
      throw error;
    }
  },

  // 加载使用趋势数据
  async loadUsageData() {
    try {
      const res = await app.request({
        url: '/analysis/usage',
        data: {
          timeRange: this.data.timeRange
        }
      });

      const usageData = res.data;
      
      // 更新使用率饼图
      const usageChart = {
        type: 'doughnut',
        data: {
          labels: ['已使用', '浪费', '剩余'],
          datasets: [{
            data: [
              usageData.usedIngredients,
              usageData.wastedIngredients,
              usageData.totalIngredients - usageData.usedIngredients - usageData.wastedIngredients
            ],
            backgroundColor: [
              '#4CAF50',
              '#FF5722',
              '#FFC107'
            ],
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              position: 'bottom'
            }
          }
        }
      };

      this.setData({
        usageData,
        'chartOptions.usage': usageChart
      });

    } catch (error) {
      console.error('加载使用数据失败:', error);
      throw error;
    }
  },

  // 切换标签页
  switchTab(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentTab: index
    });
  },

  // 切换时间范围
  switchTimeRange(e) {
    const range = e.currentTarget.dataset.range;
    this.setData({
      timeRange: range
    });
    this.loadAnalysisData();
  },

  // 查看健康建议详情
  viewHealthSuggestions() {
    const suggestions = this.data.healthData.suggestions;
    if (suggestions.length === 0) {
      app.showSuccess('您的饮食很健康！');
      return;
    }

    wx.showModal({
      title: '健康建议',
      content: suggestions.join('\n'),
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 查看营养详情
  viewNutritionDetail() {
    wx.navigateTo({
      url: '/pages/analysis/nutrition/nutrition'
    });
  },

  // 查看消费详情
  viewExpenseDetail() {
    wx.navigateTo({
      url: '/pages/analysis/expense/expense'
    });
  },

  // 查看使用详情
  viewUsageDetail() {
    wx.navigateTo({
      url: '/pages/analysis/usage/usage'
    });
  },

  // 导出报告
  async exportReport() {
    wx.showLoading({ title: '生成中...' });

    try {
      const res = await app.request({
        url: '/analysis/export',
        method: 'POST',
        data: {
          timeRange: this.data.timeRange,
          includeCharts: true
        }
      });

      wx.hideLoading();

      // 下载报告
      wx.downloadFile({
        url: res.data.downloadUrl,
        success: (downloadRes) => {
          if (downloadRes.statusCode === 200) {
            wx.openDocument({
              filePath: downloadRes.tempFilePath,
              success: () => {
                app.showSuccess('报告已生成');
              },
              fail: () => {
                app.showError('打开报告失败');
              }
            });
          }
        },
        fail: () => {
          app.showError('下载报告失败');
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('导出报告失败:', error);
      app.showError('导出失败');
    }
  },

  // 分享报告
  shareReport() {
    wx.showActionSheet({
      itemList: ['分享给好友', '保存到相册'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.shareToFriend();
        } else {
          this.saveToAlbum();
        }
      }
    });
  },

  // 分享给好友
  shareToFriend() {
    // 生成分享图片
    this.generateShareImage().then(imagePath => {
      wx.shareAppMessage({
        title: '我的食材管理分析报告',
        path: '/pages/analysis/analysis',
        imageUrl: imagePath
      });
    });
  },

  // 保存到相册
  async saveToAlbum() {
    try {
      // 请求保存权限
      const authRes = await wx.authorize({
        scope: 'scope.writePhotosAlbum'
      });

      // 生成分享图片
      const imagePath = await this.generateShareImage();

      // 保存到相册
      await wx.saveImageToPhotosAlbum({
        filePath: imagePath
      });

      app.showSuccess('已保存到相册');

    } catch (error) {
      console.error('保存到相册失败:', error);
      if (error.errMsg.includes('auth deny')) {
        wx.showModal({
          title: '需要授权',
          content: '需要您授权保存图片到相册',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting();
            }
          }
        });
      } else {
        app.showError('保存失败');
      }
    }
  },

  // 生成分享图片
  generateShareImage() {
    return new Promise((resolve, reject) => {
      const ctx = wx.createCanvasContext('shareCanvas', this);
      
      // 设置画布背景
      ctx.setFillStyle('#ffffff');
      ctx.fillRect(0, 0, 750, 1000);
      
      // 绘制标题
      ctx.setFillStyle('#333333');
      ctx.setFontSize(36);
      ctx.setTextAlign('center');
      ctx.fillText('我的食材管理分析报告', 375, 80);
      
      // 绘制健康评分
      const score = this.data.healthData.score;
      const level = this.data.healthData.level;
      
      ctx.setFillStyle('#4CAF50');
      ctx.setFontSize(72);
      ctx.fillText(score.toString(), 375, 200);
      
      ctx.setFillStyle('#666666');
      ctx.setFontSize(28);
      ctx.fillText(`健康等级：${level}`, 375, 240);
      
      // 绘制统计数据
      const stats = [
        { label: '总消费', value: `¥${this.data.expenseData.totalExpense}` },
        { label: '使用率', value: `${this.data.usageData.usageRate}%` },
        { label: '浪费率', value: `${this.data.usageData.wasteRate}%` }
      ];
      
      stats.forEach((stat, index) => {
        const y = 320 + index * 80;
        ctx.setFillStyle('#333333');
        ctx.setFontSize(32);
        ctx.setTextAlign('left');
        ctx.fillText(stat.label, 150, y);
        
        ctx.setTextAlign('right');
        ctx.fillText(stat.value, 600, y);
      });
      
      // 绘制二维码
      ctx.setFillStyle('#cccccc');
      ctx.fillRect(300, 700, 150, 150);
      ctx.setFillStyle('#333333');
      ctx.setFontSize(24);
      ctx.setTextAlign('center');
      ctx.fillText('扫码查看详情', 375, 880);
      
      // 绘制底部信息
      ctx.setFillStyle('#999999');
      ctx.setFontSize(20);
      ctx.fillText('智能冰箱食材管理助手', 375, 950);
      
      ctx.draw(false, () => {
        wx.canvasToTempFilePath({
          canvasId: 'shareCanvas',
          success: (res) => {
            resolve(res.tempFilePath);
          },
          fail: reject
        }, this);
      });
    });
  },

  // 设置提醒
  setReminder() {
    wx.navigateTo({
      url: '/pages/settings/reminder/reminder'
    });
  },

  // 查看历史记录
  viewHistory() {
    wx.navigateTo({
      url: '/pages/analysis/history/history'
    });
  }
});