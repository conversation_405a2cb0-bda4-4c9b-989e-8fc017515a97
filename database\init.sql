-- 智能冰箱食材管理助手数据库初始化脚本
-- 创建时间: 2024-01-01
-- 字符集: utf8mb4

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 用户表
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `password` varchar(255) DEFAULT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `wx_openid` varchar(100) DEFAULT NULL COMMENT '微信openid',
  `wx_unionid` varchar(100) DEFAULT NULL COMMENT '微信unionid',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态 1:正常 0:禁用',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 0:否 1:是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_wx_openid` (`wx_openid`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- 食材分类表
-- ----------------------------
DROP TABLE IF EXISTS `ingredient_categories`;
CREATE TABLE `ingredient_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='食材分类表';

-- 插入默认分类数据
INSERT INTO `ingredient_categories` (`name`, `icon`, `sort_order`) VALUES
('蔬菜', '🥬', 1),
('水果', '🍎', 2),
('肉类', '🥩', 3),
('海鲜', '🐟', 4),
('蛋奶', '🥚', 5),
('调料', '🧂', 6),
('主食', '🍚', 7),
('饮品', '🥤', 8),
('零食', '🍪', 9),
('其他', '📦', 10);

-- ----------------------------
-- 用户食材表
-- ----------------------------
DROP TABLE IF EXISTS `user_ingredients`;
CREATE TABLE `user_ingredients` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '食材ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `name` varchar(100) NOT NULL COMMENT '食材名称',
  `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `quantity` decimal(10,2) DEFAULT '1.00' COMMENT '数量',
  `unit` varchar(20) DEFAULT '个' COMMENT '单位',
  `purchase_date` date DEFAULT NULL COMMENT '购买日期',
  `expire_date` date DEFAULT NULL COMMENT '过期日期',
  `storage_location` varchar(50) DEFAULT NULL COMMENT '存储位置',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '价格',
  `image` varchar(255) DEFAULT NULL COMMENT '图片',
  `notes` text COMMENT '备注',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态 1:正常 2:即将过期 3:已过期',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_expire_date` (`expire_date`),
  KEY `idx_status` (`status`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`category_id`) REFERENCES `ingredient_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户食材表';

-- ----------------------------
-- 菜谱表
-- ----------------------------
DROP TABLE IF EXISTS `recipes`;
CREATE TABLE `recipes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '菜谱ID',
  `name` varchar(100) NOT NULL COMMENT '菜谱名称',
  `description` text COMMENT '菜谱描述',
  `image` varchar(255) DEFAULT NULL COMMENT '菜谱图片',
  `category` varchar(50) DEFAULT NULL COMMENT '菜谱分类',
  `difficulty` varchar(20) DEFAULT '简单' COMMENT '难度等级',
  `cook_time` int(11) DEFAULT '30' COMMENT '制作时间(分钟)',
  `servings` int(11) DEFAULT '2' COMMENT '份量(人份)',
  `calories` varchar(20) DEFAULT NULL COMMENT '热量',
  `ingredients` json DEFAULT NULL COMMENT '食材清单',
  `steps` json DEFAULT NULL COMMENT '制作步骤',
  `tips` text COMMENT '小贴士',
  `tools` varchar(255) DEFAULT NULL COMMENT '所需工具',
  `like_count` int(11) DEFAULT '0' COMMENT '点赞数',
  `view_count` int(11) DEFAULT '0' COMMENT '浏览数',
  `is_public` tinyint(1) DEFAULT '1' COMMENT '是否公开',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_difficulty` (`difficulty`),
  KEY `idx_like_count` (`like_count`),
  KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜谱表';

-- 插入示例菜谱数据
INSERT INTO `recipes` (`name`, `description`, `category`, `difficulty`, `cook_time`, `servings`, `ingredients`, `steps`, `tips`) VALUES
('西红柿鸡蛋', '经典家常菜，营养丰富', '家常菜', '简单', 15, 2, 
 '[{"name":"西红柿","quantity":"2","unit":"个"},{"name":"鸡蛋","quantity":"3","unit":"个"},{"name":"盐","quantity":"适量","unit":""},{"name":"糖","quantity":"少许","unit":""}]',
 '[{"step":1,"description":"鸡蛋打散，加少许盐"},{"step":2,"description":"西红柿切块"},{"step":3,"description":"热锅下油，炒鸡蛋盛起"},{"step":4,"description":"炒西红柿出汁，加糖调味"},{"step":5,"description":"倒入鸡蛋翻炒均匀即可"}]',
 '西红柿要炒出汁水，这样更香更好吃'),
('青椒肉丝', '下饭神器，简单易做', '家常菜', '简单', 20, 2,
 '[{"name":"青椒","quantity":"3","unit":"个"},{"name":"猪肉丝","quantity":"200","unit":"克"},{"name":"生抽","quantity":"2","unit":"勺"},{"name":"料酒","quantity":"1","unit":"勺"}]',
 '[{"step":1,"description":"肉丝用生抽和料酒腌制10分钟"},{"step":2,"description":"青椒切丝"},{"step":3,"description":"热锅下油，炒肉丝至变色"},{"step":4,"description":"加入青椒丝翻炒"},{"step":5,"description":"调味炒匀即可"}]',
 '肉丝要先腌制，这样更嫩更入味');

-- ----------------------------
-- 购物清单表
-- ----------------------------
DROP TABLE IF EXISTS `shopping_list`;
CREATE TABLE `shopping_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '购物项ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `quantity` decimal(10,2) DEFAULT '1.00' COMMENT '数量',
  `unit` varchar(20) DEFAULT '个' COMMENT '单位',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '预估价格',
  `actual_price` decimal(10,2) DEFAULT NULL COMMENT '实际价格',
  `notes` varchar(255) DEFAULT NULL COMMENT '备注',
  `is_purchased` tinyint(1) DEFAULT '0' COMMENT '是否已购买',
  `purchase_date` datetime DEFAULT NULL COMMENT '购买时间',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_is_purchased` (`is_purchased`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`category_id`) REFERENCES `ingredient_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='购物清单表';

-- ----------------------------
-- 用户收藏表
-- ----------------------------
DROP TABLE IF EXISTS `user_favorites`;
CREATE TABLE `user_favorites` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `recipe_id` int(11) NOT NULL COMMENT '菜谱ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_recipe` (`user_id`, `recipe_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_recipe_id` (`recipe_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`recipe_id`) REFERENCES `recipes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表';

-- ----------------------------
-- 用户记录表
-- ----------------------------
DROP TABLE IF EXISTS `user_records`;
CREATE TABLE `user_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` varchar(50) NOT NULL COMMENT '记录类型',
  `content` json DEFAULT NULL COMMENT '记录内容',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户记录表';

-- ----------------------------
-- 常用食材表
-- ----------------------------
DROP TABLE IF EXISTS `common_ingredients`;
CREATE TABLE `common_ingredients` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '食材ID',
  `name` varchar(100) NOT NULL COMMENT '食材名称',
  `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `unit` varchar(20) DEFAULT '个' COMMENT '默认单位',
  `shelf_life` int(11) DEFAULT NULL COMMENT '保质期(天)',
  `storage_tips` varchar(255) DEFAULT NULL COMMENT '储存建议',
  `nutrition_info` json DEFAULT NULL COMMENT '营养信息',
  `image` varchar(255) DEFAULT NULL COMMENT '图片',
  `usage_count` int(11) DEFAULT '0' COMMENT '使用次数',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_usage_count` (`usage_count`),
  FOREIGN KEY (`category_id`) REFERENCES `ingredient_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='常用食材表';

-- 插入常用食材数据
INSERT INTO `common_ingredients` (`name`, `category_id`, `unit`, `shelf_life`, `storage_tips`) VALUES
('西红柿', 1, '个', 7, '常温保存，避免阳光直射'),
('黄瓜', 1, '根', 5, '冷藏保存'),
('土豆', 1, '个', 30, '阴凉干燥处保存'),
('胡萝卜', 1, '根', 14, '冷藏保存'),
('白菜', 1, '棵', 10, '冷藏保存'),
('苹果', 2, '个', 14, '冷藏保存'),
('香蕉', 2, '根', 5, '常温保存'),
('橙子', 2, '个', 10, '常温保存'),
('猪肉', 3, '斤', 3, '冷藏或冷冻保存'),
('鸡肉', 3, '斤', 3, '冷藏或冷冻保存'),
('牛肉', 3, '斤', 3, '冷藏或冷冻保存'),
('鸡蛋', 5, '个', 21, '冷藏保存'),
('牛奶', 5, '盒', 7, '冷藏保存'),
('大米', 7, '斤', 365, '密封干燥保存'),
('面条', 7, '包', 180, '密封干燥保存');

SET FOREIGN_KEY_CHECKS = 1;

-- 创建索引优化查询性能
CREATE INDEX idx_user_ingredients_expire ON user_ingredients(user_id, expire_date, status);
CREATE INDEX idx_shopping_list_user_status ON shopping_list(user_id, is_purchased, is_deleted);
CREATE INDEX idx_recipes_public_category ON recipes(is_public, category, is_deleted);

-- 创建视图方便查询
CREATE VIEW v_user_ingredient_stats AS
SELECT 
    ui.user_id,
    ic.name as category_name,
    COUNT(*) as ingredient_count,
    SUM(CASE WHEN ui.status = 2 THEN 1 ELSE 0 END) as expiring_count,
    SUM(CASE WHEN ui.status = 3 THEN 1 ELSE 0 END) as expired_count
FROM user_ingredients ui
LEFT JOIN ingredient_categories ic ON ui.category_id = ic.id
WHERE ui.is_deleted = 0
GROUP BY ui.user_id, ui.category_id;

COMMIT;