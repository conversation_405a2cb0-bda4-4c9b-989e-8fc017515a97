/* 菜谱分析页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
  padding: 20rpx 30rpx 30rpx;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.action-icon:active {
  opacity: 0.6;
}

/* 时间范围选择 */
.time-range-tabs {
  display: flex;
  gap: 20rpx;
}

.time-tab {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.time-tab.active {
  background-color: white;
  color: #FF6B6B;
  font-weight: 600;
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
}

.analysis-content {
  padding: 30rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #FF6B6B;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 总体统计 */
.overview-section {
  margin-bottom: 30rpx;
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.overview-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.overview-item:active {
  transform: scale(0.95);
}

.overview-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  line-height: 1;
}

.overview-label {
  font-size: 24rpx;
  color: #999;
}

.overview-divider {
  width: 1rpx;
  height: 60rpx;
  background-color: #e9ecef;
  margin: 0 20rpx;
}

/* 图表类型切换 */
.chart-tabs {
  display: flex;
  background-color: white;
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.chart-tab {
  flex: 1;
  text-align: center;
  padding: 15rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.chart-tab.active {
  background-color: #FF6B6B;
  color: white;
  font-weight: 600;
}

/* 图表容器 */
.chart-section,
.activity-section {
  margin-bottom: 30rpx;
}

.chart-container,
.activity-container {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  gap: 20rpx;
}

.empty-icon {
  width: 80rpx;
  height: 80rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 分类统计 */
.category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.category-item:last-child {
  border-bottom: none;
}

.category-item:active {
  background-color: #f8f9fa;
  margin: 0 -30rpx;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.category-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 12rpx;
  background-color: #f8f9fa;
  padding: 8rpx;
  box-sizing: border-box;
}

.category-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.category-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.category-count {
  font-size: 24rpx;
  color: #999;
}

.category-progress {
  display: flex;
  align-items: center;
  gap: 15rpx;
  min-width: 150rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background-color: #e9ecef;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #FF6B6B;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  min-width: 50rpx;
  text-align: right;
}

/* 难度分布 */
.difficulty-chart {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 40rpx 0;
}

.difficulty-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  transition: all 0.3s ease;
}

.difficulty-item:active {
  transform: scale(0.95);
}

.difficulty-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.difficulty-number {
  font-size: 36rpx;
  font-weight: 700;
  color: white;
}

.difficulty-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.difficulty-percent {
  font-size: 24rpx;
  color: #999;
}

/* 用时分析 */
.time-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.time-item:last-child {
  border-bottom: none;
}

.time-item:active {
  background-color: #f8f9fa;
  margin: 0 -30rpx;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.time-range {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.time-count {
  font-size: 24rpx;
  color: #999;
}

.time-progress {
  display: flex;
  align-items: center;
  gap: 15rpx;
  min-width: 150rpx;
}

/* 热门菜谱 */
.popular-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.popular-item:last-child {
  border-bottom: none;
}

.popular-item:active {
  background-color: #f8f9fa;
  margin: 0 -30rpx;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
}

.popular-rank {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #FF6B6B;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.popular-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.popular-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.popular-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.popular-meta {
  display: flex;
  gap: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.popular-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  flex-shrink: 0;
}

.score-number {
  font-size: 32rpx;
  font-weight: 700;
  color: #FF6B6B;
}

.score-label {
  font-size: 22rpx;
  color: #999;
}

/* 最近活动 */
.activity-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:active {
  background-color: #f8f9fa;
  margin: 0 -30rpx;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
}

.activity-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 12rpx;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-icon image {
  width: 30rpx;
  height: 30rpx;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.activity-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.activity-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.activity-time {
  font-size: 24rpx;
  color: #999;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .analysis-content {
    padding: 20rpx;
  }
  
  .overview-card,
  .chart-container,
  .activity-container {
    padding: 25rpx;
  }
  
  .overview-number {
    font-size: 42rpx;
  }
  
  .overview-divider {
    margin: 0 15rpx;
  }
  
  .difficulty-circle {
    width: 100rpx;
    height: 100rpx;
  }
  
  .difficulty-number {
    font-size: 32rpx;
  }
  
  .page-header {
    padding: 15rpx 20rpx 25rpx;
  }
  
  .header-title {
    font-size: 32rpx;
  }
  
  .time-range-tabs {
    gap: 15rpx;
  }
  
  .time-tab {
    padding: 10rpx 20rpx;
    font-size: 24rpx;
  }
  
  .popular-item {
    gap: 15rpx;
  }
  
  .popular-image {
    width: 70rpx;
    height: 70rpx;
  }
  
  .popular-meta {
    gap: 15rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .overview-card,
  .chart-container,
  .activity-container {
    background-color: #2d2d2d;
  }
  
  .chart-tabs {
    background-color: #2d2d2d;
  }
  
  .chart-tab {
    color: #ccc;
  }
  
  .chart-tab.active {
    background-color: #FF6B6B;
    color: white;
  }
  
  .overview-number,
  .section-title,
  .category-name,
  .time-range,
  .popular-name,
  .activity-title {
    color: white;
  }
  
  .overview-label,
  .category-count,
  .time-count,
  .progress-text,
  .difficulty-label,
  .difficulty-percent,
  .popular-meta,
  .score-label,
  .activity-desc,
  .activity-time,
  .empty-text {
    color: #ccc;
  }
  
  .overview-divider {
    background-color: #404040;
  }
  
  .progress-bar {
    background-color: #404040;
  }
  
  .category-item:active,
  .time-item:active,
  .popular-item:active,
  .activity-item:active {
    background-color: #404040;
  }
  
  .category-icon,
  .activity-icon {
    background-color: #404040;
  }
}

/* 动画效果 */
.overview-card,
.chart-container,
.activity-container {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chart-tab {
  animation: fadeInScale 0.3s ease;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.difficulty-item {
  animation: bounceIn 0.4s ease;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.category-item,
.time-item,
.popular-item,
.activity-item {
  animation: slideInLeft 0.3s ease;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 进度条动画 */
.progress-fill {
  animation: progressFill 1s ease-out;
}

@keyframes progressFill {
  from {
    width: 0;
  }
}

/* 数字计数动画 */
.overview-number,
.difficulty-number,
.score-number {
  animation: countUp 0.8s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 排名徽章动画 */
.popular-rank {
  animation: rankBadge 0.5s ease-out;
}

@keyframes rankBadge {
  0% {
    opacity: 0;
    transform: scale(0) rotate(-180deg);
  }
  50% {
    transform: scale(1.2) rotate(-90deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* 活动图标动画 */
.activity-icon {
  animation: iconPulse 0.6s ease-out;
}

@keyframes iconPulse {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}