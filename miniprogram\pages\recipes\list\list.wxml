<!--菜谱列表页面-->
<view class="page-container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <view class="search-input-wrapper">
        <text class="search-icon emoji-icon">🔍</text>
        <input
          class="search-input"
          type="text"
          placeholder="搜索菜谱名称或食材"
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
          bindconfirm="onSearch"
        />
        <view class="search-actions">
          <view class="action-btn" bindtap="onSearchClear" wx:if="{{searchKeyword}}">
            <text class="action-icon emoji-icon">❌</text>
          </view>
          <view class="action-btn" bindtap="startVoiceSearch">
            <text class="action-icon emoji-icon">🎤</text>
          </view>
          <view class="action-btn" bindtap="scanToSearch">
            <text class="action-icon emoji-icon">📱</text>
          </view>
        </view>
      </view>
      <view class="filter-btn" bindtap="showFilterPanel">
        <text class="filter-icon">🔽</text>
      </view>
    </view>

    <!-- 分类标签 -->
    <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
      <view class="category-list">
        <view 
          class="category-item {{selectedCategory === item ? 'active' : ''}}"
          wx:for="{{categories}}"
          wx:key="*this"
          data-category="{{item}}"
          bindtap="onCategoryChange"
        >
          {{item}}
        </view>
      </view>
    </scroll-view>

    <!-- 排序选项 -->
    <view class="sort-section">
      <view class="sort-list">
        <view
          class="sort-item {{sortType === 'created_at' ? 'active' : ''}}"
          data-sort="latest"
          bindtap="onSortChange"
        >
          最新
        </view>
        <view
          class="sort-item {{sortType === 'like_count' ? 'active' : ''}}"
          data-sort="popular"
          bindtap="onSortChange"
        >
          热门
        </view>
        <view
          class="sort-item {{sortType === 'view_count' ? 'active' : ''}}"
          data-sort="rating"
          bindtap="onSortChange"
        >
          浏览量
        </view>
      </view>
    </view>
  </view>

  <!-- 菜谱列表 -->
  <view class="recipes-container">
    <view class="recipes-grid">
      <view 
        class="recipe-card"
        wx:for="{{recipes}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="viewRecipeDetail"
      >
        <view class="recipe-image">
          <image
            src="{{item.image_url || 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400&h=300&fit=crop&crop=center'}}"
            mode="aspectFill"
            class="recipe-img"
            lazy-load="true"
            binderror="onImageError"
            data-index="{{index}}"
          ></image>
          
          <!-- 收藏按钮 -->
          <view 
            class="favorite-btn {{item.is_favorited ? 'favorited' : ''}}"
            data-id="{{item.id}}"
            data-index="{{index}}"
            bindtap="toggleFavorite"
          >
            <text class="heart-icon">{{item.is_favorited ? '❤️' : '🤍'}}</text>
          </view>

          <!-- 难度标签 -->
          <view class="difficulty-badge difficulty-{{item.difficulty}}">
            {{item.difficulty === 'easy' ? '简单' : item.difficulty === 'medium' ? '中等' : '困难'}}
          </view>
        </view>

        <view class="recipe-info">
          <text class="recipe-name">{{item.name}}</text>
          <text class="recipe-desc">{{item.description}}</text>
          
          <view class="recipe-meta">
            <view
              class="meta-item clickable"
              data-tooltip="烹饪时间"
              data-value="{{item.cook_time}}分钟"
              bindtap="showTooltip"
            >
              <view class="meta-icon-wrapper">
                <text class="meta-icon emoji-icon">⏰</text>
              </view>
              <text class="meta-text">{{item.cook_time}}</text>
              <text class="meta-unit">分钟</text>
            </view>

            <view
              class="meta-item clickable"
              data-tooltip="用户评分"
              data-value="{{item.rating || '暂无评分'}}"
              bindtap="showTooltip"
            >
              <view class="meta-icon-wrapper">
                <text class="meta-icon emoji-icon">⭐</text>
              </view>
              <text class="meta-text">{{item.rating || '暂无'}}</text>
              <text class="meta-unit">评分</text>
            </view>

            <view
              class="meta-item clickable"
              data-tooltip="适合人数"
              data-value="{{item.servings}}人份"
              bindtap="showTooltip"
            >
              <view class="meta-icon-wrapper">
                <text class="meta-icon emoji-icon">👤</text>
              </view>
              <text class="meta-text">{{item.servings}}</text>
              <text class="meta-unit">人份</text>
            </view>
          </view>

          <view class="recipe-tags" wx:if="{{item.tags && item.tags.length > 0}}">
            <text 
              class="recipe-tag"
              wx:for="{{item.tags}}"
              wx:key="*this"
              wx:for-item="tag"
            >
              {{tag}}
            </text>
          </view>
        </view>

        <!-- 分享按钮 -->
        <view 
          class="share-btn"
          data-id="{{item.id}}"
          data-name="{{item.name}}"
          bindtap="shareRecipe"
        >
          <text class="emoji-icon">📤</text>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && !loading}}">
      <text class="load-more-text">上拉加载更多</text>
    </view>

    <!-- 加载中 -->
    <view class="loading-more" wx:if="{{loading && !refreshing}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && recipes.length > 0}}">
      <text class="no-more-text">没有更多菜谱了</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && recipes.length === 0}}">
      <text class="empty-image emoji-icon">🍳</text>
      <text class="empty-text">暂无菜谱</text>
      <text class="empty-desc">快来添加第一个菜谱吧</text>
      <button class="btn btn-primary empty-btn" bindtap="addNewRecipe">
        添加菜谱
      </button>
    </view>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel {{showFilter ? 'show' : ''}}" wx:if="{{showFilter}}">
    <view class="filter-content">
      <view class="filter-header">
        <text class="filter-title">筛选条件</text>
        <view class="filter-actions">
          <text class="filter-reset" bindtap="resetFilter">重置</text>
          <text class="filter-close" bindtap="hideFilterPanel">关闭</text>
        </view>
      </view>

      <view class="filter-section">
        <text class="filter-label">难度等级</text>
        <view class="filter-options">
          <view 
            class="filter-option {{filterOptions.difficulty === 'easy' ? 'active' : ''}}"
            data-type="difficulty"
            data-value="easy"
            bindtap="onFilterChange"
          >
            简单
          </view>
          <view 
            class="filter-option {{filterOptions.difficulty === 'medium' ? 'active' : ''}}"
            data-type="difficulty"
            data-value="medium"
            bindtap="onFilterChange"
          >
            中等
          </view>
          <view 
            class="filter-option {{filterOptions.difficulty === 'hard' ? 'active' : ''}}"
            data-type="difficulty"
            data-value="hard"
            bindtap="onFilterChange"
          >
            困难
          </view>
        </view>
      </view>

      <view class="filter-section">
        <text class="filter-label">制作时间</text>
        <view class="filter-options">
          <view 
            class="filter-option {{filterOptions.cookTime === '<30' ? 'active' : ''}}"
            data-type="cookTime"
            data-value="<30"
            bindtap="onFilterChange"
          >
            30分钟内
          </view>
          <view 
            class="filter-option {{filterOptions.cookTime === '30-60' ? 'active' : ''}}"
            data-type="cookTime"
            data-value="30-60"
            bindtap="onFilterChange"
          >
            30-60分钟
          </view>
          <view 
            class="filter-option {{filterOptions.cookTime === '>60' ? 'active' : ''}}"
            data-type="cookTime"
            data-value=">60"
            bindtap="onFilterChange"
          >
            60分钟以上
          </view>
        </view>
      </view>

      <view class="filter-section">
        <text class="filter-label">评分等级</text>
        <view class="filter-options">
          <view 
            class="filter-option {{filterOptions.rating === '>4' ? 'active' : ''}}"
            data-type="rating"
            data-value=">4"
            bindtap="onFilterChange"
          >
            4分以上
          </view>
          <view 
            class="filter-option {{filterOptions.rating === '>3' ? 'active' : ''}}"
            data-type="rating"
            data-value=">3"
            bindtap="onFilterChange"
          >
            3分以上
          </view>
          <view 
            class="filter-option {{filterOptions.rating === '>2' ? 'active' : ''}}"
            data-type="rating"
            data-value=">2"
            bindtap="onFilterChange"
          >
            2分以上
          </view>
        </view>
      </view>

      <view class="filter-footer">
        <button class="btn btn-primary btn-block" bindtap="applyFilter">
          应用筛选
        </button>
      </view>
    </view>
  </view>

  <!-- 筛选面板遮罩 -->
  <view 
    class="filter-mask {{showFilter ? 'show' : ''}}"
    bindtap="hideFilterPanel"
  ></view>

  <!-- 添加按钮 -->
  <view class="fab-btn" bindtap="addNewRecipe">
    <text class="fab-icon emoji-icon">➕</text>
  </view>

  <!-- 气泡提示 -->
  <view class="tooltip {{showTooltip ? 'show' : ''}}" wx:if="{{tooltipData}}">
    <view class="tooltip-content">
      <text class="tooltip-title">{{tooltipData.title}}</text>
      <text class="tooltip-value">{{tooltipData.value}}</text>
    </view>
    <view class="tooltip-arrow"></view>
  </view>

  <!-- 遮罩层 -->
  <view
    class="tooltip-mask {{showTooltip ? 'show' : ''}}"
    bindtap="hideTooltip"
    wx:if="{{showTooltip}}"
  ></view>
</view>