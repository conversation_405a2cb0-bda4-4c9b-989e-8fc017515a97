// 测试收藏API功能
const axios = require('axios');

const BASE_URL = 'http://127.0.0.1:3000/api';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

console.log('🧪 测试收藏API功能\n');

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// 测试检查收藏状态
async function testCheckFavoriteStatus() {
  console.log('1. 测试检查收藏状态...');
  try {
    const response = await api.get('/favorites/check/recipe/1');
    console.log('✅ 检查收藏状态成功:', response.data);
    return response.data.data.is_favorited;
  } catch (error) {
    console.error('❌ 检查收藏状态失败:', error.response?.data || error.message);
    return false;
  }
}

// 测试切换收藏状态
async function testToggleFavorite() {
  console.log('\n2. 测试切换收藏状态...');
  try {
    const response = await api.post('/favorites/toggle', {
      type: 'recipe',
      target_id: 1
    });
    console.log('✅ 切换收藏状态成功:', response.data);
    return response.data.data.is_favorited;
  } catch (error) {
    console.error('❌ 切换收藏状态失败:', error.response?.data || error.message);
    return null;
  }
}

// 主测试函数
async function runTests() {
  try {
    console.log('🔍 开始测试收藏API...\n');
    
    // 1. 检查初始收藏状态
    const initialStatus = await testCheckFavoriteStatus();
    console.log(`初始收藏状态: ${initialStatus ? '已收藏' : '未收藏'}`);
    
    // 2. 切换收藏状态
    const newStatus = await testToggleFavorite();
    if (newStatus !== null) {
      console.log(`切换后状态: ${newStatus ? '已收藏' : '未收藏'}`);
      
      // 3. 再次检查收藏状态确认
      console.log('\n3. 确认收藏状态变化...');
      const confirmedStatus = await testCheckFavoriteStatus();
      console.log(`确认后状态: ${confirmedStatus ? '已收藏' : '未收藏'}`);
      
      if (newStatus === confirmedStatus) {
        console.log('✅ 收藏状态变化一致');
      } else {
        console.log('❌ 收藏状态变化不一致');
      }
      
      // 4. 再次切换回原状态
      console.log('\n4. 测试再次切换...');
      const finalStatus = await testToggleFavorite();
      if (finalStatus !== null) {
        console.log(`最终状态: ${finalStatus ? '已收藏' : '未收藏'}`);
        
        if (finalStatus === initialStatus) {
          console.log('✅ 收藏状态成功恢复到初始状态');
        } else {
          console.log('❌ 收藏状态未能恢复到初始状态');
        }
      }
    }
    
    console.log('\n🎉 收藏API测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testCheckFavoriteStatus, testToggleFavorite, runTests };
