<!--食材列表页面-->
<view class="page-container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <image class="search-icon" src="/images/icons/search.png" mode="aspectFit"></image>
      <input 
        class="search-input" 
        type="text" 
        placeholder="搜索食材名称"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <view class="search-actions">
        <text class="search-clear" wx:if="{{searchKeyword}}" bindtap="onSearchClear">清空</text>
        <image class="filter-icon" src="/images/icons/filter.png" mode="aspectFit" bindtap="toggleFilter"></image>
      </view>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-section">
    <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
      <view class="category-list">
        <view 
          class="category-item {{currentCategory === item ? 'active' : ''}}"
          wx:for="{{categories}}"
          wx:key="*this"
          data-category="{{item}}"
          bindtap="onCategoryChange"
        >
          {{item}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 筛选器 -->
  <view class="filter-panel {{showFilter ? 'show' : ''}}" wx:if="{{showFilter}}">
    <view class="filter-section">
      <text class="filter-title">状态筛选</text>
      <view class="filter-options">
        <view 
          class="filter-option {{filterStatus === 'all' ? 'active' : ''}}"
          data-status="all"
          bindtap="onStatusFilterChange"
        >
          全部
        </view>
        <view 
          class="filter-option {{filterStatus === 'fresh' ? 'active' : ''}}"
          data-status="fresh"
          bindtap="onStatusFilterChange"
        >
          新鲜
        </view>
        <view 
          class="filter-option {{filterStatus === 'expiring' ? 'active' : ''}}"
          data-status="expiring"
          bindtap="onStatusFilterChange"
        >
          即将过期
        </view>
        <view 
          class="filter-option {{filterStatus === 'expired' ? 'active' : ''}}"
          data-status="expired"
          bindtap="onStatusFilterChange"
        >
          已过期
        </view>
      </view>
    </view>

    <view class="filter-section">
      <text class="filter-title">排序方式</text>
      <view class="sort-options">
        <view 
          class="sort-option {{sortBy === 'expire_date' ? 'active' : ''}}"
          data-sort="expire_date"
          bindtap="onSortChange"
        >
          按过期时间 {{sortBy === 'expire_date' ? (sortOrder === 'asc' ? '↑' : '↓') : ''}}
        </view>
        <view 
          class="sort-option {{sortBy === 'created_at' ? 'active' : ''}}"
          data-sort="created_at"
          bindtap="onSortChange"
        >
          按添加时间 {{sortBy === 'created_at' ? (sortOrder === 'asc' ? '↑' : '↓') : ''}}
        </view>
        <view 
          class="sort-option {{sortBy === 'name' ? 'active' : ''}}"
          data-sort="name"
          bindtap="onSortChange"
        >
          按名称 {{sortBy === 'name' ? (sortOrder === 'asc' ? '↑' : '↓') : ''}}
        </view>
      </view>
    </view>
  </view>

  <!-- 工具栏 -->
  <view class="toolbar">
    <view class="toolbar-left">
      <text class="ingredient-count">共 {{ingredients.length}} 个食材</text>
    </view>
    <view class="toolbar-right">
      <view class="batch-actions">
        <text class="batch-action" data-action="delete_expired" bindtap="onBatchAction">清理过期</text>
        <text class="batch-action" data-action="export" bindtap="onBatchAction">导出</text>
      </view>
    </view>
  </view>

  <!-- 食材列表 -->
  <view class="ingredients-list" wx:if="{{ingredients.length > 0}}">
    <view 
      class="ingredient-item"
      wx:for="{{ingredients}}"
      wx:key="id"
      data-id="{{item.id}}"
      bindtap="viewIngredientDetail"
    >
      <view class="ingredient-image">
        <image 
          src="{{item.image_url || '/images/default-ingredient.png'}}" 
          mode="aspectFill"
          class="ingredient-img"
        ></image>
        <view class="status-badge status-{{item.statusInfo.status}}">
          {{item.statusInfo.text}}
        </view>
      </view>

      <view class="ingredient-content">
        <view class="ingredient-header">
          <text class="ingredient-name">{{item.name}}</text>
          <text class="ingredient-category">{{item.category}}</text>
        </view>
        
        <view class="ingredient-details">
          <view class="detail-row">
            <text class="detail-label">数量:</text>
            <text class="detail-value">{{item.quantity}} {{item.unit}}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">过期时间:</text>
            <text class="detail-value expire-date" style="color: {{item.statusInfo.color}}">
              {{item.expire_date}}
            </text>
          </view>
          <view class="detail-row" wx:if="{{item.location}}">
            <text class="detail-label">存放位置:</text>
            <text class="detail-value">{{item.location}}</text>
          </view>
        </view>
      </view>

      <view class="ingredient-actions">
        <view 
          class="action-btn edit-btn"
          data-id="{{item.id}}"
          bindtap="editIngredient"
        >
          <image src="/images/icons/edit.png" mode="aspectFit"></image>
        </view>
        <view 
          class="action-btn delete-btn"
          data-id="{{item.id}}"
          data-name="{{item.name}}"
          bindtap="deleteIngredient"
        >
          <image src="/images/icons/delete.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && ingredients.length === 0}}">
    <image class="empty-icon" src="/images/empty-ingredients.png" mode="aspectFit"></image>
    <text class="empty-text">暂无食材记录</text>
    <text class="empty-desc">点击下方按钮添加您的第一个食材吧！</text>
    <button class="btn btn-primary" bindtap="addIngredient">添加食材</button>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading && ingredients.length === 0}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{loading && ingredients.length > 0}}">
    <text class="load-more-text">加载更多...</text>
  </view>

  <!-- 没有更多 -->
  <view class="no-more" wx:if="{{!hasMore && ingredients.length > 0}}">
    <text class="no-more-text">没有更多了</text>
  </view>

  <!-- 添加按钮 -->
  <view class="fab" bindtap="addIngredient">
    <image class="fab-icon" src="/images/icons/add.png" mode="aspectFit"></image>
  </view>
</view>