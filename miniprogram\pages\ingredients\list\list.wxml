<!--食材列表页面-->
<view class="page-container">
  <!-- 顶部导航栏 -->
  <view class="header-section">
    <view class="header-content">
      <view class="header-title">
        <text class="title-text">我的食材</text>
        <text class="subtitle-text">智能管理，新鲜生活</text>
      </view>
      <view class="header-stats">
        <view class="stat-item">
          <text class="stat-number">{{ingredients.length}}</text>
          <text class="stat-label">总数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{freshCount}}</text>
          <text class="stat-label">新鲜</text>
        </view>
        <view class="stat-item warning" wx:if="{{expiringCount > 0}}">
          <text class="stat-number">{{expiringCount}}</text>
          <text class="stat-label">即将过期</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 搜索和筛选栏 -->
  <view class="search-filter-section">
    <view class="search-container">
      <view class="search-bar">
        <view class="search-icon">🔍</view>
        <input
          class="search-input"
          type="text"
          placeholder="搜索食材名称、分类..."
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
          bindconfirm="onSearch"
        />
        <view class="search-clear" wx:if="{{searchKeyword}}" bindtap="onSearchClear">×</view>
      </view>
      <view class="filter-toggle {{showFilter ? 'active' : ''}}" bindtap="toggleFilter">
        <view class="filter-icon">⚙️</view>
      </view>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-section">
    <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
      <view class="category-list">
        <view
          class="category-chip {{currentCategory === 'all' ? 'active' : ''}}"
          data-category="all"
          bindtap="onCategoryChange"
        >
          <view class="chip-icon">🏠</view>
          <text class="chip-text">全部</text>
        </view>
        <view
          class="category-chip {{currentCategory === item ? 'active' : ''}}"
          wx:for="{{categories}}"
          wx:key="*this"
          data-category="{{item}}"
          bindtap="onCategoryChange"
        >
          <view class="chip-icon">{{getCategoryIcon(item)}}</view>
          <text class="chip-text">{{item}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 高级筛选器 -->
  <view class="filter-panel {{showFilter ? 'show' : ''}}" wx:if="{{showFilter}}">
    <view class="filter-overlay" bindtap="toggleFilter"></view>
    <view class="filter-content">
      <view class="filter-header">
        <text class="filter-title">筛选和排序</text>
        <view class="filter-close" bindtap="toggleFilter">×</view>
      </view>

      <view class="filter-body">
        <!-- 状态筛选 -->
        <view class="filter-group">
          <text class="group-title">食材状态</text>
          <view class="filter-chips">
            <view
              class="filter-chip {{filterStatus === 'all' ? 'active' : ''}}"
              data-status="all"
              bindtap="onStatusFilterChange"
            >
              <view class="chip-dot all"></view>
              <text>全部</text>
            </view>
            <view
              class="filter-chip {{filterStatus === 'fresh' ? 'active' : ''}}"
              data-status="fresh"
              bindtap="onStatusFilterChange"
            >
              <view class="chip-dot fresh"></view>
              <text>新鲜</text>
            </view>
            <view
              class="filter-chip {{filterStatus === 'expiring' ? 'active' : ''}}"
              data-status="expiring"
              bindtap="onStatusFilterChange"
            >
              <view class="chip-dot expiring"></view>
              <text>即将过期</text>
            </view>
            <view
              class="filter-chip {{filterStatus === 'expired' ? 'active' : ''}}"
              data-status="expired"
              bindtap="onStatusFilterChange"
            >
              <view class="chip-dot expired"></view>
              <text>已过期</text>
            </view>
          </view>
        </view>

        <!-- 排序方式 -->
        <view class="filter-group">
          <text class="group-title">排序方式</text>
          <view class="sort-chips">
            <view
              class="sort-chip {{sortBy === 'expire_date' ? 'active' : ''}}"
              data-sort="expire_date"
              bindtap="onSortChange"
            >
              <view class="sort-icon">📅</view>
              <text>过期时间</text>
              <view class="sort-arrow" wx:if="{{sortBy === 'expire_date'}}">
                {{sortOrder === 'asc' ? '↑' : '↓'}}
              </view>
            </view>
            <view
              class="sort-chip {{sortBy === 'created_at' ? 'active' : ''}}"
              data-sort="created_at"
              bindtap="onSortChange"
            >
              <view class="sort-icon">🕒</view>
              <text>添加时间</text>
              <view class="sort-arrow" wx:if="{{sortBy === 'created_at'}}">
                {{sortOrder === 'asc' ? '↑' : '↓'}}
              </view>
            </view>
            <view
              class="sort-chip {{sortBy === 'name' ? 'active' : ''}}"
              data-sort="name"
              bindtap="onSortChange"
            >
              <view class="sort-icon">🔤</view>
              <text>名称</text>
              <view class="sort-arrow" wx:if="{{sortBy === 'name'}}">
                {{sortOrder === 'asc' ? '↑' : '↓'}}
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="filter-footer">
        <button class="filter-reset" bindtap="resetFilters">重置</button>
        <button class="filter-apply" bindtap="applyFilters">应用</button>
      </view>
    </view>
  </view>

  <!-- 快捷操作栏 -->
  <view class="quick-actions" wx:if="{{ingredients.length > 0}}">
    <view class="quick-action" bindtap="onBatchAction" data-action="delete_expired">
      <view class="action-icon">🧹</view>
      <text class="action-text">清理过期</text>
    </view>
    <view class="quick-action" bindtap="onBatchAction" data-action="export">
      <view class="action-icon">📤</view>
      <text class="action-text">导出列表</text>
    </view>
    <view class="quick-action" bindtap="onBatchAction" data-action="shopping">
      <view class="action-icon">🛒</view>
      <text class="action-text">购物清单</text>
    </view>
  </view>

  <!-- 食材网格列表 -->
  <view class="ingredients-grid" wx:if="{{ingredients.length > 0}}">
    <view
      class="ingredient-card"
      wx:for="{{ingredients}}"
      wx:key="id"
      data-id="{{item.id}}"
      bindtap="viewIngredientDetail"
    >
      <!-- 卡片头部 -->
      <view class="card-header">
        <view class="ingredient-image">
          <image
            src="{{item.image_url}}"
            mode="aspectFill"
            class="ingredient-img"
            lazy-load
            binderror="onImageError"
            data-index="{{index}}"
          ></image>
          <view class="status-indicator status-{{item.statusInfo.status}}"></view>
        </view>
        <view class="card-actions">
          <view
            class="action-btn edit-btn"
            data-id="{{item.id}}"
            bindtap="editIngredient"
            catchtap="stopPropagation"
          >
            <view class="btn-icon">✏️</view>
          </view>
          <view
            class="action-btn delete-btn"
            data-id="{{item.id}}"
            data-name="{{item.name}}"
            bindtap="deleteIngredient"
            catchtap="stopPropagation"
          >
            <view class="btn-icon">🗑️</view>
          </view>
        </view>
      </view>

      <!-- 卡片内容 -->
      <view class="card-content">
        <view class="ingredient-info">
          <text class="ingredient-name">{{item.name}}</text>
          <view class="ingredient-meta">
            <view class="meta-item">
              <view class="meta-icon">🏷️</view>
              <text class="meta-text">{{item.category}}</text>
            </view>
            <view class="meta-item">
              <view class="meta-icon">📦</view>
              <text class="meta-text">{{item.quantity}} {{item.unit}}</text>
            </view>
          </view>
        </view>

        <view class="expire-info">
          <view class="expire-label">过期时间</view>
          <view class="expire-date status-{{item.statusInfo.status}}">
            {{item.statusInfo.text}}
          </view>
          <view class="expire-countdown" wx:if="{{item.statusInfo.days !== undefined}}">
            {{item.statusInfo.days > 0 ? item.statusInfo.days + '天后' : '已过期'}}
          </view>
        </view>

        <view class="location-info" wx:if="{{item.location}}">
          <view class="location-icon">📍</view>
          <text class="location-text">{{item.location}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && ingredients.length === 0}}">
    <view class="empty-illustration">
      <view class="empty-icon">🥬</view>
      <view class="empty-bg-circle"></view>
    </view>
    <view class="empty-content">
      <text class="empty-title">还没有食材记录</text>
      <text class="empty-desc">开始添加您的第一个食材，让智能管理帮您保持食材新鲜！</text>
      <view class="empty-features">
        <view class="feature-item">
          <view class="feature-icon">⏰</view>
          <text class="feature-text">过期提醒</text>
        </view>
        <view class="feature-item">
          <view class="feature-icon">📊</view>
          <text class="feature-text">智能统计</text>
        </view>
        <view class="feature-item">
          <view class="feature-icon">🛒</view>
          <text class="feature-text">购物建议</text>
        </view>
      </view>
      <button class="empty-action-btn" bindtap="addIngredient">
        <view class="btn-icon">+</view>
        <text class="btn-text">添加食材</text>
      </button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading && ingredients.length === 0}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载食材...</text>
  </view>

  <!-- 加载更多 -->
  <view class="load-more-state" wx:if="{{loading && ingredients.length > 0}}">
    <view class="load-more-spinner"></view>
    <text class="load-more-text">加载更多...</text>
  </view>

  <!-- 没有更多 -->
  <view class="no-more-state" wx:if="{{!hasMore && ingredients.length > 0}}">
    <view class="no-more-line"></view>
    <text class="no-more-text">已显示全部食材</text>
    <view class="no-more-line"></view>
  </view>

  <!-- 浮动操作按钮 -->
  <view class="fab-container" wx:if="{{ingredients.length > 0}}">
    <view class="fab-main" bindtap="addIngredient">
      <view class="fab-icon">+</view>
      <view class="fab-label">添加</view>
    </view>
  </view>
</view>