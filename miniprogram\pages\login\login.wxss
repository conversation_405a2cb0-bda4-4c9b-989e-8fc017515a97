/* 登录页面样式 */

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 80rpx 30rpx 40rpx;
  box-sizing: border-box;
  position: relative;
  align-items: center;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="80" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.login-header {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  font-size: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10rpx);
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 16rpx;
}

.app-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 28rpx;
  padding: 60rpx 50rpx;
  margin-bottom: 60rpx;
  width: 100%;
  max-width: 580rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

.form-group {
  margin-bottom: 36rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  border: 2rpx solid rgba(102, 126, 234, 0.2);
  border-radius: 20rpx;
  background: linear-gradient(145deg, #f8f9ff, #ffffff);
  transition: all 0.3s ease;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  height: 96rpx;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1), inset 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  transform: translateY(-2rpx);
}

.input-icon {
  width: 44rpx;
  height: 44rpx;
  margin: 0 24rpx 0 20rpx;
  opacity: 0.7;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.input-wrapper:focus-within .input-icon {
  opacity: 1;
  transform: scale(1.1);
}

.input-wrapper .form-input {
  flex: 1;
  padding: 0 20rpx;
  font-size: 32rpx !important;
  background-color: transparent;
  border: none !important;
  color: #333;
  font-weight: 500;
  line-height: 96rpx;
  height: 96rpx;
  box-sizing: border-box;
  width: auto !important;
}

.input-wrapper .form-input::placeholder {
  color: #999;
  font-weight: 400;
  font-size: 32rpx;
}

.password-toggle {
  width: 44rpx;
  height: 44rpx;
  margin: 0 20rpx;
  opacity: 0.6;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 50%;
  flex-shrink: 0;
}

.password-toggle:hover {
  opacity: 1;
  background: rgba(102, 126, 234, 0.1);
}

.login-btn {
  margin-top: 20rpx;
  padding: 36rpx;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
  border: none;
  color: white;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.login-btn:active::before {
  left: 100%;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40rpx;
}

.forgot-password,
.register-link {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 500;
  position: relative;
  transition: all 0.3s ease;
}

.forgot-password::after,
.register-link::after {
  content: '';
  position: absolute;
  bottom: -4rpx;
  left: 0;
  width: 0;
  height: 2rpx;
  background: #667eea;
  transition: width 0.3s ease;
}

.forgot-password:active::after,
.register-link:active::after {
  width: 100%;
}



.login-footer {
  text-align: center;
  margin-top: auto;
  padding-top: 40rpx;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.link-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
  text-decoration: underline;
  text-underline-offset: 4rpx;
  transition: all 0.3s ease;
}

.link-text:active {
  color: white;
  text-shadow: 0 0 8rpx rgba(255, 255, 255, 0.5);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .login-container {
    padding: 60rpx 20rpx 30rpx;
  }

  .login-form {
    padding: 50rpx 40rpx;
    max-width: 100%;
  }

  .login-header {
    margin-bottom: 60rpx;
  }

  .input-icon {
    margin: 0 20rpx 0 16rpx;
  }

  .form-input {
    padding: 28rpx 16rpx;
    font-size: 30rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .login-form {
    background-color: #2d2d2d;
  }
  
  .input-wrapper {
    background-color: #404040;
    border-color: #555;
  }
  
  .input-wrapper:focus-within {
    background-color: #2d2d2d;
  }
  
  .form-input {
    color: white;
  }
}