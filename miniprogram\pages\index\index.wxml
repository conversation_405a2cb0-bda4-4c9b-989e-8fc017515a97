<!--首页模板-->
<view class="page-container {{!isLogin ? 'login-page' : ''}}">
  <!-- 用户信息区域 -->
  <view class="user-section" wx:if="{{isLogin}}">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatar || defaultAvatar}}" mode="aspectFill" binderror="handleImageError"></image>
      <view class="user-details">
        <text class="username">{{userInfo.nickname || userInfo.phone}}</text>
        <text class="welcome-text">欢迎回来！</text>
      </view>
    </view>
    <view class="health-score">
      <view class="score-circle" style="border-color: {{getHealthScoreColor(dashboardData.health_score)}}">
        <text class="score-number">{{dashboardData.health_score}}</text>
      </view>
      <text class="score-text">健康度</text>
    </view>
  </view>

  <!-- 未登录状态 -->
  <view class="login-prompt" wx:if="{{!isLogin}}">
    <view class="prompt-icon">🔐</view>
    <text class="prompt-text">登录后查看您的食材管理数据</text>
    <button class="btn btn-primary" bindtap="goToLogin">立即登录</button>
  </view>

  <!-- 数据概览 -->
  <view class="dashboard-section" wx:if="{{isLogin}}">
    <view class="section-title">数据概览</view>
    
    <view class="stats-grid">
      <!-- 食材统计 -->
      <view class="stat-card" bindtap="viewIngredients">
        <view class="stat-header">
          <view class="stat-icon">🥬</view>
          <text class="stat-title">食材管理</text>
        </view>
        <view class="stat-content">
          <view class="stat-number">{{dashboardData.ingredients.total}}</view>
          <view class="stat-desc">总计食材</view>
          <view class="stat-detail" wx:if="{{dashboardData.ingredients.expiring_soon > 0}}">
            <text class="text-warning">{{dashboardData.ingredients.expiring_soon}}个即将过期</text>
          </view>
          <view class="stat-detail" wx:if="{{dashboardData.ingredients.expired > 0}}">
            <text class="text-danger">{{dashboardData.ingredients.expired}}个已过期</text>
          </view>
        </view>
      </view>

      <!-- 菜谱统计 -->
      <view class="stat-card" bindtap="viewRecipes">
        <view class="stat-header">
          <view class="stat-icon">📖</view>
          <text class="stat-title">菜谱收藏</text>
        </view>
        <view class="stat-content">
          <view class="stat-number">{{dashboardData.recipes.total}}</view>
          <view class="stat-desc">收藏菜谱</view>
          <view class="stat-detail">
            <text class="text-secondary">{{dashboardData.recipes.public}}个公开菜谱</text>
          </view>
        </view>
      </view>

      <!-- 购物统计 -->
      <view class="stat-card" bindtap="viewShopping">
        <view class="stat-header">
          <view class="stat-icon">🛒</view>
          <text class="stat-title">购物清单</text>
        </view>
        <view class="stat-content">
          <view class="stat-number">{{dashboardData.shopping.total_items}}</view>
          <view class="stat-desc">购物项目</view>
          <view class="stat-detail" wx:if="{{dashboardData.shopping.pending_items > 0}}">
            <text class="text-warning">{{dashboardData.shopping.pending_items}}个待购买</text>
          </view>
          <view class="stat-detail">
            <text class="text-secondary">总支出 ¥{{dashboardData.shopping.total_spent}}</text>
          </view>
        </view>
      </view>

      <!-- 健康分析 -->
      <view class="stat-card" bindtap="viewAnalysis">
        <view class="stat-header">
          <view class="stat-icon">📊</view>
          <text class="stat-title">健康分析</text>
        </view>
        <view class="stat-content">
          <view class="stat-number" style="color: {{getHealthScoreColor(dashboardData.health_score)}}">
            {{getHealthScoreText(dashboardData.health_score)}}
          </view>
          <view class="stat-desc">健康评级</view>
          <view class="stat-detail">
            <text class="text-secondary">评分 {{dashboardData.health_score}}/100</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions-section" wx:if="{{isLogin}}">
    <view class="section-title">快捷操作</view>
    
    <view class="actions-grid">
      <view 
        class="action-item" 
        wx:for="{{quickActions}}" 
        wx:key="id"
        data-url="{{item.url}}"
        bindtap="onQuickActionTap"
      >
        <view class="action-icon">{{item.iconEmoji}}</view>
        <text class="action-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 最近活动 -->
  <view class="activity-section" wx:if="{{isLogin && recentActivity.length > 0}}">
    <view class="section-header">
      <text class="section-title">最近活动</text>
      <text class="view-more" bindtap="viewMoreActivity">查看更多</text>
    </view>
    
    <view class="activity-list">
      <view 
        class="activity-item" 
        wx:for="{{recentActivity}}" 
        wx:key="id"
      >
        <view class="activity-icon">
          <view class="activity-icon-emoji">{{getActivityIcon(item.type)}}</view>
        </view>
        <view class="activity-content">
          <text class="activity-desc">{{item.description}}</text>
          <text class="activity-time">{{formatActivityDescription(item)}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{isLogin && !loading && recentActivity.length === 0}}">
    <view class="empty-icon">📝</view>
    <text class="empty-text">暂无活动记录</text>
    <text class="empty-desc">开始使用应用来记录您的食材管理活动吧！</text>
  </view>
</view>