// 分析WXML标签匹配情况
const fs = require('fs');

function analyzeWxmlTags(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`文件不存在: ${filePath}`);
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  console.log(`📁 分析文件: ${filePath}\n`);
  
  // 标签栈，用于跟踪嵌套
  const tagStack = [];
  const issues = [];
  
  lines.forEach((line, index) => {
    const lineNum = index + 1;
    const trimmed = line.trim();
    
    // 跳过注释和空行
    if (trimmed.startsWith('<!--') || trimmed === '') return;
    
    // 查找所有标签
    const tagRegex = /<\/?[\w-]+[^>]*>/g;
    let match;
    
    while ((match = tagRegex.exec(line)) !== null) {
      const tag = match[0];
      
      // 自闭合标签
      if (tag.endsWith('/>')) {
        console.log(`第${lineNum}行: 自闭合标签 ${tag}`);
        continue;
      }
      
      // 结束标签
      if (tag.startsWith('</')) {
        const tagName = tag.match(/<\/(\w+)/)[1];
        console.log(`第${lineNum}行: 结束标签 ${tag}`);
        
        if (tagStack.length === 0) {
          issues.push(`第${lineNum}行: 多余的结束标签 ${tag}`);
        } else {
          const lastTag = tagStack.pop();
          if (lastTag.name !== tagName) {
            issues.push(`第${lineNum}行: 标签不匹配，期望 </${lastTag.name}>，实际 ${tag}`);
          }
        }
      }
      // 开始标签
      else {
        const tagName = tag.match(/<(\w+)/)[1];
        console.log(`第${lineNum}行: 开始标签 ${tag}`);
        tagStack.push({ name: tagName, line: lineNum });
      }
    }
  });
  
  // 检查未闭合的标签
  if (tagStack.length > 0) {
    console.log('\n❌ 未闭合的标签:');
    tagStack.forEach(tag => {
      issues.push(`第${tag.line}行: 未闭合的标签 <${tag.name}>`);
      console.log(`  第${tag.line}行: <${tag.name}>`);
    });
  }
  
  // 显示问题总结
  console.log('\n📊 分析结果:');
  if (issues.length === 0) {
    console.log('✅ 所有标签都正确匹配');
  } else {
    console.log(`❌ 发现 ${issues.length} 个问题:`);
    issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`);
    });
  }
  
  return issues;
}

// 分析recipes/add页面
analyzeWxmlTags('miniprogram/pages/recipes/add/add.wxml');
