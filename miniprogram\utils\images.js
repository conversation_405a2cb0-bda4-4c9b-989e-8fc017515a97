// 图片资源配置
const IMAGE_CONFIG = {
  // 默认食材图片池
  defaultIngredients: [
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop&crop=center', // 蔬菜
    'https://images.unsplash.com/photo-1542838132-92c53300491e?w=200&h=200&fit=crop&crop=center', // 水果
    'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=200&h=200&fit=crop&crop=center', // 食材
    'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=200&h=200&fit=crop&crop=center', // 食物
    'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=200&h=200&fit=crop&crop=center'  // 食材
  ],

  // 按分类的默认图片
  categoryDefaults: {
    '蔬菜': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop&crop=center',
    '水果': 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=200&h=200&fit=crop&crop=center',
    '肉类': 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=200&h=200&fit=crop&crop=center',
    '海鲜': 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=200&h=200&fit=crop&crop=center',
    '蛋奶': 'https://images.unsplash.com/photo-1563636619-e9143da7973b?w=200&h=200&fit=crop&crop=center',
    '调料': 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=200&h=200&fit=crop&crop=center',
    '零食': 'https://images.unsplash.com/photo-1621939514649-280e2ee25f60?w=200&h=200&fit=crop&crop=center',
    '饮品': 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=200&h=200&fit=crop&crop=center',
    '其他': 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=200&h=200&fit=crop&crop=center'
  },

  // 状态指示图标
  statusIcons: {
    fresh: '🟢',
    expiring: '🟡', 
    expired: '🔴'
  }
};

/**
 * 获取默认食材图片
 * @param {string} category - 食材分类
 * @param {number} index - 索引（用于随机选择）
 * @returns {string} 图片URL
 */
function getDefaultIngredientImage(category = '', index = 0) {
  // 如果有分类对应的默认图片，使用分类图片
  if (category && IMAGE_CONFIG.categoryDefaults[category]) {
    return IMAGE_CONFIG.categoryDefaults[category];
  }
  
  // 否则从默认图片池中随机选择
  const imageIndex = index % IMAGE_CONFIG.defaultIngredients.length;
  return IMAGE_CONFIG.defaultIngredients[imageIndex];
}

/**
 * 获取备用图片（当主图片加载失败时使用）
 * @param {number} index - 索引
 * @returns {string} 备用图片URL
 */
function getFallbackImage(index = 0) {
  // 使用不同的图片作为备用
  const fallbackIndex = (index + 1) % IMAGE_CONFIG.defaultIngredients.length;
  return IMAGE_CONFIG.defaultIngredients[fallbackIndex];
}

/**
 * 获取状态图标
 * @param {string} status - 状态 (fresh/expiring/expired)
 * @returns {string} 状态图标
 */
function getStatusIcon(status) {
  return IMAGE_CONFIG.statusIcons[status] || '⚪';
}

/**
 * 预加载图片
 * @param {string} url - 图片URL
 * @returns {Promise} 预加载Promise
 */
function preloadImage(url) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(url);
    img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
    img.src = url;
  });
}

/**
 * 批量预加载图片
 * @param {Array<string>} urls - 图片URL数组
 * @returns {Promise<Array>} 预加载结果
 */
async function preloadImages(urls) {
  const results = await Promise.allSettled(
    urls.map(url => preloadImage(url))
  );
  
  return results.map((result, index) => ({
    url: urls[index],
    success: result.status === 'fulfilled',
    error: result.status === 'rejected' ? result.reason : null
  }));
}

module.exports = {
  IMAGE_CONFIG,
  getDefaultIngredientImage,
  getFallbackImage,
  getStatusIcon,
  preloadImage,
  preloadImages
};
