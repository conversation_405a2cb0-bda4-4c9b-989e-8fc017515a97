// 默认食材图片池
var defaultIngredients = [
  'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop&crop=center', // 蔬菜
  'https://images.unsplash.com/photo-1542838132-92c53300491e?w=200&h=200&fit=crop&crop=center', // 水果
  'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=200&h=200&fit=crop&crop=center', // 食材
  'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=200&h=200&fit=crop&crop=center', // 食物
  'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=200&h=200&fit=crop&crop=center'  // 食材
];

// 按分类的默认图片
var categoryDefaults = {
  '蔬菜': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop&crop=center',
  '水果': 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=200&h=200&fit=crop&crop=center',
  '肉类': 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=200&h=200&fit=crop&crop=center',
  '海鲜': 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=200&h=200&fit=crop&crop=center',
  '蛋奶': 'https://images.unsplash.com/photo-1563636619-e9143da7973b?w=200&h=200&fit=crop&crop=center',
  '调料': 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=200&h=200&fit=crop&crop=center',
  '零食': 'https://images.unsplash.com/photo-1621939514649-280e2ee25f60?w=200&h=200&fit=crop&crop=center',
  '饮品': 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=200&h=200&fit=crop&crop=center',
  '其他': 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=200&h=200&fit=crop&crop=center'
};

// 获取默认食材图片
function getDefaultIngredientImage(category, index) {
  category = category || '';
  index = index || 0;

  // 如果有分类对应的默认图片，使用分类图片
  if (category && categoryDefaults[category]) {
    return categoryDefaults[category];
  }

  // 否则从默认图片池中随机选择
  var imageIndex = index % defaultIngredients.length;
  return defaultIngredients[imageIndex];
}

// 获取备用图片（当主图片加载失败时使用）
function getFallbackImage(index) {
  index = index || 0;
  // 使用不同的图片作为备用
  var fallbackIndex = (index + 1) % defaultIngredients.length;
  return defaultIngredients[fallbackIndex];
}

// 获取状态图标
function getStatusIcon(status) {
  var statusIcons = {
    fresh: '🟢',
    expiring: '🟡',
    expired: '🔴'
  };
  return statusIcons[status] || '⚪';
}

module.exports = {
  getDefaultIngredientImage: getDefaultIngredientImage,
  getFallbackImage: getFallbackImage,
  getStatusIcon: getStatusIcon
};
