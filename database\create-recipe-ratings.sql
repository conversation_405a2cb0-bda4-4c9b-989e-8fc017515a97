-- 创建菜谱评分表
USE smart_fridge;

-- 创建菜谱评分表
DROP TABLE IF EXISTS `recipe_ratings`;
CREATE TABLE `recipe_ratings` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评分ID',
  `recipe_id` int(11) NOT NULL COMMENT '菜谱ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `rating` tinyint(1) NOT NULL COMMENT '评分(1-5)',
  `comment` text NULL COMMENT '评价内容',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评分时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_recipe_user` (`recipe_id`, `user_id`),
  KEY `idx_recipe_id` (`recipe_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_rating` (`rating`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜谱评分表';

-- 检查recipes表是否有rating和rating_count字段
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'smart_fridge' 
  AND TABLE_NAME = 'recipes' 
  AND COLUMN_NAME IN ('rating', 'rating_count');

-- 如果recipes表没有rating字段，添加它们
-- 注意：这些命令可能会失败如果字段已存在，这是正常的
ALTER TABLE recipes ADD COLUMN rating decimal(2,1) DEFAULT 0 COMMENT '平均评分';
ALTER TABLE recipes ADD COLUMN rating_count int(11) DEFAULT 0 COMMENT '评分数量';

-- 显示创建结果
DESCRIBE recipe_ratings;
DESCRIBE recipes;
