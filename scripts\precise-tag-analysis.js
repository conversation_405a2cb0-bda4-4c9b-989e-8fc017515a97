// 精确的WXML标签分析工具
const fs = require('fs');

function analyzeWxmlPrecisely(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log('❌ 文件不存在');
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  
  // 移除注释
  const cleanContent = content.replace(/<!--[\s\S]*?-->/g, '');
  
  // 标签栈
  const tagStack = [];
  const issues = [];
  
  // 更精确的标签匹配正则
  const tagRegex = /<\/?[\w-]+(?:\s[^>]*)?>/g;
  let match;
  let lineNumber = 1;
  let charIndex = 0;
  
  while ((match = tagRegex.exec(cleanContent)) !== null) {
    // 计算行号
    while (charIndex < match.index) {
      if (cleanContent[charIndex] === '\n') {
        lineNumber++;
      }
      charIndex++;
    }
    charIndex = match.index + match[0].length;
    
    const tag = match[0];
    
    // 跳过自闭合标签
    if (tag.endsWith('/>')) {
      console.log(`第${lineNumber}行: 自闭合标签 ${tag}`);
      continue;
    }
    
    // 结束标签
    if (tag.startsWith('</')) {
      const tagName = tag.match(/<\/(\w+)/)[1];
      console.log(`第${lineNumber}行: 结束标签 ${tag}`);
      
      if (tagStack.length === 0) {
        issues.push(`第${lineNumber}行: 多余的结束标签 ${tag}`);
      } else {
        const lastTag = tagStack.pop();
        if (lastTag.name !== tagName) {
          issues.push(`第${lineNumber}行: 标签不匹配，期望 </${lastTag.name}>，实际 ${tag} (开始于第${lastTag.line}行)`);
          // 将错误的标签放回栈中
          tagStack.push(lastTag);
        }
      }
    }
    // 开始标签
    else {
      const tagName = tag.match(/<(\w+)/)[1];
      console.log(`第${lineNumber}行: 开始标签 ${tag}`);
      tagStack.push({ name: tagName, line: lineNumber, tag: tag });
    }
  }
  
  // 检查未闭合的标签
  console.log('\n📋 未闭合的标签:');
  if (tagStack.length > 0) {
    tagStack.forEach(tag => {
      issues.push(`第${tag.line}行: 未闭合的标签 <${tag.name}>`);
      console.log(`  第${tag.line}行: ${tag.tag}`);
    });
  } else {
    console.log('  无');
  }
  
  // 显示问题总结
  console.log('\n📊 分析结果:');
  if (issues.length === 0) {
    console.log('✅ 所有标签都正确匹配');
  } else {
    console.log(`❌ 发现 ${issues.length} 个问题:`);
    issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`);
    });
  }
  
  return { tagStack, issues };
}

// 分析recipes/add页面
console.log('🔍 精确分析recipes/add页面标签匹配...\n');
analyzeWxmlPrecisely('miniprogram/pages/recipes/add/add.wxml');
