// 强力修复所有emoji图标问题
const fs = require('fs');
const path = require('path');

// 需要修复的文件和对应的emoji
const FILES_TO_FIX = [
  {
    file: 'miniprogram/pages/analysis/dashboard/dashboard.wxml',
    emojis: ['🛒', '⚠️', '🤍', '🥬']
  },
  {
    file: 'miniprogram/pages/analysis/ingredients/ingredients.wxml',
    emojis: ['⚠️']
  },
  {
    file: 'miniprogram/pages/analysis/recipes/recipes.wxml',
    emojis: ['📤']
  },
  {
    file: 'miniprogram/pages/favorites/list/list.wxml',
    emojis: ['✏️']
  },
  {
    file: 'miniprogram/pages/recipes/detail/detail.wxml',
    emojis: ['⏰', '✅', '📤', '✏️']
  }
];

function fixFile(filePath, emojis) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`❌ 文件不存在: ${filePath}`);
      return false;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    emojis.forEach(emoji => {
      // 多种可能的image标签模式
      const patterns = [
        `<image class="[^"]*" src="${emoji}"[^>]*>`,
        `<image src="${emoji}"[^>]*>`,
        `<image[^>]*src="${emoji}"[^>]*>`,
        `<image[^>]*class="[^"]*"[^>]*src="${emoji}"[^>]*>`
      ];
      
      patterns.forEach(pattern => {
        const regex = new RegExp(pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
        if (regex.test(content)) {
          content = content.replace(regex, `<text class="emoji-icon">${emoji}</text>`);
          modified = true;
          console.log(`  ✅ 修复 ${emoji} 在 ${path.basename(filePath)}`);
        }
      });
      
      // 更通用的替换方式
      const simplePattern = `src="${emoji}"`;
      if (content.includes(simplePattern)) {
        // 找到所有包含这个emoji的image标签
        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
          if (lines[i].includes(simplePattern) && lines[i].includes('<image')) {
            // 提取class属性
            const classMatch = lines[i].match(/class="([^"]*)"/);
            let className = classMatch ? classMatch[1] : '';
            if (className && !className.includes('emoji-icon')) {
              className += ' emoji-icon';
            } else if (!className) {
              className = 'emoji-icon';
            }
            
            // 替换整行
            lines[i] = lines[i].replace(/<image[^>]*>/, `<text class="${className}">${emoji}</text>`);
            modified = true;
            console.log(`  ✅ 修复 ${emoji} 在 ${path.basename(filePath)} 第${i+1}行`);
          }
        }
        content = lines.join('\n');
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复文件: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  文件无需修复: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 修复文件失败: ${filePath}`, error.message);
    return false;
  }
}

function main() {
  console.log('🔧 开始强力修复emoji图标问题...\n');
  
  let totalFiles = 0;
  let fixedFiles = 0;
  
  FILES_TO_FIX.forEach(({ file, emojis }) => {
    totalFiles++;
    console.log(`\n📁 处理文件: ${file}`);
    console.log(`🎯 目标emoji: ${emojis.join(', ')}`);
    
    if (fixFile(file, emojis)) {
      fixedFiles++;
    }
  });
  
  console.log(`\n📊 修复结果:`);
  console.log(`  总文件数: ${totalFiles}`);
  console.log(`  修复文件数: ${fixedFiles}`);
  console.log(`  跳过文件数: ${totalFiles - fixedFiles}`);
  
  if (fixedFiles > 0) {
    console.log('\n✅ 修复完成！');
    console.log('\n💡 建议:');
    console.log('1. 重新编译小程序项目');
    console.log('2. 测试所有页面的图标显示');
    console.log('3. 运行验证脚本确认修复效果');
  } else {
    console.log('\n✅ 所有文件都已是最新状态');
  }
}

// 验证修复结果
function verify() {
  console.log('🔍 验证修复结果...\n');
  
  const EMOJIS = ['⋯', '📅', '🛒', '📝', '⏰', '🔔', '📋', '📜', '✅', '⚠️', '🤍', '❤️', '📤', '✏️', '🥬'];
  let foundIssues = 0;
  
  FILES_TO_FIX.forEach(({ file }) => {
    if (!fs.existsSync(file)) return;
    
    try {
      const content = fs.readFileSync(file, 'utf8');
      
      EMOJIS.forEach(emoji => {
        const pattern = new RegExp(`<image[^>]*src="${emoji.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"[^>]*>`, 'g');
        if (pattern.test(content)) {
          console.log(`❌ 仍有问题: ${file} -> ${emoji}`);
          foundIssues++;
        }
      });
    } catch (error) {
      console.error(`验证失败: ${file}`, error.message);
    }
  });
  
  if (foundIssues === 0) {
    console.log('✅ 验证通过，所有emoji图标已正确修复');
  } else {
    console.log(`⚠️  仍有 ${foundIssues} 个问题需要手动检查`);
  }
}

// 命令行参数处理
const command = process.argv[2];

if (command === 'verify') {
  verify();
} else {
  main();
}

module.exports = { main, verify, FILES_TO_FIX };
