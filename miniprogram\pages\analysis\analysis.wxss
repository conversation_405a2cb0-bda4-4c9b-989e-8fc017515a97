/* 数据分析页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.time-selector {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
}

.time-picker {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.time-text {
  font-size: 26rpx;
  color: white;
}

.arrow-icon {
  width: 20rpx;
  height: 20rpx;
  filter: brightness(0) invert(1);
}

.action-buttons {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.3s ease;
}

.action-btn:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.action-btn image {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

/* 标签页导航 */
.tab-nav {
  background-color: white;
  display: flex;
  border-bottom: 1rpx solid #e0e0e0;
}

.tab-item {
  flex: 1;
  padding: 30rpx 20rpx;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #667eea;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #667eea;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
  padding: 20rpx;
}

.tab-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 通用卡片样式 */
.analysis-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.detail-btn {
  background-color: transparent;
  border: 2rpx solid #667eea;
  color: #667eea;
  border-radius: 20rpx;
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.detail-btn:active {
  background-color: #667eea;
  color: white;
}

/* 健康评分卡片 */
.health-score-card {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  gap: 40rpx;
  color: white;
  box-shadow: 0 4rpx 20rpx rgba(76, 175, 80, 0.3);
}

.score-circle {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  flex-shrink: 0;
}

.score-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.score-number {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  line-height: 1;
}

.score-label {
  display: block;
  font-size: 22rpx;
  opacity: 0.9;
  margin-top: 8rpx;
}

.score-ring {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(
    white 0deg,
    white calc(var(--score) * 3.6deg),
    rgba(255, 255, 255, 0.3) calc(var(--score) * 3.6deg),
    rgba(255, 255, 255, 0.3) 360deg
  );
  padding: 8rpx;
  box-sizing: border-box;
}

.score-ring::before {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #4CAF50;
}

.score-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.health-level {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  text-align: center;
  font-weight: 600;
  font-size: 28rpx;
  background-color: rgba(255, 255, 255, 0.2);
  width: fit-content;
}

.suggestions-btn {
  background-color: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  color: white;
  border-radius: 20rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.suggestions-btn:active {
  background-color: rgba(255, 255, 255, 0.3);
}

/* 营养分析 */
.nutrition-chart {
  height: 400rpx;
  margin-bottom: 30rpx;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

.nutrition-stats {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.nutrition-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.nutrition-color {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.protein-color { background-color: #FF6B6B; }
.carbs-color { background-color: #4ECDC4; }
.fat-color { background-color: #45B7D1; }
.vitamins-color { background-color: #96CEB4; }
.minerals-color { background-color: #FFEAA7; }

.nutrition-label {
  flex: 1;
  font-size: 26rpx;
  color: #666;
}

.nutrition-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 过期统计 */
.expiry-stats {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.expiry-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 30rpx 20rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.expiry-item.fresh {
  background-color: rgba(76, 175, 80, 0.1);
}

.expiry-item.warning {
  background-color: rgba(255, 193, 7, 0.1);
}

.expiry-item.expired {
  background-color: rgba(244, 67, 54, 0.1);
}

.expiry-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expiry-icon image {
  width: 40rpx;
  height: 40rpx;
}

.expiry-info {
  text-align: center;
}

.expiry-number {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  line-height: 1;
}

.expiry-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

/* 消费概览 */
.expense-overview {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.overview-item {
  text-align: center;
  flex: 1;
}

.overview-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.overview-value {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
}

.overview-value.primary {
  color: #667eea;
  font-size: 36rpx;
}

/* 消费趋势图 */
.expense-chart {
  height: 400rpx;
}

/* 分类消费 */
.category-expenses {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.category-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.category-icon image {
  width: 28rpx;
  height: 28rpx;
  filter: brightness(0) invert(1);
}

.category-name {
  flex: 1;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.category-amount {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
}

.amount-value {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.amount-percent {
  font-size: 22rpx;
  color: #666;
}

.category-bar {
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.5s ease;
}

/* 热门分类 */
.top-categories {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.top-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.rank-number {
  width: 40rpx;
  height: 40rpx;
  background-color: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.top-item .category-info {
  flex: 1;
}

.category-count {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
}

.top-item .category-amount {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 使用概览 */
.usage-overview {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.overview-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  line-height: 1;
}

.stat-number.used {
  color: #4CAF50;
}

.stat-number.wasted {
  color: #FF5722;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.usage-rates {
  display: flex;
  gap: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.rate-item {
  flex: 1;
  text-align: center;
}

.rate-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.rate-value {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
}

.rate-value.good {
  color: #4CAF50;
}

.rate-value.bad {
  color: #FF5722;
}

/* 使用率饼图 */
.usage-chart {
  height: 400rpx;
  margin-bottom: 30rpx;
}

.usage-legend {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.legend-color {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.used-color { background-color: #4CAF50; }
.wasted-color { background-color: #FF5722; }
.remaining-color { background-color: #FFC107; }

.legend-label {
  flex: 1;
  font-size: 24rpx;
  color: #666;
}

.legend-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

/* 月度使用趋势 */
.monthly-usage {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.month-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.month-label {
  width: 80rpx;
  font-size: 24rpx;
  color: #666;
  flex-shrink: 0;
}

.usage-bars {
  flex: 1;
}

.usage-bar {
  height: 20rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
  display: flex;
}

.bar-segment {
  height: 100%;
}

.bar-segment.used {
  background-color: #4CAF50;
}

.bar-segment.wasted {
  background-color: #FF5722;
}

.usage-total {
  width: 60rpx;
  text-align: right;
  font-size: 24rpx;
  color: #666;
  flex-shrink: 0;
}

/* 分类使用情况 */
.category-usage {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.usage-category {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.usage-category .category-name {
  flex: 1;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.category-rate {
  font-size: 24rpx;
  color: #666;
}

.category-progress {
  margin-bottom: 16rpx;
}

.progress-bar {
  height: 8rpx;
  background-color: #e0e0e0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.5s ease;
}

.category-stats {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.stat-text {
  font-size: 22rpx;
  color: #666;
}

/* 底部操作栏 */
.bottom-actions {
  background-color: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #e0e0e0;
  display: flex;
  gap: 20rpx;
}

.action-button {
  flex: 1;
  background-color: #f8f9fa;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.action-button:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

.action-button image {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.7;
}

.action-button text {
  font-size: 24rpx;
  color: #666;
}

/* 分享画布 */
.share-canvas {
  position: fixed;
  top: -2000rpx;
  left: -2000rpx;
  z-index: -1;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-container {
  background-color: white;
  border-radius: 16rpx;
  padding: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .page-header {
    padding: 15rpx 20rpx;
  }
  
  .page-title {
    font-size: 32rpx;
  }
  
  .content-scroll {
    padding: 15rpx;
  }
  
  .analysis-card {
    padding: 20rpx;
  }
  
  .health-score-card {
    padding: 30rpx 20rpx;
    gap: 30rpx;
  }
  
  .score-circle {
    width: 120rpx;
    height: 120rpx;
  }
  
  .score-number {
    font-size: 40rpx;
  }
  
  .expense-overview {
    padding: 20rpx;
  }
  
  .overview-value {
    font-size: 28rpx;
  }
  
  .overview-value.primary {
    font-size: 32rpx;
  }
  
  .expiry-stats {
    gap: 15rpx;
  }
  
  .expiry-item {
    padding: 20rpx 15rpx;
  }
  
  .expiry-number {
    font-size: 32rpx;
  }
  
  .stat-number {
    font-size: 32rpx;
  }
  
  .rate-value {
    font-size: 28rpx;
  }
  
  .bottom-actions {
    padding: 15rpx 20rpx;
    gap: 15rpx;
  }
  
  .action-button {
    padding: 15rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .tab-nav,
  .analysis-card,
  .expense-overview,
  .usage-overview,
  .bottom-actions,
  .loading-container {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .card-title,
  .overview-value,
  .stat-number,
  .rate-value,
  .nutrition-value,
  .expiry-number,
  .category-name,
  .amount-value {
    color: white;
  }
  
  .overview-label,
  .stat-label,
  .rate-label,
  .nutrition-label,
  .expiry-label,
  .amount-percent,
  .category-count,
  .category-rate,
  .stat-text {
    color: #999;
  }
  
  .tab-item {
    color: #ccc;
  }
  
  .tab-item.active {
    color: #667eea;
  }
  
  .action-button {
    background-color: #404040;
  }
  
  .action-button:active {
    background-color: #555;
  }
  
  .action-button text {
    color: #ccc;
  }
  
  .top-item,
  .usage-category {
    background-color: #404040;
  }
  
  .category-bar,
  .usage-bar,
  .progress-bar {
    background-color: #555;
  }
  
  .loading-text {
    color: #ccc;
  }
}

/* 动画效果 */
.analysis-card,
.expense-overview,
.usage-overview {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 图表动画 */
.bar-fill,
.progress-fill {
  animation: fillBar 1s ease-out;
}

@keyframes fillBar {
  from {
    width: 0;
  }
}

/* 评分圆环动画 */
.score-ring {
  animation: fillRing 2s ease-out;
}

@keyframes fillRing {
  from {
    background: conic-gradient(
      white 0deg,
      white 0deg,
      rgba(255, 255, 255, 0.3) 0deg,
      rgba(255, 255, 255, 0.3) 360deg
    );
  }
}