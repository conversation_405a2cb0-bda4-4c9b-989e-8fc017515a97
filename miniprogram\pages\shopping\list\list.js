// 购物清单列表页面逻辑
const app = getApp();

Page({
  data: {
    shoppingList: [],
    loading: false,
    refreshing: false,
    selectedItems: [],
    editMode: false,
    totalAmount: 0,
    completedCount: 0,
    categories: {},
    showCompleted: true,
    sortType: 'category_id' // category_id, name, created_at
  },

  onLoad() {
    console.log('购物清单页面加载');
    this.loadShoppingList();
  },

  onShow() {
    // 检查是否需要刷新数据
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    if (currentPage.data.needRefresh) {
      this.loadShoppingList();
      this.setData({ needRefresh: false });
    }
  },

  onPullDownRefresh() {
    this.loadShoppingList();
  },

  // 加载购物清单
  async loadShoppingList() {
    this.setData({ loading: true, refreshing: true });

    try {
      const res = await app.request({
        url: '/shopping',
        data: {
          sort: this.data.sortType,
          include_completed: this.data.showCompleted
        }
      });

      const shoppingList = this.processShoppingData(res.data.shopping_list || []);
      const categories = this.groupByCategory(shoppingList);
      const stats = this.calculateStats(shoppingList);

      this.setData({
        shoppingList,
        categories,
        totalAmount: stats.totalAmount,
        totalAmountText: stats.totalAmount.toFixed(2),
        completedCount: stats.completedCount,
        loading: false,
        refreshing: false
      });

      wx.stopPullDownRefresh();

    } catch (error) {
      console.error('加载购物清单失败:', error);
      app.showError('加载失败');
      this.setData({
        loading: false,
        refreshing: false
      });
      wx.stopPullDownRefresh();
    }
  },

  // 处理购物清单数据
  processShoppingData(data) {
    return data.map(item => ({
      ...item,
      totalPrice: item.price ? (item.price * item.quantity).toFixed(2) : '0.00'
    }));
  },

  // 按分类分组
  groupByCategory(items) {
    const categories = {};
    items.forEach(item => {
      const category = item.category || '其他';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(item);
    });
    return categories;
  },

  // 计算统计信息
  calculateStats(items) {
    let totalAmount = 0;
    let completedCount = 0;

    items.forEach(item => {
      if (item.price) {
        totalAmount += item.price * item.quantity;
      }
      if (item.is_purchased) {
        completedCount++;
      }
    });

    return { totalAmount, completedCount };
  },

  // 切换完成状态
  async toggleItemStatus(e) {
    const { id, index } = e.currentTarget.dataset;
    const item = this.data.shoppingList[index];

    try {
      await app.request({
        url: `/shopping/${id}`,
        method: 'PUT',
        data: {
          is_purchased: !item.is_purchased,
          purchased_at: !item.is_purchased ? new Date().toISOString() : null
        }
      });

      // 更新本地状态
      const shoppingList = [...this.data.shoppingList];
      shoppingList[index].is_purchased = !shoppingList[index].is_purchased;
      shoppingList[index].purchased_at = !shoppingList[index].is_purchased ? 
        new Date().toISOString() : null;

      const categories = this.groupByCategory(shoppingList);
      const stats = this.calculateStats(shoppingList);

      this.setData({
        shoppingList,
        categories,
        totalAmount: stats.totalAmount,
        totalAmountText: stats.totalAmount.toFixed(2),
        completedCount: stats.completedCount
      });

      // 震动反馈
      wx.vibrateShort();

    } catch (error) {
      console.error('更新状态失败:', error);
      app.showError('操作失败');
    }
  },

  // 进入编辑模式
  enterEditMode() {
    this.setData({
      editMode: true,
      selectedItems: []
    });
  },

  // 退出编辑模式
  exitEditMode() {
    this.setData({
      editMode: false,
      selectedItems: []
    });
  },

  // 选择/取消选择项目
  toggleSelectItem(e) {
    const id = e.currentTarget.dataset.id;
    const selectedItems = [...this.data.selectedItems];
    const index = selectedItems.indexOf(id);

    if (index > -1) {
      selectedItems.splice(index, 1);
    } else {
      selectedItems.push(id);
    }

    this.setData({ selectedItems });
  },

  // 全选/取消全选
  toggleSelectAll() {
    const { selectedItems, shoppingList } = this.data;
    const allIds = shoppingList.map(item => item.id);
    
    if (selectedItems.length === allIds.length) {
      this.setData({ selectedItems: [] });
    } else {
      this.setData({ selectedItems: allIds });
    }
  },

  // 批量删除
  async batchDelete() {
    const { selectedItems } = this.data;
    
    if (selectedItems.length === 0) {
      app.showError('请选择要删除的项目');
      return;
    }

    const confirmed = await app.showConfirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedItems.length} 个项目吗？`
    });

    if (!confirmed) return;

    try {
      await app.request({
        url: '/shopping/batch',
        method: 'DELETE',
        data: { ids: selectedItems }
      });

      app.showSuccess('删除成功');
      this.exitEditMode();
      this.loadShoppingList();

    } catch (error) {
      console.error('批量删除失败:', error);
      app.showError('删除失败');
    }
  },

  // 批量标记为已购买
  async batchMarkPurchased() {
    const { selectedItems } = this.data;
    
    if (selectedItems.length === 0) {
      app.showError('请选择要标记的项目');
      return;
    }

    try {
      await app.request({
        url: '/shopping/batch',
        method: 'PUT',
        data: {
          ids: selectedItems,
          is_purchased: true,
          purchased_at: new Date().toISOString()
        }
      });

      app.showSuccess('标记成功');
      this.exitEditMode();
      this.loadShoppingList();

    } catch (error) {
      console.error('批量标记失败:', error);
      app.showError('操作失败');
    }
  },

  // 删除单个项目
  async deleteItem(e) {
    const { id, name } = e.currentTarget.dataset;

    const confirmed = await app.showConfirm({
      title: '确认删除',
      content: `确定要删除"${name}"吗？`
    });

    if (!confirmed) return;

    try {
      await app.request({
        url: `/shopping/${id}`,
        method: 'DELETE'
      });

      app.showSuccess('删除成功');
      this.loadShoppingList();

    } catch (error) {
      console.error('删除失败:', error);
      app.showError('删除失败');
    }
  },

  // 编辑项目
  editItem(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/shopping/add/add?id=${id}`
    });
  },

  // 添加新项目
  addNewItem() {
    wx.navigateTo({
      url: '/pages/shopping/add/add'
    });
  },

  // 切换显示已完成项目
  toggleShowCompleted() {
    this.setData({
      showCompleted: !this.data.showCompleted
    });
    this.loadShoppingList();
  },

  // 切换排序方式
  changeSortType(e) {
    const sortType = e.currentTarget.dataset.sort;
    // 映射前端显示名称到后端字段名
    const sortMapping = {
      'category': 'category_id',
      'name': 'name',
      'date': 'created_at'
    };
    const actualSortType = sortMapping[sortType] || 'created_at';
    this.setData({ sortType: actualSortType });
    this.loadShoppingList();
  },

  // 清空已完成项目
  async clearCompleted() {
    const completedItems = this.data.shoppingList.filter(item => item.is_purchased);
    
    if (completedItems.length === 0) {
      app.showError('没有已完成的项目');
      return;
    }

    const confirmed = await app.showConfirm({
      title: '确认清空',
      content: `确定要清空 ${completedItems.length} 个已完成的项目吗？`
    });

    if (!confirmed) return;

    try {
      const ids = completedItems.map(item => item.id);
      await app.request({
        url: '/shopping/batch',
        method: 'DELETE',
        data: { ids }
      });

      app.showSuccess('清空成功');
      this.loadShoppingList();

    } catch (error) {
      console.error('清空失败:', error);
      app.showError('清空失败');
    }
  },

  // 智能添加（基于食材推荐）
  smartAdd() {
    wx.navigateTo({
      url: '/pages/shopping/smart/smart'
    });
  },

  // 导出购物清单
  exportList() {
    const { shoppingList } = this.data;
    let content = '购物清单\n\n';
    
    const categories = this.groupByCategory(shoppingList);
    Object.keys(categories).forEach(category => {
      content += `${category}:\n`;
      categories[category].forEach(item => {
        const status = item.is_purchased ? '✓' : '○';
        content += `${status} ${item.name} ${item.quantity}${item.unit}\n`;
      });
      content += '\n';
    });

    wx.setClipboardData({
      data: content,
      success: () => {
        app.showSuccess('购物清单已复制到剪贴板');
      }
    });
  },

  // 分享购物清单
  shareList() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 显示操作菜单
  showActionSheet() {
    const itemList = ['智能添加', '导出清单', '清空已完成', '分享清单'];
    
    wx.showActionSheet({
      itemList,
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.smartAdd();
            break;
          case 1:
            this.exportList();
            break;
          case 2:
            this.clearCompleted();
            break;
          case 3:
            this.shareList();
            break;
        }
      }
    });
  },

  // 分享给好友
  onShareAppMessage() {
    const { shoppingList, totalAmount } = this.data;
    return {
      title: `我的购物清单 (${shoppingList.length}项)`,
      desc: `预计消费: ¥${totalAmount.toFixed(2)}`,
      path: '/pages/shopping/list/list',
      imageUrl: '/images/share-shopping.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '我的购物清单',
      query: '',
      imageUrl: '/images/share-shopping.png'
    };
  }
});