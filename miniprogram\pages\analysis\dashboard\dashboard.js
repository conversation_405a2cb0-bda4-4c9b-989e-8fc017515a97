// 数据分析仪表板页面
const app = getApp();

Page({
  data: {
    // 统计数据
    stats: {
      totalIngredients: 0,
      expiringSoon: 0,
      totalRecipes: 0,
      favoriteRecipes: 0,
      totalShopping: 0,
      completedShopping: 0,
      monthlySpending: 0,
      healthScore: 0
    },
    
    // 图表数据
    chartData: {
      ingredientTrend: [],
      categoryDistribution: [],
      spendingTrend: [],
      healthTrend: []
    },
    
    // 页面状态
    loading: false,
    refreshing: false,
    
    // 时间范围
    timeRanges: [
      { id: 'week', name: '本周' },
      { id: 'month', name: '本月' },
      { id: 'quarter', name: '本季度' },
      { id: 'year', name: '本年' }
    ],
    currentTimeRange: 'month',
    
    // 快捷操作
    quickActions: [
      {
        id: 'add_ingredient',
        name: '添加食材',
        icon: '/images/icons/add-ingredient.png',
        url: '/pages/ingredients/add/add'
      },
      {
        id: 'find_recipe',
        name: '找菜谱',
        icon: '/images/icons/recipe.png',
        url: '/pages/recipes/list/list'
      },
      {
        id: 'shopping_list',
        name: '购物清单',
        icon: '🛒',
        url: '/pages/shopping/list/list'
      },
      {
        id: 'health_report',
        name: '健康报告',
        icon: '/images/icons/health.png',
        url: '/pages/analysis/health/health'
      }
    ]
  },

  onLoad() {
    console.log('数据分析仪表板页面加载');
    this.loadDashboardData();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  // 加载仪表板数据
  async loadDashboardData() {
    try {
      this.setData({ loading: true });

      const params = {
        timeRange: this.data.currentTimeRange
      };

      const result = await app.api.getDashboardData(params);
      
      this.setData({
        stats: result.stats,
        chartData: result.chartData,
        loading: false
      });

    } catch (error) {
      console.error('加载仪表板数据失败:', error);
      app.showError('加载失败');
      this.setData({ loading: false });
    }
  },

  // 刷新数据
  async refreshData() {
    try {
      this.setData({ refreshing: true });

      const params = {
        timeRange: this.data.currentTimeRange
      };

      const result = await app.api.getDashboardData(params);
      
      this.setData({
        stats: result.stats,
        chartData: result.chartData,
        refreshing: false
      });

      wx.stopPullDownRefresh();

    } catch (error) {
      console.error('刷新数据失败:', error);
      app.showError('刷新失败');
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  // 切换时间范围
  switchTimeRange(e) {
    const timeRange = e.currentTarget.dataset.range;
    if (timeRange === this.data.currentTimeRange) return;

    this.setData({ currentTimeRange: timeRange });
    this.loadDashboardData();
  },

  // 快捷操作
  onQuickAction(e) {
    const action = e.currentTarget.dataset.action;
    
    if (action.url) {
      wx.navigateTo({
        url: action.url
      });
    }
  },

  // 查看详细统计
  viewDetailStats(e) {
    const type = e.currentTarget.dataset.type;
    
    const urls = {
      ingredients: '/pages/analysis/ingredients/ingredients',
      recipes: '/pages/analysis/recipes/recipes',
      shopping: '/pages/analysis/shopping/shopping',
      health: '/pages/analysis/health/health'
    };
    
    if (urls[type]) {
      wx.navigateTo({
        url: urls[type]
      });
    }
  },

  // 查看过期提醒
  viewExpiringIngredients() {
    wx.navigateTo({
      url: '/pages/ingredients/list/list?filter=expiring'
    });
  },

  // 查看收藏菜谱
  viewFavoriteRecipes() {
    wx.navigateTo({
      url: '/pages/favorites/list/list?category=recipes'
    });
  },

  // 查看购物清单
  viewShoppingList() {
    wx.navigateTo({
      url: '/pages/shopping/list/list'
    });
  },

  // 格式化数字
  formatNumber(num) {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  },

  // 格式化金额
  formatMoney(amount) {
    return '¥' + amount.toFixed(2);
  },

  // 获取健康评分颜色
  getHealthScoreColor(score) {
    if (score >= 80) return '#28a745';
    if (score >= 60) return '#ffc107';
    return '#dc3545';
  },

  // 获取健康评分等级
  getHealthScoreLevel(score) {
    if (score >= 90) return '优秀';
    if (score >= 80) return '良好';
    if (score >= 60) return '一般';
    return '需改善';
  },

  // 获取时间范围名称
  getTimeRangeName(rangeId) {
    const range = this.data.timeRanges.find(r => r.id === rangeId);
    return range ? range.name : '本月';
  }
});