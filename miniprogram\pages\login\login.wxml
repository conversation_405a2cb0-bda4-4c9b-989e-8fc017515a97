<!--登录页面-->
<view class="login-container page-container">
  <view class="login-header fade-in-up">
    <view class="logo app-logo">🧊</view>
    <text class="app-name">智能冰箱助手</text>
    <text class="app-desc">让食材管理更智能</text>
  </view>

  <view class="login-form form-card fade-in-up">
    <view class="form-group">
      <view class="input-wrapper">
        <view class="input-icon">📱</view>
        <input
          class="form-input"
          type="number"
          placeholder="手机号"
          value="{{phone}}"
          bindinput="onPhoneInput"
          maxlength="11"
        />
      </view>
    </view>

    <view class="form-group">
      <view class="input-wrapper">
        <view class="input-icon">🔒</view>
        <input
          class="form-input"
          type="{{showPassword ? 'text' : 'password'}}"
          placeholder="密码"
          value="{{password}}"
          bindinput="onPasswordInput"
          maxlength="20"
        />
        <view 
          class="password-toggle" 
          bindtap="togglePasswordShow"
        >{{showPassword ? '👁️' : '🙈'}}</view>
      </view>
    </view>

    <button 
      class="btn btn-primary btn-block login-btn" 
      bindtap="handleLogin"
      loading="{{loading}}"
      disabled="{{loading}}"
    >
      <view wx:if="{{loading}}" class="loading-spinner"></view>
      {{loading ? '登录中...' : '立即登录'}}
    </button>

    <view class="login-options">
      <text class="forgot-password" bindtap="handleForgotPassword">忘记密码？</text>
      <text class="register-link" bindtap="goToRegister">立即注册</text>
    </view>
  </view>

  <view class="login-footer">
    <text class="footer-text">登录即表示同意</text>
    <text class="link-text">《用户协议》</text>
    <text class="footer-text">和</text>
    <text class="link-text">《隐私政策》</text>
  </view>
</view>