// 测试购物清单功能修复
console.log('🛒 测试购物清单功能修复\n');

// 模拟食材数据
const mockIngredient = {
  id: 1,
  name: '橙子',
  category: '水果',
  category_name: '水果',
  quantity: 2.00,
  unit: '斤',
  expire_date: '2025-08-07T16:00:00.000Z',
  statusInfo: {
    status: 'fresh',
    text: '6天后过期',
    color: '#38a169'
  }
};

// 分类映射
const categoryMap = {
  '蔬菜': 1,
  '水果': 2,
  '肉类': 3,
  '海鲜': 4,
  '蛋奶': 5,
  '调料': 6,
  '主食': 7,
  '饮品': 8,
  '零食': 9,
  '其他': 10
};

// 获取分类ID函数
function getCategoryId(categoryName) {
  return categoryMap[categoryName] || 10;
}

console.log('🔍 问题分析:');
console.log('❌ 原始问题:');
console.log('  - 前端发送 category 字段，后端期望 category_id');
console.log('  - 400 Bad Request: "该商品已在购物清单中"');
console.log('  - 没有处理重复添加的情况');

console.log('\n✅ 修复方案:');
console.log('1. 字段映射修复:');
console.log('   - category -> category_id');
console.log('   - 添加分类名称到ID的映射函数');

console.log('\n2. 数据格式修复:');
console.log('修复前的请求数据:');
const oldData = {
  name: mockIngredient.name,
  category: mockIngredient.category,
  quantity: 1,
  unit: mockIngredient.unit,
  notes: `来自食材：${mockIngredient.name}`
};
console.log(JSON.stringify(oldData, null, 2));

console.log('\n修复后的请求数据:');
const newData = {
  name: mockIngredient.name,
  category_id: getCategoryId(mockIngredient.category_name || mockIngredient.category),
  quantity: 1,
  unit: mockIngredient.unit || '个',
  notes: `来自食材：${mockIngredient.name}`
};
console.log(JSON.stringify(newData, null, 2));

console.log('\n📋 分类映射表:');
Object.entries(categoryMap).forEach(([name, id]) => {
  console.log(`  ${name} -> ${id}`);
});

console.log('\n🎯 错误处理改进:');
console.log('1. 检测重复添加错误');
console.log('2. 显示友好的确认对话框');
console.log('3. 提供增加数量的选项');

console.log('\n📱 用户交互流程:');
console.log('正常流程:');
console.log('  1. 点击"添加到购物清单"');
console.log('  2. 发送正确格式的请求');
console.log('  3. 显示"已添加到购物清单"');

console.log('\n重复添加流程:');
console.log('  1. 点击"添加到购物清单"');
console.log('  2. 检测到商品已存在');
console.log('  3. 弹出确认对话框');
console.log('  4. 用户选择是否增加数量');

console.log('\n🔧 技术改进:');
console.log('✅ 字段映射:');
console.log('  - 使用 category_id 替代 category');
console.log('  - 添加分类名称到ID的转换');
console.log('  - 提供默认单位"个"');

console.log('\n✅ 错误处理:');
console.log('  - 捕获特定错误消息');
console.log('  - 提供用户友好的交互');
console.log('  - 预留数量更新功能');

console.log('\n✅ 数据验证:');
console.log('  - 确保category_id为数字');
console.log('  - 提供默认值防止空值');
console.log('  - 兼容不同的分类字段名');

console.log('\n🧪 测试用例:');

// 测试分类映射
console.log('\n1. 分类映射测试:');
const testCategories = ['水果', '蔬菜', '肉类', '未知分类'];
testCategories.forEach(category => {
  const id = getCategoryId(category);
  console.log(`  ${category} -> ${id}`);
});

// 测试请求数据格式
console.log('\n2. 请求数据格式测试:');
const testIngredients = [
  { name: '苹果', category_name: '水果', unit: '个' },
  { name: '白菜', category: '蔬菜', unit: '棵' },
  { name: '牛肉', category_name: '肉类' }, // 无单位
];

testIngredients.forEach((ingredient, index) => {
  const requestData = {
    name: ingredient.name,
    category_id: getCategoryId(ingredient.category_name || ingredient.category),
    quantity: 1,
    unit: ingredient.unit || '个',
    notes: `来自食材：${ingredient.name}`
  };
  console.log(`  测试${index + 1}: ${JSON.stringify(requestData)}`);
});

console.log('\n🎉 修复完成！');
console.log('现在食材详情页面的购物清单功能应该可以正常工作：');
console.log('  ✅ 正确的字段映射');
console.log('  ✅ 友好的错误处理');
console.log('  ✅ 重复添加提示');
console.log('  ✅ 数据格式验证');
