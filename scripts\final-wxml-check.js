// 最终WXML语法验证
const fs = require('fs');
const path = require('path');

// 需要检查的目录
const DIRECTORIES = ['miniprogram/pages'];

// 获取所有WXML文件
function getAllWxmlFiles(dir, fileList = []) {
  if (!fs.existsSync(dir)) {
    return fileList;
  }
  
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      getAllWxmlFiles(filePath, fileList);
    } else if (file.endsWith('.wxml')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// 检查明显的语法错误
function checkObviousErrors(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const errors = [];
    
    // 检查明显的错误模式
    const errorPatterns = [
      {
        pattern: '</text></image>',
        description: '错误的标签组合: </text></image>'
      },
      {
        pattern: '</text></text>',
        description: '重复的结束标签: </text></text>'
      },
      {
        pattern: '</text></view>',
        description: '可能的错误标签组合: </text></view>'
      }
    ];
    
    errorPatterns.forEach(({ pattern, description }) => {
      if (content.includes(pattern)) {
        errors.push(description);
      }
    });
    
    // 检查孤立的结束标签
    lines.forEach((line, index) => {
      const trimmed = line.trim();
      if (trimmed === '</text>' || trimmed === '</image>' || trimmed === '</view>') {
        // 检查前一行是否也是结束标签
        const prevLine = index > 0 ? lines[index - 1].trim() : '';
        if (prevLine.endsWith('/>') || prevLine === '</text>' || prevLine === '</image>' || prevLine === '</view>') {
          errors.push(`第${index + 1}行: 可能的孤立结束标签 ${trimmed}`);
        }
      }
    });
    
    return errors;
  } catch (error) {
    return [`文件读取失败: ${error.message}`];
  }
}

// 主检查函数
function finalCheck() {
  console.log('🔍 最终WXML语法验证...\n');
  
  let totalFiles = 0;
  let problemFiles = 0;
  let totalErrors = 0;
  
  DIRECTORIES.forEach(dir => {
    const fullDir = path.resolve(dir);
    
    if (!fs.existsSync(fullDir)) {
      console.log(`⚠️  目录不存在: ${dir}`);
      return;
    }
    
    console.log(`📁 检查目录: ${dir}`);
    const files = getAllWxmlFiles(fullDir);
    
    files.forEach(filePath => {
      totalFiles++;
      const relativePath = path.relative(process.cwd(), filePath);
      
      const errors = checkObviousErrors(filePath);
      if (errors.length > 0) {
        problemFiles++;
        totalErrors += errors.length;
        
        console.log(`\n❌ 发现问题: ${relativePath}`);
        errors.forEach((error, index) => {
          console.log(`  ${index + 1}. ${error}`);
        });
      } else {
        console.log(`✅ ${path.basename(filePath)}`);
      }
    });
  });
  
  console.log(`\n📊 最终检查结果:`);
  console.log(`  总文件数: ${totalFiles}`);
  console.log(`  有问题文件数: ${problemFiles}`);
  console.log(`  总错误数: ${totalErrors}`);
  
  if (problemFiles === 0) {
    console.log('\n🎉 恭喜！所有WXML文件语法正确！');
    console.log('✅ 小程序应该可以正常编译运行了');
  } else {
    console.log('\n⚠️  仍有语法错误需要修复');
    console.log('💡 建议手动检查上述问题文件');
  }
  
  return { totalFiles, problemFiles, totalErrors };
}

// 运行检查
finalCheck();

module.exports = { finalCheck, checkObviousErrors };
