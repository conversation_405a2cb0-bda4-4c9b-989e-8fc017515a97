// 菜谱列表页面逻辑
const app = getApp();

Page({
  data: {
    recipes: [],
    loading: false,
    refreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    searchKeyword: '',
    selectedCategory: '',
    categories: ['全部', '家常菜', '素食', '汤品', '甜品', '小食', '饮品'],
    sortType: 'latest', // latest, popular, rating
    showFilter: false,
    filterOptions: {
      difficulty: '', // easy, medium, hard
      cookTime: '', // <30, 30-60, >60
      rating: '' // >4, >3, >2
    }
  },

  onLoad() {
    console.log('菜谱列表页面加载');
    this.loadRecipes(true);
  },

  onShow() {
    // 检查是否需要刷新数据
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    if (currentPage.data.needRefresh) {
      this.loadRecipes(true);
      this.setData({ needRefresh: false });
    }
  },

  onPullDownRefresh() {
    this.loadRecipes(true);
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadRecipes(false);
    }
  },

  // 加载菜谱列表
  async loadRecipes(reset = false) {
    if (this.data.loading) return;

    const { page, pageSize, searchKeyword, selectedCategory, sortType, filterOptions } = this.data;
    const currentPage = reset ? 1 : page;

    this.setData({
      loading: true,
      refreshing: reset
    });

    try {
      const params = {
        page: currentPage,
        pageSize,
        sort: sortType
      };

      // 添加搜索条件
      if (searchKeyword) {
        params.keyword = searchKeyword;
      }

      // 添加分类筛选
      if (selectedCategory && selectedCategory !== '全部') {
        params.category = selectedCategory;
      }

      // 添加其他筛选条件
      if (filterOptions.difficulty) {
        params.difficulty = filterOptions.difficulty;
      }
      if (filterOptions.cookTime) {
        params.cookTime = filterOptions.cookTime;
      }
      if (filterOptions.rating) {
        params.rating = filterOptions.rating;
      }

      const res = await app.request({
        url: '/recipes',
        data: params
      });

      const newRecipes = res.data.recipes || [];
      const recipes = reset ? newRecipes : [...this.data.recipes, ...newRecipes];

      this.setData({
        recipes,
        page: currentPage + 1,
        hasMore: newRecipes.length === pageSize,
        loading: false,
        refreshing: false
      });

      if (reset) {
        wx.stopPullDownRefresh();
      }

    } catch (error) {
      console.error('加载菜谱列表失败:', error);
      app.showError('加载失败');
      this.setData({
        loading: false,
        refreshing: false
      });
      
      if (reset) {
        wx.stopPullDownRefresh();
      }
    }
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  onSearch() {
    this.loadRecipes(true);
  },

  // 清除搜索
  onSearchClear() {
    this.setData({
      searchKeyword: ''
    });
    this.loadRecipes(true);
  },

  // 选择分类
  onCategoryChange(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      selectedCategory: category
    });
    this.loadRecipes(true);
  },

  // 切换排序方式
  onSortChange(e) {
    const sortType = e.currentTarget.dataset.sort;
    this.setData({
      sortType
    });
    this.loadRecipes(true);
  },

  // 显示筛选器
  showFilterPanel() {
    this.setData({
      showFilter: true
    });
  },

  // 隐藏筛选器
  hideFilterPanel() {
    this.setData({
      showFilter: false
    });
  },

  // 筛选条件改变
  onFilterChange(e) {
    const { type, value } = e.currentTarget.dataset;
    const filterOptions = { ...this.data.filterOptions };
    
    // 如果点击的是已选中的选项，则取消选择
    if (filterOptions[type] === value) {
      filterOptions[type] = '';
    } else {
      filterOptions[type] = value;
    }

    this.setData({
      filterOptions
    });
  },

  // 应用筛选
  applyFilter() {
    this.setData({
      showFilter: false
    });
    this.loadRecipes(true);
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      filterOptions: {
        difficulty: '',
        cookTime: '',
        rating: ''
      }
    });
  },

  // 查看菜谱详情
  viewRecipeDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/recipes/detail/detail?id=${id}`
    });
  },

  // 收藏/取消收藏菜谱
  async toggleFavorite(e) {
    e.stopPropagation();
    const { id, index } = e.currentTarget.dataset;
    const recipe = this.data.recipes[index];

    try {
      if (recipe.is_favorited) {
        await app.request({
          url: `/favorites/${id}`,
          method: 'DELETE'
        });
        app.showSuccess('已取消收藏');
      } else {
        await app.request({
          url: '/favorites',
          method: 'POST',
          data: {
            type: 'recipe',
            target_id: id
          }
        });
        app.showSuccess('收藏成功');
      }

      // 更新本地状态
      const recipes = [...this.data.recipes];
      recipes[index].is_favorited = !recipes[index].is_favorited;
      this.setData({ recipes });

    } catch (error) {
      console.error('收藏操作失败:', error);
      app.showError('操作失败');
    }
  },

  // 分享菜谱
  shareRecipe(e) {
    e.stopPropagation();
    const { id, name } = e.currentTarget.dataset;
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 添加新菜谱
  addNewRecipe() {
    wx.navigateTo({
      url: '/pages/recipes/add/add'
    });
  },

  // 语音搜索
  startVoiceSearch() {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.record']) {
          this.doVoiceSearch();
        } else {
          wx.authorize({
            scope: 'scope.record',
            success: () => {
              this.doVoiceSearch();
            },
            fail: () => {
              app.showError('需要录音权限才能使用语音搜索');
            }
          });
        }
      }
    });
  },

  // 执行语音搜索
  doVoiceSearch() {
    const recorderManager = wx.getRecorderManager();
    
    recorderManager.start({
      duration: 10000,
      sampleRate: 16000,
      numberOfChannels: 1,
      encodeBitRate: 96000,
      format: 'mp3'
    });

    app.showLoading('正在录音...');

    recorderManager.onStop((res) => {
      wx.hideLoading();
      // 这里可以调用语音识别API
      app.showSuccess('语音搜索功能开发中');
    });

    setTimeout(() => {
      recorderManager.stop();
    }, 3000);
  },

  // 扫码搜索
  scanToSearch() {
    wx.scanCode({
      success: (res) => {
        this.setData({
          searchKeyword: res.result
        });
        this.loadRecipes(true);
      },
      fail: () => {
        app.showError('扫码失败');
      }
    });
  },

  // 分享给好友
  onShareAppMessage() {
    return {
      title: '发现好菜谱，一起来学做菜吧！',
      desc: '智能冰箱食材管理助手 - 菜谱推荐',
      path: '/pages/recipes/list/list',
      imageUrl: '/images/share-recipes.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '发现好菜谱，一起来学做菜吧！',
      query: '',
      imageUrl: '/images/share-recipes.png'
    };
  }
});