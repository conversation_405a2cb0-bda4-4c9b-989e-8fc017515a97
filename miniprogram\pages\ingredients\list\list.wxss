/* 食材列表页面样式 */

.page-container {
  padding-bottom: 120rpx;
}

/* 搜索栏 */
.search-section {
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 24rpx;
  padding: 16rpx 20rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  background-color: transparent;
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.search-clear {
  font-size: 24rpx;
  color: #999;
  padding: 8rpx 12rpx;
}

.filter-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 分类标签 */
.category-section {
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 20rpx;
  gap: 16rpx;
}

.category-item {
  flex-shrink: 0;
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.category-item.active {
  background-color: #4CAF50;
  color: white;
}

/* 筛选器 */
.filter-panel {
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.filter-panel.show {
  max-height: 400rpx;
}

.filter-section {
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-section:last-child {
  border-bottom: none;
}

.filter-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.filter-options,
.sort-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.filter-option,
.sort-option {
  padding: 12rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.filter-option.active,
.sort-option.active {
  background-color: #4CAF50;
  color: white;
}

/* 工具栏 */
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.ingredient-count {
  font-size: 26rpx;
  color: #666;
}

.batch-actions {
  display: flex;
  gap: 20rpx;
}

.batch-action {
  font-size: 26rpx;
  color: #4CAF50;
  padding: 8rpx 16rpx;
  border: 1rpx solid #4CAF50;
  border-radius: 12rpx;
}

/* 食材列表 */
.ingredients-list {
  padding: 0 20rpx;
}

.ingredient-item {
  display: flex;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.ingredient-item:active {
  transform: translateY(2rpx);
}

.ingredient-image {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.ingredient-img {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  background-color: #f5f5f5;
}

.status-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: white;
}

.status-badge.status-1 {
  background-color: #2ed573;
}

.status-badge.status-2 {
  background-color: #ffa502;
}

.status-badge.status-3 {
  background-color: #ff4757;
}

.ingredient-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.ingredient-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.ingredient-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.ingredient-category {
  font-size: 22rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.ingredient-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

.expire-date {
  font-weight: 500;
}

.ingredient-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-left: 16rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn image {
  width: 32rpx;
  height: 32rpx;
}

.edit-btn {
  background-color: #e3f2fd;
}

.edit-btn:active {
  background-color: #bbdefb;
}

.delete-btn {
  background-color: #ffebee;
}

.delete-btn:active {
  background-color: #ffcdd2;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #ccc;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}

.loading-text {
  margin-left: 20rpx;
  color: #666;
  font-size: 26rpx;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.load-more-text {
  color: #666;
  font-size: 26rpx;
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.no-more-text {
  color: #999;
  font-size: 24rpx;
}

/* 悬浮按钮 */
.fab {
  position: fixed;
  right: 40rpx;
  bottom: 120rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #66BB6A 0%, #4CAF50 50%, #43A047 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12rpx 32rpx rgba(76, 175, 80, 0.35),
              0 4rpx 16rpx rgba(76, 175, 80, 0.25),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
  z-index: 100;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2rpx solid rgba(255, 255, 255, 0.15);
  overflow: hidden;
}

.fab:active {
  transform: scale(0.92);
  box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.4),
              0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
  line-height: 1;
  transition: transform 0.2s ease;
}

/* 添加光泽效果 */
.fab::after {
  content: '';
  position: absolute;
  top: 10%;
  left: 10%;
  width: 30%;
  height: 30%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, transparent 50%);
  border-radius: 50%;
  pointer-events: none;
}

/* 添加脉冲动画 */
.fab::before {
  content: '';
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  right: -8rpx;
  bottom: -8rpx;
  border-radius: 50%;
  background: rgba(76, 175, 80, 0.2);
  animation: pulse 3s infinite;
  z-index: -1;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.15);
    opacity: 0.4;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

/* 按钮图标旋转效果 */
.fab:active .fab-icon {
  transform: rotate(90deg);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .ingredient-item {
    padding: 20rpx;
  }
  
  .ingredient-image {
    width: 100rpx;
    height: 100rpx;
    margin-right: 20rpx;
  }
  
  .ingredient-name {
    font-size: 28rpx;
  }
  
  .ingredient-actions {
    gap: 12rpx;
  }
  
  .action-btn {
    width: 50rpx;
    height: 50rpx;
  }
  
  .action-btn image {
    width: 28rpx;
    height: 28rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .search-section,
  .category-section,
  .filter-panel,
  .toolbar,
  .ingredient-item {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .search-bar {
    background-color: #404040;
  }
  
  .search-input {
    color: white;
  }
  
  .category-item {
    background-color: #404040;
    color: #ccc;
  }
  
  .ingredient-name {
    color: white;
  }
  
  .detail-value {
    color: #ccc;
  }
  
  .ingredient-category {
    background-color: #404040;
    color: #999;
  }
}