/* 食材列表页面样式 - 现代化重构版 */

/* 页面容器 */
.page-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  padding-bottom: 160rpx;
}

/* 顶部导航栏 */
.header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 32rpx 32rpx;
  padding-top: calc(40rpx + env(safe-area-inset-top));
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.header-title {
  flex: 1;
}

.title-text {
  font-size: 48rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 8rpx;
}

.subtitle-text {
  font-size: 28rpx;
  opacity: 0.8;
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  text-align: center;
  min-width: 60rpx;
}

.stat-item.warning {
  color: #ffd93d;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 700;
  display: block;
  line-height: 1;
}

.stat-label {
  font-size: 20rpx;
  opacity: 0.8;
  margin-top: 4rpx;
  display: block;
}

/* 搜索和筛选栏 */
.search-filter-section {
  background: white;
  padding: 24rpx 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 10;
}

.search-container {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.search-bar {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.search-bar:focus-within {
  background: white;
  border-color: #667eea;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.search-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.search-bar:focus-within .search-icon {
  opacity: 1;
  color: #667eea;
}

.search-input {
  flex: 1;
  font-size: 30rpx;
  background: transparent;
  border: none;
  outline: none;
}

.search-clear {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #6c757d;
  transition: all 0.2s ease;
}

.search-clear:active {
  background: #adb5bd;
  transform: scale(0.9);
}

.filter-toggle {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.filter-toggle.active,
.filter-toggle:active {
  background: #667eea;
  border-color: #667eea;
  transform: scale(0.95);
}

.filter-toggle.active .filter-icon,
.filter-toggle:active .filter-icon {
  filter: brightness(0) invert(1);
}

.filter-icon {
  font-size: 32rpx;
  transition: all 0.3s ease;
}

/* 分类标签 */
.category-section {
  background: white;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f1f3f4;
}

.category-scroll {
  width: 100%;
}

.category-list {
  display: flex;
  padding: 0 32rpx;
  gap: 16rpx;
}

.category-chip {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 32rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: fit-content;
}

.category-chip.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
}

.category-chip:active {
  transform: scale(0.95);
}

.chip-icon {
  font-size: 28rpx;
}

.chip-text {
  font-size: 26rpx;
  font-weight: 500;
}

.category-chip.active .chip-icon,
.category-chip.active .chip-text {
  color: white;
}

/* 高级筛选器 */
.filter-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: none;
}

.filter-panel.show {
  display: block;
}

.filter-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4rpx);
}

.filter-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 80vh;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f1f3f4;
}

.filter-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3748;
}

.filter-close {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #6c757d;
  transition: all 0.2s ease;
}

.filter-close:active {
  background: #e9ecef;
  transform: scale(0.9);
}

.filter-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-group {
  margin-bottom: 48rpx;
}

.group-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 24rpx;
  display: block;
}

.filter-chips,
.sort-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.filter-chip,
.sort-chip {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 28rpx;
  background: #f8f9fa;
  border-radius: 32rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  font-size: 28rpx;
}

.filter-chip.active,
.sort-chip.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.filter-chip:active,
.sort-chip:active {
  transform: scale(0.95);
}

.chip-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}

.chip-dot.all { background: #6c757d; }
.chip-dot.fresh { background: #28a745; }
.chip-dot.expiring { background: #ffc107; }
.chip-dot.expired { background: #dc3545; }

.sort-icon {
  font-size: 24rpx;
}

.sort-arrow {
  font-size: 20rpx;
  font-weight: bold;
}

.filter-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #f1f3f4;
  background: #f8f9fa;
}

.filter-reset,
.filter-apply {
  flex: 1;
  height: 88rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.filter-reset {
  background: white;
  color: #6c757d;
  border: 2rpx solid #dee2e6;
}

.filter-apply {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.filter-reset:active {
  background: #f8f9fa;
  transform: scale(0.98);
}

.filter-apply:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 快捷操作栏 */
.quick-actions {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  background: white;
  margin: 16rpx 0;
}

.quick-action {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 24rpx 16rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.quick-action:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.action-icon {
  font-size: 32rpx;
}

.action-text {
  font-size: 24rpx;
  color: #6c757d;
  font-weight: 500;
}

/* 食材网格列表 */
.ingredients-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  padding: 0 32rpx 32rpx;
}

.ingredient-card {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
}

.ingredient-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
}

/* 卡片头部 */
.card-header {
  position: relative;
  height: 200rpx;
}

.ingredient-image {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.ingredient-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.status-indicator {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  border: 3rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.status-indicator.status-fresh { background: #28a745; }
.status-indicator.status-expiring { background: #ffc107; }
.status-indicator.status-expired { background: #dc3545; }

.card-actions {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  display: flex;
  gap: 8rpx;
}

.action-btn {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.9);
}

.edit-btn:active {
  background: rgba(102, 126, 234, 0.9);
}

.delete-btn:active {
  background: rgba(220, 53, 69, 0.9);
}

.btn-icon {
  font-size: 24rpx;
}

/* 卡片内容 */
.card-content {
  padding: 24rpx;
}

.ingredient-info {
  margin-bottom: 20rpx;
}

.ingredient-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  display: block;
  margin-bottom: 12rpx;
  line-height: 1.2;
}

.ingredient-meta {
  display: flex;
  gap: 16rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  flex: 1;
}

.meta-icon {
  font-size: 20rpx;
  opacity: 0.7;
}

.meta-text {
  font-size: 24rpx;
  color: #6c757d;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.expire-info {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.expire-label {
  font-size: 20rpx;
  color: #6c757d;
  margin-bottom: 8rpx;
}

.expire-date {
  font-size: 26rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.expire-date.status-fresh { color: #28a745; }
.expire-date.status-expiring { color: #ffc107; }
.expire-date.status-expired { color: #dc3545; }

.expire-countdown {
  font-size: 22rpx;
  color: #6c757d;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.location-icon {
  font-size: 20rpx;
  opacity: 0.7;
}

.location-text {
  font-size: 24rpx;
  color: #6c757d;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 32rpx;
  text-align: center;
}

.empty-illustration {
  position: relative;
  margin-bottom: 48rpx;
}

.empty-icon {
  font-size: 120rpx;
  position: relative;
  z-index: 2;
}

.empty-bg-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 50%;
  z-index: 1;
}

.empty-content {
  max-width: 500rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16rpx;
  display: block;
}

.empty-desc {
  font-size: 28rpx;
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 48rpx;
  display: block;
}

.empty-features {
  display: flex;
  justify-content: center;
  gap: 32rpx;
  margin-bottom: 48rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.feature-icon {
  font-size: 32rpx;
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-text {
  font-size: 24rpx;
  color: #6c757d;
}

.empty-action-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx 48rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 32rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.empty-action-btn:active {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);
}

.empty-action-btn .btn-icon {
  font-size: 28rpx;
  font-weight: normal;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 32rpx;
  gap: 24rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f1f3f4;
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #6c757d;
}

.load-more-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 32rpx;
}

.load-more-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #f1f3f4;
  border-top: 3rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.load-more-text {
  font-size: 26rpx;
  color: #6c757d;
}

.no-more-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 32rpx;
}

.no-more-line {
  flex: 1;
  height: 1rpx;
  background: #dee2e6;
}

.no-more-text {
  font-size: 24rpx;
  color: #adb5bd;
  padding: 0 16rpx;
}

/* 智能浮动按钮 */
.smart-fab {
  position: fixed;
  bottom: 32rpx;
  right: 32rpx;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.fab-main {
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.fab-main:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.6);
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
}

.fab-label {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  white-space: nowrap;
  backdrop-filter: blur(8rpx);
}

/* 快速扫码按钮 */
.scan-fab {
  position: fixed;
  bottom: 160rpx;
  right: 32rpx;
  z-index: 99;
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.scan-fab:active {
  transform: scale(0.9);
  background: #f8f9fa;
}

.scan-icon {
  font-size: 36rpx;
}
}

/* 分类标签 */
.category-section {
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 20rpx;
  gap: 16rpx;
}

.category-item {
  flex-shrink: 0;
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.category-item.active {
  background-color: #4CAF50;
  color: white;
}

/* 筛选器 */
.filter-panel {
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.filter-panel.show {
  max-height: 400rpx;
}

.filter-section {
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-section:last-child {
  border-bottom: none;
}

.filter-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.filter-options,
.sort-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.filter-option,
.sort-option {
  padding: 12rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.filter-option.active,
.sort-option.active {
  background-color: #4CAF50;
  color: white;
}

/* 工具栏 */
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.ingredient-count {
  font-size: 26rpx;
  color: #666;
}

.batch-actions {
  display: flex;
  gap: 20rpx;
}

.batch-action {
  font-size: 26rpx;
  color: #4CAF50;
  padding: 8rpx 16rpx;
  border: 1rpx solid #4CAF50;
  border-radius: 12rpx;
}

/* 食材列表 */
.ingredients-list {
  padding: 0 20rpx;
}

.ingredient-item {
  display: flex;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.ingredient-item:active {
  transform: translateY(2rpx);
}

.ingredient-image {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.ingredient-img {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  background-color: #f5f5f5;
}

.status-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: white;
}

.status-badge.status-1 {
  background-color: #2ed573;
}

.status-badge.status-2 {
  background-color: #ffa502;
}

.status-badge.status-3 {
  background-color: #ff4757;
}

.ingredient-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.ingredient-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.ingredient-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.ingredient-category {
  font-size: 22rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.ingredient-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

.expire-date {
  font-weight: 500;
}

.ingredient-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-left: 16rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn image {
  width: 32rpx;
  height: 32rpx;
}

.edit-btn {
  background-color: #e3f2fd;
}

.edit-btn:active {
  background-color: #bbdefb;
}

.delete-btn {
  background-color: #ffebee;
}

.delete-btn:active {
  background-color: #ffcdd2;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #ccc;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}

.loading-text {
  margin-left: 20rpx;
  color: #666;
  font-size: 26rpx;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.load-more-text {
  color: #666;
  font-size: 26rpx;
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.no-more-text {
  color: #999;
  font-size: 24rpx;
}

/* 悬浮按钮 */
.fab {
  position: fixed;
  right: 40rpx;
  bottom: 120rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #66BB6A 0%, #4CAF50 50%, #43A047 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12rpx 32rpx rgba(76, 175, 80, 0.35),
              0 4rpx 16rpx rgba(76, 175, 80, 0.25),
              inset 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
  z-index: 100;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2rpx solid rgba(255, 255, 255, 0.15);
  overflow: hidden;
}

.fab:active {
  transform: scale(0.92);
  box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.4),
              0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
  line-height: 1;
  transition: transform 0.2s ease;
}

/* 添加光泽效果 */
.fab::after {
  content: '';
  position: absolute;
  top: 10%;
  left: 10%;
  width: 30%;
  height: 30%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, transparent 50%);
  border-radius: 50%;
  pointer-events: none;
}

/* 添加脉冲动画 */
.fab::before {
  content: '';
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  right: -8rpx;
  bottom: -8rpx;
  border-radius: 50%;
  background: rgba(76, 175, 80, 0.2);
  animation: pulse 3s infinite;
  z-index: -1;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.15);
    opacity: 0.4;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

/* 按钮图标旋转效果 */
.fab:active .fab-icon {
  transform: rotate(90deg);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .ingredient-item {
    padding: 20rpx;
  }
  
  .ingredient-image {
    width: 100rpx;
    height: 100rpx;
    margin-right: 20rpx;
  }
  
  .ingredient-name {
    font-size: 28rpx;
  }
  
  .ingredient-actions {
    gap: 12rpx;
  }
  
  .action-btn {
    width: 50rpx;
    height: 50rpx;
  }
  
  .action-btn image {
    width: 28rpx;
    height: 28rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .search-section,
  .category-section,
  .filter-panel,
  .toolbar,
  .ingredient-item {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .search-bar {
    background-color: #404040;
  }
  
  .search-input {
    color: white;
  }
  
  .category-item {
    background-color: #404040;
    color: #ccc;
  }
  
  .ingredient-name {
    color: white;
  }
  
  .detail-value {
    color: #ccc;
  }
  
  .ingredient-category {
    background-color: #404040;
    color: #999;
  }
}