/**
 * 图片处理工具类
 */

// 默认图片映射
const DEFAULT_IMAGES = {
  'login-prompt': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjVGNUY1Ii8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjYwIiByPSIyMCIgZmlsbD0iI0NDQyIvPgo8cGF0aCBkPSJNNzAgMTAwSDEzMFYxMTBINzBWMTAwWiIgZmlsbD0iI0NDQyIvPgo8dGV4dCB4PSIxMDAiIHk9IjEzMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzk5OSIgZm9udC1zaXplPSIxMiI+5omp5omL55m75b2VPC90ZXh0Pgo8L3N2Zz4K',
  'wechat-login': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMwN0MxNjAiLz4KPHBhdGggZD0iTTI4IDEySDEyVjI4SDI4VjEyWiIgZmlsbD0id2hpdGUiLz4KPHRleHQgeD0iMjAiIHk9IjI1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjMDdDMTYwIiBmb250LXNpemU9IjgiPuW+rTwvdGV4dD4KPC9zdmc+Cg==',
  'default-food': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjBGOEZGIi8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iNDAiIHI9IjE1IiBmaWxsPSIjRkZBNzI2Ii8+CjxwYXRoIGQ9Ik0zNSA2MEg2NVY3MEgzNVY2MFoiIGZpbGw9IiM0Q0FGNTASCZ0ZXh0IHg9IjUwIiB5PSI4NSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzk5OSIgZm9udC1zaXplPSIxMCI+6aOf5p2QPC90ZXh0Pgo8L3N2Zz4K',
  'default-recipe': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRkZGM0UwIi8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iNDAiIHI9IjEyIiBmaWxsPSIjRkY5ODAwIi8+CjxwYXRoIGQ9Ik00MCA2MEg2MFY2NUg0MFY2MFoiIGZpbGw9IiNGRjk4MDAiLz4KPHA+dGggZD0iTTQ1IDY4SDU1VjcySA0NVY2OFoiIGZpbGw9IiNGRjk4MDAiLz4KPHA+dGggZD0iTTQyIDc1SDU4Vjc4SDQyVjc1WiIgZmlsbD0iI0ZGOTgwMCIvPgo8dGV4dCB4PSI1MCIgeT0iODgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM5OTkiIGZvbnQtc2l6ZT0iMTAiPuiPnOiwsTwvdGV4dD4KPC9zdmc+Cg==',
  'default-avatar': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxjaXJjbGUgY3g9IjUwIiBjeT0iNTAiIHI9IjUwIiBmaWxsPSIjRTlFQ0VGIi8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iNDAiIHI9IjE1IiBmaWxsPSIjOTU5NUE1Ii8+CjxwYXRoIGQ9Ik0yNSA4MEM0MCA2NSA2MCA2NSA3NSA4MEw3NSA5MEwyNSA5MFYOMFoiIGZpbGw9IiM5NTk1QTUiLz4KPC9zdmc+Cg==',
  'ingredients-icon': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM0Q0FGNTASCZ0ZXh0IHg9IjIwIiB5PSIyNSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0id2hpdGUiIGZvbnQtc2l6ZT0iMTAiPuaJkTwvdGV4dD4KPC9zdmc+Cg==',
  'recipes-icon': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGRjk4MDAiLz4KPHA+dGggZD0iTTEwIDEwSDMwVjMwSDEwVjEwWiIgZmlsbD0id2hpdGUiLz4KPHA+dGggZD0iTTE0IDE0SDE4VjE4SDE0VjE0WiIgZmlsbD0iI0ZGOTgwMCIvPgo8cGF0aCBkPSJNMjIgMTRIMjZWMThIMjJWMTRaIiBmaWxsPSIjRkY5ODAwIi8+CjxwYXRoIGQ9Ik0xNCAyMkgxOFYyNkgxNFYyMloiIGZpbGw9IiNGRjk4MDAiLz4KPHA+dGggZD0iTTIyIDIySDI2VjI2SDIyVjIyWiIgZmlsbD0iI0ZGOTgwMCIvPgo8L3N2Zz4K',
  'shopping-icon': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGRkE3MjYiLz4KPHBhdGggZD0iTTEwIDEySDMwVjI4SDEwVjEyWiIgZmlsbD0id2hpdGUiLz4KPHA+dGggZD0iTTE0IDE2SDE4VjIwSDE0VjE2WiIgZmlsbD0iI0ZGQTcyNiIvPgo8cGF0aCBkPSJNMjIgMTZIMjZWMjBIMjJWMTZaIiBmaWxsPSIjRkZBNzI2Ii8+CjxwYXRoIGQ9Ik0xNCAyMkgxOFYyNEgxNFYyMloiIGZpbGw9IiNGRkE3MjYiLz4KPHA+dGggZD0iTTIyIDIySDI2VjI0SDIyVjIyWiIgZmlsbD0iI0ZGQTcyNiIvPgo8L3N2Zz4K',
  'analysis-icon': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMxN0EyQjgiLz4KPHBhdGggZD0iTTEwIDI4SDE0VjE4SDE0VjI4WiIgZmlsbD0id2hpdGUiLz4KPHA+dGggZD0iTTE4IDI4SDIyVjE0SDIyVjI4WiIgZmlsbD0id2hpdGUiLz4KPHA+dGggZD0iTTI2IDI4SDMwVjIwSDMwVjI4WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg=='
};

/**
 * 获取图片路径，如果图片不存在则返回默认图片
 * @param {string} imageName 图片名称（不含扩展名）
 * @param {string} category 图片分类
 * @returns {string} 图片路径或默认图片
 */
function getImagePath(imageName, category = '') {
  // 构建图片路径
  const imagePath = `/images/${imageName}.png`;
  
  // 如果有默认图片，返回默认图片
  if (DEFAULT_IMAGES[imageName]) {
    return DEFAULT_IMAGES[imageName];
  }
  
  // 根据分类返回默认图片
  switch (category) {
    case 'food':
    case 'ingredient':
      return DEFAULT_IMAGES['default-food'];
    case 'recipe':
      return DEFAULT_IMAGES['default-recipe'];
    case 'avatar':
    case 'user':
      return '/images/tab/default-avatar.png';
    default:
      return imagePath;
  }
}

/**
 * 处理图片加载错误
 * @param {Event} e 错误事件
 * @param {string} category 图片分类
 */
function handleImageError(e, category = '') {
  const target = e.target || e.currentTarget;
  if (!target) return;
  
  // 根据分类设置默认图片
  let defaultImage = '';
  switch (category) {
    case 'food':
    case 'ingredient':
      defaultImage = DEFAULT_IMAGES['default-food'];
      break;
    case 'recipe':
      defaultImage = DEFAULT_IMAGES['default-recipe'];
      break;
    case 'avatar':
    case 'user':
      defaultImage = '/images/tab/default-avatar.png';
      break;
    default:
      // 尝试从src中推断类型
      const src = target.src || '';
      if (src.includes('food') || src.includes('ingredient')) {
        defaultImage = DEFAULT_IMAGES['default-food'];
      } else if (src.includes('recipe')) {
        defaultImage = DEFAULT_IMAGES['default-recipe'];
      } else if (src.includes('avatar') || src.includes('user')) {
        defaultImage = '/images/tab/default-avatar.png';
      } else {
        defaultImage = DEFAULT_IMAGES['default-food'];
      }
  }
  
  // 设置默认图片
  target.src = defaultImage;
}

/**
 * 创建占位符图片
 * @param {number} width 宽度
 * @param {number} height 高度
 * @param {string} text 显示文字
 * @param {string} bgColor 背景色
 * @param {string} textColor 文字颜色
 * @returns {string} base64图片
 */
function createPlaceholder(width = 100, height = 100, text = '暂无图片', bgColor = '#F5F5F5', textColor = '#999') {
  const svg = `
    <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="${width}" height="${height}" fill="${bgColor}"/>
      <text x="${width/2}" y="${height/2 + 5}" text-anchor="middle" fill="${textColor}" font-size="12">${text}</text>
    </svg>
  `;
  return `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(svg)))}`;
}

module.exports = {
  getImagePath,
  handleImageError,
  createPlaceholder,
  DEFAULT_IMAGES
};