// 菜谱添加/编辑页面
const app = getApp();

Page({
  data: {
    // 页面状态
    isEdit: false,
    recipeId: null,
    loading: false,
    
    // 表单数据
    formData: {
      name: '',
      description: '',
      category: '',
      difficulty: 1,
      cookTime: 30,
      servings: 2,
      image: '',
      ingredients: [],
      steps: [],
      nutrition: {
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0
      },
      tags: []
    },
    
    // 选项数据
    categories: [
      { id: 'breakfast', name: '早餐' },
      { id: 'lunch', name: '午餐' },
      { id: 'dinner', name: '晚餐' },
      { id: 'snack', name: '小食' },
      { id: 'dessert', name: '甜品' },
      { id: 'soup', name: '汤品' }
    ],
    
    difficulties: [
      { value: 1, name: '简单' },
      { value: 2, name: '中等' },
      { value: 3, name: '困难' }
    ],
    
    timeOptions: [15, 30, 45, 60, 90, 120],
    servingOptions: [1, 2, 3, 4, 5, 6, 8, 10],
    
    // 界面状态
    showCategoryPicker: false,
    showDifficultyPicker: false,
    showTimePicker: false,
    showServingPicker: false,
    showTagInput: false,
    
    // 输入状态
    newIngredient: { name: '', amount: '', unit: '' },
    newStep: '',
    newTag: '',
    
    // 可用食材
    availableIngredients: []
  },

  onLoad(options) {
    console.log('菜谱添加页面加载', options);
    
    if (options.id) {
      this.setData({
        isEdit: true,
        recipeId: options.id
      });
      this.loadRecipeData(options.id);
    }
    
    this.loadAvailableIngredients();
  },

  // 加载菜谱数据
  async loadRecipeData(id) {
    try {
      this.setData({ loading: true });
      
      const recipe = await app.api.getRecipe(id);
      this.setData({
        formData: recipe,
        loading: false
      });
      
      wx.setNavigationBarTitle({
        title: '编辑菜谱'
      });
    } catch (error) {
      console.error('加载菜谱失败:', error);
      app.showError('加载菜谱失败');
      this.setData({ loading: false });
    }
  },

  // 加载可用食材
  async loadAvailableIngredients() {
    try {
      const ingredients = await app.api.getIngredients();
      this.setData({
        availableIngredients: ingredients
      });
    } catch (error) {
      console.error('加载食材失败:', error);
    }
  },

  // 基本信息输入
  onNameInput(e) {
    this.setData({
      'formData.name': e.detail.value
    });
  },

  onDescriptionInput(e) {
    this.setData({
      'formData.description': e.detail.value
    });
  },

  // 分类选择
  showCategoryPicker() {
    this.setData({ showCategoryPicker: true });
  },

  onCategoryChange(e) {
    const index = e.detail.value;
    const category = this.data.categories[index];
    this.setData({
      'formData.category': category.id,
      showCategoryPicker: false
    });
  },

  onCategoryCancel() {
    this.setData({ showCategoryPicker: false });
  },

  // 难度选择
  showDifficultyPicker() {
    this.setData({ showDifficultyPicker: true });
  },

  onDifficultyChange(e) {
    const index = e.detail.value;
    const difficulty = this.data.difficulties[index];
    this.setData({
      'formData.difficulty': difficulty.value,
      showDifficultyPicker: false
    });
  },

  onDifficultyCancel() {
    this.setData({ showDifficultyPicker: false });
  },

  // 时间选择
  showTimePicker() {
    this.setData({ showTimePicker: true });
  },

  onTimeChange(e) {
    const index = e.detail.value;
    const time = this.data.timeOptions[index];
    this.setData({
      'formData.cookTime': time,
      showTimePicker: false
    });
  },

  onTimeCancel() {
    this.setData({ showTimePicker: false });
  },

  // 份数选择
  showServingPicker() {
    this.setData({ showServingPicker: true });
  },

  onServingChange(e) {
    const index = e.detail.value;
    const serving = this.data.servingOptions[index];
    this.setData({
      'formData.servings': serving,
      showServingPicker: false
    });
  },

  onServingCancel() {
    this.setData({ showServingPicker: false });
  },

  // 图片上传
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.uploadImage(res.tempFilePaths[0]);
      }
    });
  },

  async uploadImage(filePath) {
    try {
      wx.showLoading({ title: '上传中...' });
      
      const imageUrl = await app.api.uploadImage(filePath);
      this.setData({
        'formData.image': imageUrl
      });
      
      wx.hideLoading();
      app.showSuccess('图片上传成功');
    } catch (error) {
      console.error('图片上传失败:', error);
      wx.hideLoading();
      app.showError('图片上传失败');
    }
  },

  // 食材管理
  onIngredientNameInput(e) {
    this.setData({
      'newIngredient.name': e.detail.value
    });
  },

  onIngredientAmountInput(e) {
    this.setData({
      'newIngredient.amount': e.detail.value
    });
  },

  onIngredientUnitInput(e) {
    this.setData({
      'newIngredient.unit': e.detail.value
    });
  },

  addIngredient() {
    const { newIngredient, formData } = this.data;
    
    if (!newIngredient.name.trim()) {
      app.showError('请输入食材名称');
      return;
    }
    
    if (!newIngredient.amount.trim()) {
      app.showError('请输入用量');
      return;
    }
    
    const ingredient = {
      id: Date.now(),
      name: newIngredient.name.trim(),
      amount: newIngredient.amount.trim(),
      unit: newIngredient.unit.trim() || '个'
    };
    
    formData.ingredients.push(ingredient);
    
    this.setData({
      'formData.ingredients': formData.ingredients,
      newIngredient: { name: '', amount: '', unit: '' }
    });
  },

  removeIngredient(e) {
    const index = e.currentTarget.dataset.index;
    const ingredients = this.data.formData.ingredients;
    ingredients.splice(index, 1);
    
    this.setData({
      'formData.ingredients': ingredients
    });
  },

  // 选择已有食材
  selectAvailableIngredient(e) {
    const ingredient = e.currentTarget.dataset.ingredient;
    this.setData({
      'newIngredient.name': ingredient.name,
      'newIngredient.unit': ingredient.unit || '个'
    });
  },

  // 制作步骤管理
  onStepInput(e) {
    this.setData({
      newStep: e.detail.value
    });
  },

  addStep() {
    const { newStep, formData } = this.data;
    
    if (!newStep.trim()) {
      app.showError('请输入制作步骤');
      return;
    }
    
    const step = {
      id: Date.now(),
      order: formData.steps.length + 1,
      description: newStep.trim(),
      image: ''
    };
    
    formData.steps.push(step);
    
    this.setData({
      'formData.steps': formData.steps,
      newStep: ''
    });
  },

  removeStep(e) {
    const index = e.currentTarget.dataset.index;
    const steps = this.data.formData.steps;
    steps.splice(index, 1);
    
    // 重新排序
    steps.forEach((step, i) => {
      step.order = i + 1;
    });
    
    this.setData({
      'formData.steps': steps
    });
  },

  moveStepUp(e) {
    const index = e.currentTarget.dataset.index;
    if (index === 0) return;
    
    const steps = this.data.formData.steps;
    [steps[index], steps[index - 1]] = [steps[index - 1], steps[index]];
    
    // 重新排序
    steps.forEach((step, i) => {
      step.order = i + 1;
    });
    
    this.setData({
      'formData.steps': steps
    });
  },

  moveStepDown(e) {
    const index = e.currentTarget.dataset.index;
    const steps = this.data.formData.steps;
    if (index === steps.length - 1) return;
    
    [steps[index], steps[index + 1]] = [steps[index + 1], steps[index]];
    
    // 重新排序
    steps.forEach((step, i) => {
      step.order = i + 1;
    });
    
    this.setData({
      'formData.steps': steps
    });
  },

  // 营养信息输入
  onNutritionInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = parseFloat(e.detail.value) || 0;
    
    this.setData({
      [`formData.nutrition.${field}`]: value
    });
  },

  // 标签管理
  showTagInput() {
    this.setData({ showTagInput: true });
  },

  onTagInput(e) {
    this.setData({
      newTag: e.detail.value
    });
  },

  addTag() {
    const { newTag, formData } = this.data;
    
    if (!newTag.trim()) {
      app.showError('请输入标签');
      return;
    }
    
    if (formData.tags.includes(newTag.trim())) {
      app.showError('标签已存在');
      return;
    }
    
    formData.tags.push(newTag.trim());
    
    this.setData({
      'formData.tags': formData.tags,
      newTag: '',
      showTagInput: false
    });
  },

  removeTag(e) {
    const index = e.currentTarget.dataset.index;
    const tags = this.data.formData.tags;
    tags.splice(index, 1);
    
    this.setData({
      'formData.tags': tags
    });
  },

  cancelTagInput() {
    this.setData({
      showTagInput: false,
      newTag: ''
    });
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;
    
    if (!formData.name.trim()) {
      app.showError('请输入菜谱名称');
      return false;
    }
    
    if (!formData.category) {
      app.showError('请选择菜谱分类');
      return false;
    }
    
    if (formData.ingredients.length === 0) {
      app.showError('请添加至少一个食材');
      return false;
    }
    
    if (formData.steps.length === 0) {
      app.showError('请添加至少一个制作步骤');
      return false;
    }
    
    return true;
  },

  // 保存菜谱
  async saveRecipe() {
    if (!this.validateForm()) {
      return;
    }
    
    try {
      this.setData({ loading: true });
      
      const { isEdit, recipeId, formData } = this.data;
      
      if (isEdit) {
        await app.api.updateRecipe(recipeId, formData);
        app.showSuccess('菜谱更新成功');
      } else {
        await app.api.createRecipe(formData);
        app.showSuccess('菜谱创建成功');
      }
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      
    } catch (error) {
      console.error('保存菜谱失败:', error);
      app.showError('保存菜谱失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 预览菜谱
  previewRecipe() {
    if (!this.validateForm()) {
      return;
    }
    
    wx.navigateTo({
      url: `/pages/recipes/preview/preview?data=${encodeURIComponent(JSON.stringify(this.data.formData))}`
    });
  },

  // 重置表单
  resetForm() {
    wx.showModal({
      title: '确认重置',
      content: '重置后将清空所有已填写的内容，确定要重置吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            formData: {
              name: '',
              description: '',
              category: '',
              difficulty: 1,
              cookTime: 30,
              servings: 2,
              image: '',
              ingredients: [],
              steps: [],
              nutrition: {
                calories: 0,
                protein: 0,
                carbs: 0,
                fat: 0,
                fiber: 0
              },
              tags: []
            },
            newIngredient: { name: '', amount: '', unit: '' },
            newStep: '',
            newTag: ''
          });
          
          app.showSuccess('表单已重置');
        }
      }
    });
  },

  // 获取分类名称
  getCategoryName(categoryId) {
    const category = this.data.categories.find(c => c.id === categoryId);
    return category ? category.name : '请选择';
  },

  // 获取难度名称
  getDifficultyName(difficulty) {
    const diff = this.data.difficulties.find(d => d.value === difficulty);
    return diff ? diff.name : '简单';
  }
});