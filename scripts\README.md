# 数据库字符集修复脚本

## 问题描述

出现以下错误时需要运行此修复脚本：
```
Error: Conversion from collation utf8mb4_general_ci into latin1_swedish_ci impossible for parameter
```

这个错误是由于数据库表的字符集配置不一致导致的。

## 解决方案

### 方法一：使用Node.js脚本（推荐）

1. 确保项目依赖已安装：
```bash
npm install
```

2. 运行字符集修复脚本：
```bash
node scripts/fix-charset.js
```

### 方法二：使用SQL脚本

1. 连接到MySQL数据库
2. 执行SQL脚本：
```bash
mysql -u root -p smart_fridge < scripts/fix-charset.sql
```

## 修复内容

脚本将执行以下操作：

1. **修改数据库默认字符集**
   - 将数据库字符集设置为 `utf8mb4`
   - 将排序规则设置为 `utf8mb4_unicode_ci`

2. **修复所有表的字符集**
   - `users` - 用户表
   - `ingredient_categories` - 食材分类表
   - `user_ingredients` - 用户食材表
   - `storage_locations` - 存储位置表
   - `units` - 单位表

3. **修复所有文本字段的字符集**
   - VARCHAR 字段
   - CHAR 字段
   - TEXT 字段
   - MEDIUMTEXT 字段
   - LONGTEXT 字段

## 验证修复结果

修复完成后，脚本会自动检查：

1. 所有表的字符集是否为 `utf8mb4_unicode_ci`
2. 所有文本字段的字符集是否正确
3. 显示修复状态报告

## 注意事项

1. **备份数据**：运行脚本前请备份数据库
2. **停止应用**：修复期间建议停止应用服务
3. **权限要求**：需要数据库管理员权限
4. **字符集兼容**：utf8mb4 向下兼容 utf8

## 常见问题

### Q: 修复后仍然报错怎么办？
A: 
1. 检查数据库连接配置中的字符集设置
2. 重启应用服务
3. 清除数据库连接池

### Q: 修复过程中出现错误？
A: 
1. 检查数据库权限
2. 确认数据库服务正常运行
3. 查看具体错误信息，可能需要手动处理特殊字段

### Q: 如何预防此类问题？
A: 
1. 创建数据库时指定正确的字符集
2. 创建表时明确指定字符集
3. 在应用配置中统一字符集设置

## 相关文件

- `config/database.js` - 数据库连接配置
- `routes/ingredients.js` - 食材路由（已修复参数处理）
- `scripts/fix-charset.js` - Node.js修复脚本
- `scripts/fix-charset.sql` - SQL修复脚本
