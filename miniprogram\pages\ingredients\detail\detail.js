// 食材详情页面逻辑
const app = getApp();

Page({
  data: {
    ingredient: null,
    loading: true,
    statusInfo: null,
    showActions: false,
    relatedRecipes: []
  },

  onLoad(options) {
    console.log('食材详情页面加载', options);
    if (options.id) {
      this.loadIngredientDetail(options.id);
      this.loadRelatedRecipes(options.id);
    } else {
      app.showError('参数错误');
      wx.navigateBack();
    }
  },

  onShow() {
    // 页面显示时重新加载数据，以防数据被修改
    if (this.data.ingredient) {
      this.loadIngredientDetail(this.data.ingredient.id);
    }
  },

  // 加载食材详情
  async loadIngredientDetail(id) {
    this.setData({ loading: true });

    try {
      const res = await app.request({
        url: `/ingredients/${id}`
      });

      const ingredient = res.data;
      const statusInfo = app.getIngredientStatus(ingredient.expire_date);

      // 格式化时间显示
      if (ingredient.expire_date) {
        // 过期时间使用状态信息中的文本，更加直观
        ingredient.expire_date_formatted = statusInfo.text;
      }
      if (ingredient.purchase_date) {
        ingredient.purchase_date_formatted = this.formatFriendlyDate(ingredient.purchase_date);
      }
      if (ingredient.created_at) {
        ingredient.created_at_formatted = this.formatFriendlyDateTime(ingredient.created_at);
      }

      this.setData({
        ingredient,
        statusInfo
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: ingredient.name
      });

    } catch (error) {
      console.error('加载食材详情失败:', error);
      app.showError('加载失败');
      wx.navigateBack();
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载相关菜谱
  async loadRelatedRecipes(ingredientId) {
    try {
      const res = await app.request({
        url: `/recipes/by-ingredient/${ingredientId}`,
        data: { limit: 5 }
      });

      this.setData({
        relatedRecipes: res.data.recipes || []
      });
    } catch (error) {
      console.error('加载相关菜谱失败:', error);
    }
  },

  // 显示操作菜单
  showActionSheet() {
    const itemList = ['编辑食材', '删除食材', '分享食材'];
    
    wx.showActionSheet({
      itemList,
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.editIngredient();
            break;
          case 1:
            this.deleteIngredient();
            break;
          case 2:
            this.shareIngredient();
            break;
        }
      }
    });
  },

  // 编辑食材
  editIngredient() {
    wx.navigateTo({
      url: `/pages/ingredients/add/add?id=${this.data.ingredient.id}`
    });
  },

  // 删除食材
  async deleteIngredient() {
    const confirmed = await app.showConfirm({
      title: '确认删除',
      content: `确定要删除食材"${this.data.ingredient.name}"吗？`
    });

    if (!confirmed) return;

    try {
      await app.request({
        url: `/ingredients/${this.data.ingredient.id}`,
        method: 'DELETE'
      });

      app.showSuccess('删除成功');
      wx.navigateBack();
    } catch (error) {
      console.error('删除食材失败:', error);
      app.showError('删除失败');
    }
  },

  // 分享食材
  shareIngredient() {
    const { ingredient } = this.data;
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 预览图片
  previewImage() {
    if (this.data.ingredient.image_url) {
      wx.previewImage({
        urls: [this.data.ingredient.image_url],
        current: this.data.ingredient.image_url
      });
    }
  },

  // 查看菜谱详情
  viewRecipeDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/recipes/detail/detail?id=${id}`
    });
  },

  // 添加到购物清单
  async addToShoppingList() {
    try {
      const { ingredient } = this.data;
      
      await app.request({
        url: '/shopping',
        method: 'POST',
        data: {
          name: ingredient.name,
          category: ingredient.category,
          quantity: 1,
          unit: ingredient.unit,
          notes: `来自食材：${ingredient.name}`
        }
      });

      app.showSuccess('已添加到购物清单');
    } catch (error) {
      console.error('添加到购物清单失败:', error);
      app.showError('添加失败');
    }
  },

  // 设置提醒
  async setReminder() {
    const { ingredient, statusInfo } = this.data;
    
    if (statusInfo.status === 3) {
      app.showError('食材已过期，无需设置提醒');
      return;
    }

    try {
      // 计算提醒时间（过期前1天）
      const expireDate = new Date(ingredient.expire_date);
      const reminderDate = new Date(expireDate.getTime() - 24 * 60 * 60 * 1000);
      const now = new Date();

      if (reminderDate <= now) {
        app.showError('提醒时间已过，无法设置');
        return;
      }

      // 这里可以调用系统通知API或后端提醒服务
      app.showSuccess('提醒设置成功');
    } catch (error) {
      console.error('设置提醒失败:', error);
      app.showError('设置失败');
    }
  },

  // 复制食材信息
  copyIngredientInfo() {
    const { ingredient } = this.data;
    const info = `食材名称：${ingredient.name}\n分类：${ingredient.category}\n数量：${ingredient.quantity}${ingredient.unit}\n过期时间：${ingredient.expire_date}`;
    
    wx.setClipboardData({
      data: info,
      success: () => {
        app.showSuccess('已复制到剪贴板');
      }
    });
  },

  // 查看历史记录
  viewHistory() {
    wx.navigateTo({
      url: `/pages/records/list/list?ingredient_id=${this.data.ingredient.id}`
    });
  },

  // 分享给好友
  onShareAppMessage() {
    const { ingredient } = this.data;
    return {
      title: `我的食材：${ingredient.name}`,
      desc: `${ingredient.category} | ${ingredient.quantity}${ingredient.unit} | ${ingredient.expire_date}到期`,
      path: `/pages/ingredients/detail/detail?id=${ingredient.id}`,
      imageUrl: ingredient.image_url
    };
  },

  // 图片加载错误处理
  onImageError(e) {
    const ingredient = this.data.ingredient;
    if (ingredient) {
      ingredient.image_url = 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=200&h=200&fit=crop&crop=center';
      this.setData({ ingredient });
      console.warn(`食材图片加载失败，使用备用图片: ${ingredient.name}`);
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    const { ingredient } = this.data;
    return {
      title: `我的食材：${ingredient.name}`,
      query: `id=${ingredient.id}`,
      imageUrl: ingredient.image_url
    };
  },

  // 格式化友好的日期显示
  formatFriendlyDate(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    const diffDays = Math.floor((targetDate - today) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return '今天';
    } else if (diffDays === 1) {
      return '明天';
    } else if (diffDays === -1) {
      return '昨天';
    } else if (diffDays > 1 && diffDays <= 7) {
      return `${diffDays}天后`;
    } else if (diffDays < -1 && diffDays >= -7) {
      return `${Math.abs(diffDays)}天前`;
    } else {
      // 超过一周的显示具体日期
      return app.formatDate(date, 'YYYY-MM-DD');
    }
  },

  // 格式化友好的日期时间显示
  formatFriendlyDateTime(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) {
      return '刚刚';
    } else if (diffMins < 60) {
      return `${diffMins}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays === 1) {
      return `昨天 ${app.formatDate(date, 'HH:mm')}`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return app.formatDate(date, 'MM-DD HH:mm');
    }
  }
});