/* 个人中心页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 30rpx;
  color: white;
  position: relative;
}

.user-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.1);
}

.edit-avatar-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.edit-avatar-btn:active {
  transform: scale(0.95);
}

.edit-avatar-btn image {
  width: 24rpx;
  height: 24rpx;
  filter: hue-rotate(90deg);
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.user-email {
  font-size: 26rpx;
  opacity: 0.9;
}

.join-date {
  font-size: 22rpx;
  opacity: 0.7;
  margin-top: 8rpx;
}

.edit-profile-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.edit-profile-btn:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.edit-profile-btn image {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

/* 通用区域样式 */
.stats-section,
.menu-section,
.settings-section,
.other-section,
.danger-section {
  margin: 30rpx 30rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.danger-title {
  color: #ff4757;
}

/* 数据统计 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  background-color: white;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  transition: all 0.3s ease;
}

.stats-item:active {
  transform: scale(0.95);
}

.stats-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ingredients-icon {
  background-color: rgba(76, 175, 80, 0.1);
}

.recipes-icon {
  background-color: rgba(255, 152, 0, 0.1);
}

.shopping-icon {
  background-color: rgba(33, 150, 243, 0.1);
}

.favorites-icon {
  background-color: rgba(233, 30, 99, 0.1);
}

.stats-icon image {
  width: 32rpx;
  height: 32rpx;
}

.stats-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 功能菜单 */
.menu-list {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9fa;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.analysis-icon {
  background-color: rgba(156, 39, 176, 0.1);
}

.backup-icon {
  background-color: rgba(0, 150, 136, 0.1);
}

.restore-icon {
  background-color: rgba(255, 87, 34, 0.1);
}

.menu-icon image {
  width: 32rpx;
  height: 32rpx;
}

.menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.menu-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.menu-desc {
  font-size: 24rpx;
  color: #999;
}

.menu-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
}

.menu-arrow image {
  width: 20rpx;
  height: 20rpx;
}

/* 设置选项 */
.settings-list {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.notifications-icon {
  background-color: rgba(255, 193, 7, 0.1);
}

.dark-mode-icon {
  background-color: rgba(96, 125, 139, 0.1);
}

.sync-icon {
  background-color: rgba(3, 169, 244, 0.1);
}

.setting-icon image {
  width: 28rpx;
  height: 28rpx;
}

.setting-content {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.setting-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.setting-desc {
  font-size: 24rpx;
  color: #999;
}

.setting-switch {
  transform: scale(0.8);
}

/* 其他选项 */
.other-list {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.other-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.other-item:last-child {
  border-bottom: none;
}

.other-item:active {
  background-color: #f8f9fa;
}

.other-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.help-icon {
  background-color: rgba(76, 175, 80, 0.1);
}

.feedback-icon {
  background-color: rgba(255, 152, 0, 0.1);
}

.about-icon {
  background-color: rgba(33, 150, 243, 0.1);
}

.privacy-icon {
  background-color: rgba(121, 85, 72, 0.1);
}

.update-icon {
  background-color: rgba(233, 30, 99, 0.1);
}

.other-icon image {
  width: 28rpx;
  height: 28rpx;
}

.other-title {
  flex: 1;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.other-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
}

.other-arrow image {
  width: 20rpx;
  height: 20rpx;
}

/* 危险操作区域 */
.danger-list {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid rgba(255, 71, 87, 0.1);
}

.danger-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  transition: all 0.3s ease;
}

.danger-item:active {
  background-color: rgba(255, 71, 87, 0.05);
}

.danger-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 10rpx;
  background-color: rgba(255, 71, 87, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.danger-icon image {
  width: 28rpx;
  height: 28rpx;
  filter: hue-rotate(340deg);
}

.danger-item .danger-title {
  flex: 1;
  font-size: 30rpx;
  font-weight: 500;
  color: #ff4757;
}

.danger-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
}

.danger-arrow image {
  width: 20rpx;
  height: 20rpx;
  filter: hue-rotate(340deg);
}

/* 退出登录 */
.logout-section {
  margin: 40rpx 30rpx 0;
}

.logout-btn {
  width: 100%;
  background-color: #ff4757;
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.logout-btn:active {
  background-color: #e84142;
  transform: scale(0.98);
}

/* 版本信息 */
.version-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  margin-top: 40rpx;
  padding: 0 30rpx;
}

.version-text {
  font-size: 24rpx;
  color: #999;
}

.copyright-text {
  font-size: 22rpx;
  color: #ccc;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-container {
  background-color: white;
  border-radius: 16rpx;
  padding: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .user-card {
    padding: 30rpx 20rpx;
    gap: 20rpx;
  }
  
  .avatar-img {
    width: 100rpx;
    height: 100rpx;
  }
  
  .user-name {
    font-size: 32rpx;
  }
  
  .user-email {
    font-size: 24rpx;
  }
  
  .join-date {
    font-size: 20rpx;
  }
  
  .stats-section,
  .menu-section,
  .settings-section,
  .other-section,
  .danger-section {
    margin: 20rpx 20rpx 0;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30rpx;
    padding: 40rpx 20rpx;
  }
  
  .stats-item {
    gap: 16rpx;
  }
  
  .stats-icon {
    width: 80rpx;
    height: 80rpx;
  }
  
  .stats-icon image {
    width: 40rpx;
    height: 40rpx;
  }
  
  .stats-number {
    font-size: 36rpx;
  }
  
  .stats-label {
    font-size: 26rpx;
  }
  
  .menu-item,
  .setting-item,
  .other-item,
  .danger-item {
    padding: 24rpx 20rpx;
  }
  
  .menu-icon,
  .setting-icon,
  .other-icon,
  .danger-icon {
    width: 60rpx;
    height: 60rpx;
    margin-right: 20rpx;
  }
  
  .menu-icon image,
  .setting-icon image,
  .other-icon image,
  .danger-icon image {
    width: 32rpx;
    height: 32rpx;
  }
  
  .menu-title,
  .setting-title,
  .other-title {
    font-size: 28rpx;
  }
  
  .menu-desc,
  .setting-desc {
    font-size: 22rpx;
  }
  
  .logout-section {
    margin: 30rpx 20rpx 0;
  }
  
  .logout-btn {
    padding: 20rpx;
    font-size: 30rpx;
  }
  
  .version-info {
    margin-top: 30rpx;
    padding: 0 20rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .stats-grid,
  .menu-list,
  .settings-list,
  .other-list,
  .danger-list,
  .loading-container {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .menu-item:active,
  .other-item:active {
    background-color: #404040;
  }
  
  .danger-item:active {
    background-color: rgba(255, 71, 87, 0.1);
  }
  
  .section-title,
  .stats-number,
  .menu-title,
  .setting-title,
  .other-title {
    color: white;
  }
  
  .stats-label,
  .menu-desc,
  .setting-desc {
    color: #999;
  }
  
  .menu-item,
  .setting-item,
  .other-item {
    border-color: #404040;
  }
  
  .loading-text {
    color: #ccc;
  }
}

/* 动画效果 */
.stats-item,
.menu-item,
.setting-item,
.other-item,
.danger-item {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 开关动画 */
.setting-switch {
  transition: all 0.3s ease;
}

/* 头像编辑按钮动画 */
.edit-avatar-btn {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}
/* Emoji图标样式 */
.emoji-icon {
  font-size: 32rpx;
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
}

/* 小尺寸emoji图标 */
.emoji-icon.small {
  font-size: 24rpx;
}

/* 大尺寸emoji图标 */
.emoji-icon.large {
  font-size: 48rpx;
}

/* 图标与文字对齐 */
.emoji-icon.inline {
  margin-right: 8rpx;
}
