<!--购物清单列表页面-->
<view class="page-container">
  <!-- 头部统计 -->
  <view class="header-stats">
    <view class="stats-card">
      <view class="stats-item">
        <text class="stats-number">{{shoppingList.length}}</text>
        <text class="stats-label">总计</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{completedCount}}</text>
        <text class="stats-label">已购买</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">¥{{totalAmountText}}</text>
        <text class="stats-label">预计消费</text>
      </view>
    </view>
    
    <view class="header-actions">
      <view class="action-btn" bindtap="showActionSheet">
        <text class="emoji-icon">⋯</text>
      </view>
    </view>
  </view>

  <!-- 工具栏 -->
  <view class="toolbar">
    <view class="toolbar-left">
      <view class="sort-options">
        <view
          class="sort-btn {{sortType === 'category_id' ? 'active' : ''}}"
          data-sort="category"
          bindtap="changeSortType"
        >
          分类
        </view>
        <view
          class="sort-btn {{sortType === 'name' ? 'active' : ''}}"
          data-sort="name"
          bindtap="changeSortType"
        >
          名称
        </view>
        <view
          class="sort-btn {{sortType === 'created_at' ? 'active' : ''}}"
          data-sort="date"
          bindtap="changeSortType"
        >
          时间
        </view>
      </view>
    </view>

    <view class="toolbar-right">
      <view class="toggle-btn {{showCompleted ? 'active' : ''}}" bindtap="toggleShowCompleted">
        <image src="/images/icons/eye.png" mode="aspectFit" class="toggle-icon"></image>
        <text class="toggle-text">显示已完成</text>
      </view>
      
      <view class="edit-btn" bindtap="{{editMode ? 'exitEditMode' : 'enterEditMode'}}">
        <text class="edit-text">{{editMode ? '完成' : '编辑'}}</text>
      </view>
    </view>
  </view>

  <!-- 购物清单内容 -->
  <view class="shopping-content">
    <!-- 按分类显示 -->
    <view class="category-section" wx:if="{{sortType === 'category'}}">
      <block wx:for="{{categories}}" wx:key="*this" wx:for-item="categoryItems" wx:for-index="categoryName">
        <view class="category-group" wx:if="{{categoryItems.length > 0}}">
          <view class="category-header">
            <text class="category-name">{{categoryName}}</text>
            <text class="category-count">({{categoryItems.length}})</text>
          </view>
          
          <view class="items-list">
            <view 
              class="shopping-item {{item.is_purchased ? 'completed' : ''}} {{editMode ? 'edit-mode' : ''}}"
              wx:for="{{categoryItems}}"
              wx:key="id"
              wx:for-item="item"
            >
              <!-- 选择框（编辑模式） -->
              <view class="item-checkbox" wx:if="{{editMode}}" bindtap="toggleSelectItem" data-id="{{item.id}}">
                <view class="checkbox {{selectedItems.indexOf(item.id) > -1 ? 'checked' : ''}}">
                  <text class="emoji-icon">✅</text> -1}}"></image>
                </view>
              </view>

              <!-- 完成状态按钮 -->
              <view class="item-status" wx:if="{{!editMode}}" bindtap="toggleItemStatus" data-id="{{item.id}}" data-index="{{index}}">
                <view class="status-circle {{item.is_purchased ? 'completed' : ''}}">
                  <text class="emoji-icon">✅</text>
                </view>
              </view>

              <!-- 项目信息 -->
              <view class="item-info" bindtap="editItem" data-id="{{item.id}}" wx:if="{{!editMode}}">
                <view class="item-main">
                  <text class="item-name {{item.is_purchased ? 'completed-text' : ''}}">{{item.name}}</text>
                  <text class="item-quantity">{{item.quantity}}{{item.unit}}</text>
                </view>
                
                <view class="item-meta" wx:if="{{item.price || item.notes}}">
                  <text class="item-price" wx:if="{{item.price}}">¥{{item.totalPrice}}</text>
                  <text class="item-notes" wx:if="{{item.notes}}">{{item.notes}}</text>
                </view>
                
                <view class="item-time" wx:if="{{item.purchased_at}}">
                  <text class="purchased-time">已购买 {{item.purchased_at}}</text>
                </view>
              </view>

              <!-- 编辑模式信息显示 -->
              <view class="item-info-edit" wx:if="{{editMode}}">
                <text class="item-name">{{item.name}}</text>
                <text class="item-quantity">{{item.quantity}}{{item.unit}}</text>
              </view>

              <!-- 操作按钮 -->
              <view class="item-actions" wx:if="{{!editMode}}">
                <view class="action-btn edit-item-btn" bindtap="editItem" data-id="{{item.id}}">
                  <text class="emoji-icon">✏️</text>
                </view>
                <view class="action-btn delete-item-btn" bindtap="deleteItem" data-id="{{item.id}}" data-name="{{item.name}}">
                  <image src="/images/icons/delete.png" mode="aspectFit"></image>
                </view>
              </view>
            </view>
          </view>
        </view>
      </block>
    </view>

    <!-- 列表显示 -->
    <view class="list-section" wx:if="{{sortType !== 'category_id'}}">
      <view class="items-list">
        <view 
          class="shopping-item {{item.is_purchased ? 'completed' : ''}} {{editMode ? 'edit-mode' : ''}}"
          wx:for="{{shoppingList}}"
          wx:key="id"
        >
          <!-- 选择框（编辑模式） -->
          <view class="item-checkbox" wx:if="{{editMode}}" bindtap="toggleSelectItem" data-id="{{item.id}}">
            <view class="checkbox {{selectedItems.indexOf(item.id) > -1 ? 'checked' : ''}}">
              <text class="emoji-icon">✅</text> -1}}"></image>
            </view>
          </view>

          <!-- 完成状态按钮 -->
          <view class="item-status" wx:if="{{!editMode}}" bindtap="toggleItemStatus" data-id="{{item.id}}" data-index="{{index}}">
            <view class="status-circle {{item.is_purchased ? 'completed' : ''}}">
              <text class="emoji-icon">✅</text>
            </view>
          </view>

          <!-- 项目信息 -->
          <view class="item-info" bindtap="editItem" data-id="{{item.id}}" wx:if="{{!editMode}}">
            <view class="item-main">
              <text class="item-name {{item.is_purchased ? 'completed-text' : ''}}">{{item.name}}</text>
              <text class="item-category">{{item.category}}</text>
              <text class="item-quantity">{{item.quantity}}{{item.unit}}</text>
            </view>
            
            <view class="item-meta" wx:if="{{item.price || item.notes}}">
              <text class="item-price" wx:if="{{item.price}}">¥{{item.totalPrice}}</text>
              <text class="item-notes" wx:if="{{item.notes}}">{{item.notes}}</text>
            </view>
            
            <view class="item-time" wx:if="{{item.purchased_at}}">
              <text class="purchased-time">已购买 {{item.purchased_at}}</text>
            </view>
          </view>

          <!-- 编辑模式信息显示 -->
          <view class="item-info-edit" wx:if="{{editMode}}">
            <text class="item-name">{{item.name}}</text>
            <text class="item-category">{{item.category}}</text>
            <text class="item-quantity">{{item.quantity}}{{item.unit}}</text>
          </view>

          <!-- 操作按钮 -->
          <view class="item-actions" wx:if="{{!editMode}}">
            <view class="action-btn edit-item-btn" bindtap="editItem" data-id="{{item.id}}">
              <text class="emoji-icon">✏️</text>
            </view>
            <view class="action-btn delete-item-btn" bindtap="deleteItem" data-id="{{item.id}}" data-name="{{item.name}}">
              <image src="/images/icons/delete.png" mode="aspectFit"></image>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && shoppingList.length === 0}}">
      <image src="/images/empty-shopping.png" mode="aspectFit" class="empty-image"></image>
      <text class="empty-text">购物清单为空</text>
      <text class="empty-desc">快来添加需要购买的商品吧</text>
      <button class="btn btn-primary empty-btn" bindtap="addNewItem">
        添加商品
      </button>
    </view>
  </view>

  <!-- 编辑模式底部操作栏 -->
  <view class="edit-toolbar" wx:if="{{editMode}}">
    <view class="edit-actions">
      <view class="select-all-btn" bindtap="toggleSelectAll">
        <view class="checkbox {{selectedItems.length === shoppingList.length && shoppingList.length > 0 ? 'checked' : ''}}">
          <text class="emoji-icon">✅</text> 0}}"></image>
        </view>
        <text class="select-all-text">全选</text>
      </view>

      <view class="edit-buttons">
        <button class="btn btn-secondary edit-action-btn" bindtap="batchMarkPurchased" disabled="{{selectedItems.length === 0}}">
          标记已购买
        </button>
        <button class="btn btn-danger edit-action-btn" bindtap="batchDelete" disabled="{{selectedItems.length === 0}}">
          删除选中
        </button>
      </view>
    </view>
  </view>

  <!-- 悬浮添加按钮 -->
  <view class="fab-btn" bindtap="addNewItem" wx:if="{{!editMode}}">
    <image src="/images/icons/add.png" mode="aspectFit"></image>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading && !refreshing}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>