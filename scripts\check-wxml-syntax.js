// 检查和修复WXML语法错误
const fs = require('fs');
const path = require('path');

// 需要检查的目录
const DIRECTORIES = ['miniprogram/pages', 'miniprogram/components'];

// 常见的语法错误模式
const SYNTAX_ERROR_PATTERNS = [
  {
    name: '错误的结束标签组合',
    pattern: /<text[^>]*>[^<]*<\/text><\/image>/g,
    fix: (match) => match.replace('</image>', ''),
    description: '</text></image> -> </text>'
  },
  {
    name: '错误的结束标签组合',
    pattern: /<text[^>]*>[^<]*<\/text><\/view>/g,
    fix: (match) => match.replace('</view>', ''),
    description: '</text></view> -> </text> (如果不在view内)'
  },
  {
    name: '未闭合的标签',
    pattern: /<text[^>]*>[^<]*$/gm,
    fix: (match) => match + '</text>',
    description: '添加缺失的</text>标签'
  },
  {
    name: '多余的结束标签',
    pattern: /<\/image>\s*<\/image>/g,
    fix: (match) => '</image>',
    description: '移除重复的</image>标签'
  }
];

// 递归获取所有WXML文件
function getAllWxmlFiles(dir, fileList = []) {
  if (!fs.existsSync(dir)) {
    return fileList;
  }
  
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      getAllWxmlFiles(filePath, fileList);
    } else if (file.endsWith('.wxml')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// 检查单个文件的语法错误
function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // 检查各种语法错误模式
    SYNTAX_ERROR_PATTERNS.forEach(pattern => {
      const matches = content.match(pattern.pattern);
      if (matches) {
        matches.forEach(match => {
          issues.push({
            type: pattern.name,
            match: match,
            fix: pattern.fix(match),
            description: pattern.description,
            line: getLineNumber(content, match)
          });
        });
      }
    });
    
    // 特殊检查：</text></image> 模式
    const textImagePattern = /<text[^>]*>([^<]*)<\/text><\/image>/g;
    let match;
    while ((match = textImagePattern.exec(content)) !== null) {
      issues.push({
        type: '错误的标签嵌套',
        match: match[0],
        fix: match[0].replace('</image>', ''),
        description: '移除多余的</image>标签',
        line: getLineNumber(content, match[0])
      });
    }
    
    return issues;
  } catch (error) {
    console.error(`检查文件失败: ${filePath}`, error.message);
    return [];
  }
}

// 修复单个文件的语法错误
function fixFile(filePath, issues) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    issues.forEach(issue => {
      if (content.includes(issue.match)) {
        content = content.replace(issue.match, issue.fix);
        modified = true;
        console.log(`  ✅ 修复: ${issue.description}`);
        console.log(`    ${issue.match} -> ${issue.fix}`);
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`修复文件失败: ${filePath}`, error.message);
    return false;
  }
}

// 获取匹配内容在文件中的行号
function getLineNumber(content, searchText) {
  const lines = content.split('\n');
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes(searchText)) {
      return i + 1;
    }
  }
  return -1;
}

// 主检查函数
function checkSyntax() {
  console.log('🔍 开始检查WXML语法错误...\n');
  
  let totalFiles = 0;
  let filesWithIssues = 0;
  let totalIssues = 0;
  
  DIRECTORIES.forEach(dir => {
    const fullDir = path.resolve(dir);
    
    if (!fs.existsSync(fullDir)) {
      console.log(`⚠️  目录不存在: ${dir}`);
      return;
    }
    
    console.log(`📁 检查目录: ${dir}`);
    const files = getAllWxmlFiles(fullDir);
    
    files.forEach(filePath => {
      totalFiles++;
      const relativePath = path.relative(process.cwd(), filePath);
      
      const issues = checkFile(filePath);
      if (issues.length > 0) {
        filesWithIssues++;
        totalIssues += issues.length;
        
        console.log(`\n❌ 发现问题: ${relativePath}`);
        issues.forEach((issue, index) => {
          console.log(`  ${index + 1}. ${issue.type} (第${issue.line}行)`);
          console.log(`     问题: ${issue.match}`);
          console.log(`     修复: ${issue.fix}`);
        });
      }
    });
  });
  
  console.log(`\n📊 检查结果:`);
  console.log(`  总文件数: ${totalFiles}`);
  console.log(`  有问题文件数: ${filesWithIssues}`);
  console.log(`  总问题数: ${totalIssues}`);
  
  return { totalFiles, filesWithIssues, totalIssues };
}

// 主修复函数
function fixSyntax() {
  console.log('🔧 开始修复WXML语法错误...\n');
  
  let totalFiles = 0;
  let fixedFiles = 0;
  let totalFixed = 0;
  
  DIRECTORIES.forEach(dir => {
    const fullDir = path.resolve(dir);
    
    if (!fs.existsSync(fullDir)) {
      console.log(`⚠️  目录不存在: ${dir}`);
      return;
    }
    
    console.log(`📁 修复目录: ${dir}`);
    const files = getAllWxmlFiles(fullDir);
    
    files.forEach(filePath => {
      totalFiles++;
      const relativePath = path.relative(process.cwd(), filePath);
      
      const issues = checkFile(filePath);
      if (issues.length > 0) {
        console.log(`\n🔧 修复文件: ${relativePath}`);
        if (fixFile(filePath, issues)) {
          fixedFiles++;
          totalFixed += issues.length;
        }
      }
    });
  });
  
  console.log(`\n📊 修复结果:`);
  console.log(`  总文件数: ${totalFiles}`);
  console.log(`  修复文件数: ${fixedFiles}`);
  console.log(`  修复问题数: ${totalFixed}`);
  
  if (fixedFiles > 0) {
    console.log('\n✅ 语法错误修复完成！');
    console.log('\n💡 建议:');
    console.log('1. 重新编译小程序项目');
    console.log('2. 检查开发者工具是否还有编译错误');
    console.log('3. 测试页面功能是否正常');
  } else {
    console.log('\n✅ 没有发现需要修复的语法错误');
  }
}

// 命令行参数处理
const command = process.argv[2];

switch (command) {
  case 'check':
    checkSyntax();
    break;
  case 'fix':
  default:
    fixSyntax();
    break;
}

module.exports = { checkSyntax, fixSyntax, SYNTAX_ERROR_PATTERNS };
