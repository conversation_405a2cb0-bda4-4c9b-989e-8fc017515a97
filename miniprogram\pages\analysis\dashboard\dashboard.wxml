<!--数据分析仪表板页面-->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <view class="header-title">数据分析</view>
      <view class="header-actions">
        <image class="action-icon" src="/images/icons/refresh.png" bindtap="refreshData" />
        <image class="action-icon" src="/images/icons/settings.png" />
      </view>
    </view>
    
    <!-- 时间范围选择 -->
    <view class="time-range-tabs">
      <view 
        wx:for="{{timeRanges}}" 
        wx:key="id"
        class="time-tab {{currentTimeRange === item.id ? 'active' : ''}}"
        data-range="{{item.id}}"
        bindtap="switchTimeRange"
      >
        {{item.name}}
      </view>
    </view>
  </view>

  <!-- 内容滚动区域 -->
  <scroll-view class="content-scroll" scroll-y enhanced show-scrollbar="{{false}}" refresher-enabled bindrefresherrefresh="refreshData" refresher-triggered="{{refreshing}}">
    
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 主要内容 -->
    <view wx:else class="dashboard-content">
      
      <!-- 核心统计卡片 -->
      <view class="stats-grid">
        <!-- 食材统计 -->
        <view class="stat-card" data-type="ingredients" bindtap="viewDetailStats">
          <view class="stat-header">
            <image class="stat-icon" src="🥬" />
            <text class="stat-title">食材管理</text>
          </view>
          <view class="stat-content">
            <view class="stat-number">{{stats.totalIngredients}}</view>
            <view class="stat-label">总食材数</view>
          </view>
          <view class="stat-footer">
            <view class="stat-warning" wx:if="{{stats.expiringSoon > 0}}" bindtap="viewExpiringIngredients">
              <image class="warning-icon" src="⚠️" />
              <text>{{stats.expiringSoon}}个即将过期</text>
            </view>
            <view wx:else class="stat-normal">
              <text>食材状态良好</text>
            </view>
          </view>
        </view>

        <!-- 菜谱统计 -->
        <view class="stat-card" data-type="recipes" bindtap="viewDetailStats">
          <view class="stat-header">
            <image class="stat-icon" src="/images/icons/recipe.png" />
            <text class="stat-title">菜谱收藏</text>
          </view>
          <view class="stat-content">
            <view class="stat-number">{{stats.totalRecipes}}</view>
            <view class="stat-label">总菜谱数</view>
          </view>
          <view class="stat-footer">
            <view class="stat-info" bindtap="viewFavoriteRecipes">
              <image class="info-icon" src="🤍" />
              <text>{{stats.favoriteRecipes}}个收藏</text>
            </view>
          </view>
        </view>

        <!-- 购物统计 -->
        <view class="stat-card" data-type="shopping" bindtap="viewDetailStats">
          <view class="stat-header">
            <image class="stat-icon" src="🛒" />
            <text class="stat-title">购物清单</text>
          </view>
          <view class="stat-content">
            <view class="stat-number">{{stats.totalShopping}}</view>
            <view class="stat-label">待购买项</view>
          </view>
          <view class="stat-footer">
            <view class="stat-progress" bindtap="viewShoppingList">
              <text>已完成 {{stats.completedShopping}}/{{stats.totalShopping}}</text>
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{stats.totalShopping > 0 ? (stats.completedShopping / stats.totalShopping * 100) : 0}}%"></view>
              </view>
            </view>
          </view>
        </view>

        <!-- 健康评分 -->
        <view class="stat-card health-card" data-type="health" bindtap="viewDetailStats">
          <view class="stat-header">
            <image class="stat-icon" src="/images/icons/health.png" />
            <text class="stat-title">健康评分</text>
          </view>
          <view class="stat-content">
            <view class="health-score" style="color: {{getHealthScoreColor(stats.healthScore)}}">
              {{stats.healthScore}}
            </view>
            <view class="health-level" style="color: {{getHealthScoreColor(stats.healthScore)}}">
              {{getHealthScoreLevel(stats.healthScore)}}
            </view>
          </view>
          <view class="stat-footer">
            <view class="health-progress">
              <view class="health-bar">
                <view class="health-fill" style="width: {{stats.healthScore}}%; background-color: {{getHealthScoreColor(stats.healthScore)}}"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 支出统计 -->
      <view class="spending-section">
        <view class="section-header">
          <text class="section-title">{{getTimeRangeName(currentTimeRange)}}支出</text>
          <view class="spending-amount">{{formatMoney(stats.monthlySpending)}}</view>
        </view>
        <view class="spending-chart">
          <!-- 这里可以集成图表组件 -->
          <view class="chart-placeholder">
            <image class="chart-icon" src="/images/icons/chart.png" />
            <text class="chart-text">支出趋势图</text>
          </view>
        </view>
      </view>

      <!-- 快捷操作 -->
      <view class="quick-actions-section">
        <view class="section-header">
          <text class="section-title">快捷操作</text>
        </view>
        <view class="quick-actions-grid">
          <view 
            wx:for="{{quickActions}}" 
            wx:key="id"
            class="quick-action-item"
            data-action="{{item}}"
            bindtap="onQuickAction"
          >
            <image class="action-icon" src="{{item.icon}}" />
            <text class="action-name">{{item.name}}</text>
          </view>
        </view>
      </view>

      <!-- 数据洞察 -->
      <view class="insights-section">
        <view class="section-header">
          <text class="section-title">数据洞察</text>
        </view>
        <view class="insights-list">
          <view class="insight-item" wx:if="{{stats.expiringSoon > 0}}">
            <image class="insight-icon warning" src="⚠️" />
            <view class="insight-content">
              <text class="insight-title">食材过期提醒</text>
              <text class="insight-desc">有 {{stats.expiringSoon}} 个食材即将过期，建议尽快使用</text>
            </view>
          </view>
          
          <view class="insight-item" wx:if="{{stats.healthScore < 60}}">
            <image class="insight-icon danger" src="⚠️" />
            <view class="insight-content">
              <text class="insight-title">健康建议</text>
              <text class="insight-desc">当前健康评分较低，建议增加蔬菜和水果的摄入</text>
            </view>
          </view>
          
          <view class="insight-item" wx:if="{{stats.totalShopping > 10}}">
            <image class="insight-icon info" src="/images/icons/shopping-tip.png" />
            <view class="insight-content">
              <text class="insight-title">购物提醒</text>
              <text class="insight-desc">购物清单项目较多，建议分批采购以保证食材新鲜度</text>
            </view>
          </view>
          
          <view class="insight-item" wx:if="{{stats.favoriteRecipes > 20}}">
            <image class="insight-icon success" src="/images/icons/recipe-tip.png" />
            <view class="insight-content">
              <text class="insight-title">菜谱推荐</text>
              <text class="insight-desc">您收藏了很多菜谱，可以尝试制作一些新的料理</text>
            </view>
          </view>
        </view>
      </view>

    </view>
  </scroll-view>
</view>