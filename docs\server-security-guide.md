# 服务端安全处理指南

## 密码安全处理

### 1. 用户注册时的密码处理

```javascript
// Node.js + Express 示例
const bcrypt = require('bcrypt');
const saltRounds = 12;

app.post('/users/register', async (req, res) => {
  try {
    const { username, email, password } = req.body;
    
    // 1. 验证密码强度
    if (!isValidPassword(password)) {
      return res.status(400).json({
        success: false,
        message: '密码必须包含大小写字母、数字和特殊字符，长度8-20位'
      });
    }
    
    // 2. 检查用户是否已存在
    const existingUser = await User.findOne({ 
      $or: [{ username }, { email }] 
    });
    
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名或邮箱已存在'
      });
    }
    
    // 3. 加密密码
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    
    // 4. 保存用户
    const newUser = new User({
      username,
      email,
      password: hashedPassword,
      createdAt: new Date()
    });
    
    await newUser.save();
    
    res.json({
      success: true,
      message: '注册成功'
    });
    
  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 密码强度验证函数
function isValidPassword(password) {
  const minLength = 8;
  const maxLength = 20;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  return password.length >= minLength && 
         password.length <= maxLength &&
         hasUpperCase && 
         hasLowerCase && 
         hasNumbers && 
         hasSpecialChar;
}
```

### 2. 用户登录时的密码验证

```javascript
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');

// 登录限制：每15分钟最多5次尝试
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 最多5次尝试
  message: {
    success: false,
    message: '登录尝试次数过多，请15分钟后再试'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.post('/users/login', loginLimiter, async (req, res) => {
  try {
    const { username, password } = req.body;
    
    // 1. 查找用户
    const user = await User.findOne({ 
      $or: [{ username }, { email: username }] 
    });
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }
    
    // 2. 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password);
    
    if (!isValidPassword) {
      // 记录失败尝试
      await logFailedLogin(user._id, req.ip);
      
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }
    
    // 3. 生成JWT token
    const token = jwt.sign(
      { 
        userId: user._id, 
        username: user.username 
      },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    // 4. 记录成功登录
    await logSuccessfulLogin(user._id, req.ip);
    
    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user._id,
          username: user.username,
          email: user.email
        }
      }
    });
    
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});
```

### 3. 安全最佳实践

1. **密码存储**：
   - 永远不要存储明文密码
   - 使用bcrypt、scrypt或Argon2等安全哈希算法
   - 使用足够的盐值轮数（建议12+）

2. **输入验证**：
   - 验证所有用户输入
   - 防止SQL注入和XSS攻击
   - 限制输入长度和格式

3. **访问控制**：
   - 实现登录失败次数限制
   - 使用HTTPS传输
   - 实现JWT token过期机制

4. **日志记录**：
   - 记录所有登录尝试
   - 监控异常登录行为
   - 实现安全事件告警

### 4. 环境变量配置

```bash
# .env 文件
JWT_SECRET=your-super-secret-jwt-key-here
DB_CONNECTION_STRING=mongodb://localhost:27017/your-app
BCRYPT_SALT_ROUNDS=12
SESSION_SECRET=your-session-secret-here
```

### 5. 数据库用户模型示例

```javascript
const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 20
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true,
    minlength: 60 // bcrypt哈希长度
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  lastLoginAt: Date,
  loginAttempts: {
    type: Number,
    default: 0
  },
  lockUntil: Date
});

module.exports = mongoose.model('User', userSchema);
```

## 总结

在实际项目中，所有的密码处理、验证和安全控制都应该在服务端完成。客户端只负责收集用户输入并发送到服务端，服务端负责所有的安全验证和数据处理。
