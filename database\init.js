#!/usr/bin/env node

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🗄️  开始初始化数据库...');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  multipleStatements: true
};

// 创建数据库和表
async function initDatabase() {
  let connection;
  
  try {
    // 连接到MySQL服务器（不指定数据库）
    console.log('📡 连接到MySQL服务器...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ MySQL连接成功');
    
    // 创建数据库
    const dbName = process.env.DB_NAME || 'smart_fridge';
    console.log(`🏗️  创建数据库: ${dbName}`);
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log('✅ 数据库创建成功');
    
    // 选择数据库
    await connection.query(`USE \`${dbName}\``);
    
    // 读取并执行SQL文件
    const sqlFilePath = path.join(__dirname, 'init.sql');
    if (fs.existsSync(sqlFilePath)) {
      console.log('📄 读取SQL初始化文件...');
      const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
      
      // 使用query方法执行多语句SQL
      console.log('🔧 执行SQL初始化脚本...');
      try {
        await connection.query(sqlContent);
        console.log('✅ SQL脚本执行完成');
      } catch (error) {
        // 忽略表已存在的错误
        if (!error.message.includes('already exists') && !error.message.includes('Duplicate')) {
          console.warn('⚠️  SQL执行警告:', error.message);
        } else {
          console.log('ℹ️  表结构已存在，跳过创建');
        }
      }
    } else {
      console.log('⚠️  未找到init.sql文件，跳过表结构创建');
    }
    
    // 插入一些测试数据
    await insertTestData(connection);
    
    console.log('🎉 数据库初始化完成！');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    
    // 提供详细的错误信息和解决方案
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 解决方案:');
      console.error('   1. 确保MySQL服务正在运行');
      console.error('   2. 检查.env文件中的数据库配置');
      console.error('   3. 确认数据库端口和用户名密码正确');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 解决方案:');
      console.error('   1. 检查数据库用户名和密码');
      console.error('   2. 确保用户有创建数据库的权限');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('📡 数据库连接已关闭');
    }
  }
}

// 插入测试数据
async function insertTestData(connection) {
  try {
    console.log('📝 插入测试数据...');
    
    // 检查是否已有数据
    const [users] = await connection.query('SELECT COUNT(*) as count FROM users');
    if (users[0].count > 0) {
      console.log('ℹ️  数据库中已有数据，跳过测试数据插入');
      return;
    }
    
    // 插入测试用户
    await connection.query(`
      INSERT IGNORE INTO users (username, email, password, avatar, created_at) VALUES
      ('测试用户', '<EMAIL>', '$2a$10$rOzJqQjQjQjQjQjQjQjQjOzJqQjQjQjQjQjQjQjQjQjQjQjQjQjQjQ', '👤', NOW())
    `);
    
    // 获取用户ID
    const [userResult] = await connection.query('SELECT id FROM users WHERE username = "测试用户"');
    const userId = userResult[0].id;
    
    // 插入测试食材分类
    await connection.query(`
      INSERT IGNORE INTO ingredient_categories (name, icon, description) VALUES
      ('蔬菜', '🥬', '新鲜蔬菜类食材'),
      ('水果', '🍎', '新鲜水果类食材'),
      ('肉类', '🥩', '肉类食材'),
      ('海鲜', '🐟', '海鲜类食材'),
      ('乳制品', '🥛', '牛奶、酸奶等乳制品'),
      ('调料', '🧂', '各种调味料')
    `);
    
    // 插入测试食材
    await connection.query(`
      INSERT IGNORE INTO ingredients (user_id, name, category_id, quantity, unit, expiry_date, storage_location, purchase_date, price, notes) VALUES
      (${userId}, '胡萝卜', 1, 5, '根', DATE_ADD(NOW(), INTERVAL 7 DAY), '冷藏室', NOW(), 8.50, '新鲜胡萝卜'),
      (${userId}, '苹果', 2, 3, '个', DATE_ADD(NOW(), INTERVAL 5 DAY), '冷藏室', NOW(), 12.00, '红富士苹果'),
      (${userId}, '鸡胸肉', 3, 500, '克', DATE_ADD(NOW(), INTERVAL 3 DAY), '冷冻室', NOW(), 25.00, '新鲜鸡胸肉'),
      (${userId}, '牛奶', 5, 1, '升', DATE_ADD(NOW(), INTERVAL 2 DAY), '冷藏室', NOW(), 15.50, '纯牛奶')
    `);
    
    // 插入测试菜谱
    await connection.query(`
      INSERT IGNORE INTO recipes (name, description, ingredients, instructions, cooking_time, difficulty, servings, image_url, tags) VALUES
      ('胡萝卜炒鸡丁', '营养丰富的家常菜', '["胡萝卜 2根", "鸡胸肉 200克", "盐 适量", "油 适量"]', '["鸡肉切丁腌制", "胡萝卜切丁", "热锅下油炒鸡丁", "加入胡萝卜丁炒制", "调味出锅"]', 20, 'easy', 2, '🍽️', '["家常菜", "营养"]'),
      ('苹果沙拉', '清爽的水果沙拉', '["苹果 2个", "酸奶 100ml", "蜂蜜 适量"]', '["苹果洗净切块", "加入酸奶", "淋上蜂蜜拌匀"]', 10, 'easy', 1, '🥗', '["水果", "健康"]')
    `);
    
    console.log('✅ 测试数据插入完成');
    
  } catch (error) {
    console.warn('⚠️  测试数据插入失败:', error.message);
    // 不影响主流程，继续执行
  }
}

// 检查数据库连接
async function checkConnection() {
  let connection;
  try {
    connection = await mysql.createConnection({
      ...dbConfig,
      database: process.env.DB_NAME || 'smart_fridge'
    });
    
    await connection.query('SELECT 1');
    console.log('✅ 数据库连接测试成功');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接测试失败:', error.message);
    return false;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 主函数
async function main() {
  console.log('🔧 数据库配置:');
  console.log(`   主机: ${dbConfig.host}:${dbConfig.port}`);
  console.log(`   用户: ${dbConfig.user}`);
  console.log(`   数据库: ${process.env.DB_NAME || 'smart_fridge'}`);
  console.log('');
  
  await initDatabase();
  
  // 验证初始化结果
  console.log('🔍 验证数据库初始化结果...');
  const isConnected = await checkConnection();
  
  if (isConnected) {
    console.log('🎉 数据库初始化验证成功！');
    console.log('📊 数据库已准备就绪，可以启动应用服务器');
  } else {
    console.error('❌ 数据库初始化验证失败');
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 初始化过程出错:', error);
    process.exit(1);
  });
}

module.exports = { initDatabase, checkConnection };