/* 菜谱添加/编辑页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.3s ease;
}

.action-btn:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.action-btn image {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

/* 表单滚动区域 */
.form-scroll {
  flex: 1;
  padding: 20rpx;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 表单区块 */
.form-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.section-count {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

.required {
  color: #ff4757;
  font-size: 28rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.item-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.item-input {
  width: 100%;
  height: 80rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.item-input:focus {
  border-color: #667eea;
  background-color: white;
}

.item-textarea {
  width: 100%;
  min-height: 120rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.item-textarea:focus {
  border-color: #667eea;
  background-color: white;
}

/* 图片上传 */
.image-upload {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-upload:active {
  border-color: #667eea;
  background-color: rgba(102, 126, 234, 0.05);
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.upload-icon {
  width: 48rpx;
  height: 48rpx;
  opacity: 0.5;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

/* 选择器行 */
.selector-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.selector-row:last-child {
  margin-bottom: 0;
}

.selector-item {
  flex: 1;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.selector-item:active {
  border-color: #667eea;
  background-color: rgba(102, 126, 234, 0.05);
}

.selector-label {
  font-size: 24rpx;
  color: #666;
}

.selector-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.selector-arrow {
  width: 20rpx;
  height: 20rpx;
  opacity: 0.5;
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
}

/* 食材列表 */
.ingredients-list {
  margin-bottom: 30rpx;
}

.ingredient-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
}

.ingredient-item:last-child {
  margin-bottom: 0;
}

.ingredient-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ingredient-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.ingredient-amount {
  font-size: 26rpx;
  color: #666;
}

.remove-btn {
  width: 48rpx;
  height: 48rpx;
  background-color: #ff4757;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-left: 20rpx;
  transition: all 0.3s ease;
}

.remove-btn:active {
  background-color: #ff3742;
  transform: scale(0.95);
}

.remove-btn image {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

/* 添加食材 */
.add-ingredient {
  border-top: 1rpx solid #e9ecef;
  padding-top: 30rpx;
}

.ingredient-inputs {
  display: flex;
  gap: 12rpx;
  align-items: center;
  margin-bottom: 20rpx;
}

.ingredient-name-input {
  flex: 2;
  height: 60rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 26rpx;
  color: #333;
  box-sizing: border-box;
}

.ingredient-amount-input {
  flex: 1;
  height: 60rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 26rpx;
  color: #333;
  box-sizing: border-box;
}

.ingredient-unit-input {
  flex: 1;
  height: 60rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 26rpx;
  color: #333;
  box-sizing: border-box;
}

.add-btn {
  background-color: #667eea;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 0 24rpx;
  height: 60rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-btn:active {
  background-color: #5a6fd8;
  transform: scale(0.98);
}

/* 可选食材 */
.available-ingredients {
  margin-top: 20rpx;
}

.available-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.available-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.available-item {
  background-color: #e9ecef;
  color: #495057;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.available-item:active {
  background-color: #667eea;
  color: white;
}

/* 制作步骤 */
.steps-list {
  margin-bottom: 30rpx;
}

.step-item {
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  background-color: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-description {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.step-actions {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex-shrink: 0;
}

.step-action-btn {
  width: 40rpx;
  height: 40rpx;
  background-color: #e9ecef;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.3s ease;
}

.step-action-btn:active {
  background-color: #dee2e6;
  transform: scale(0.95);
}

.step-action-btn.delete {
  background-color: #ff4757;
}

.step-action-btn.delete:active {
  background-color: #ff3742;
}

.step-action-btn image {
  width: 20rpx;
  height: 20rpx;
  opacity: 0.7;
}

.step-action-btn.delete image {
  filter: brightness(0) invert(1);
}

/* 添加步骤 */
.add-step {
  border-top: 1rpx solid #e9ecef;
  padding-top: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.step-input {
  width: 100%;
  min-height: 120rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.step-input:focus {
  border-color: #667eea;
  background-color: white;
}

.add-step-btn {
  background-color: #667eea;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-step-btn:active {
  background-color: #5a6fd8;
  transform: scale(0.98);
}

/* 营养信息网格 */
.nutrition-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.nutrition-item {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.nutrition-label {
  font-size: 24rpx;
  color: #666;
}

.nutrition-input {
  height: 60rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 26rpx;
  color: #333;
  box-sizing: border-box;
  text-align: center;
  transition: all 0.3s ease;
}

.nutrition-input:focus {
  border-color: #667eea;
  background-color: white;
}

/* 标签 */
.add-tag-btn {
  width: 40rpx;
  height: 40rpx;
  background-color: #667eea;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-left: auto;
  transition: all 0.3s ease;
}

.add-tag-btn:active {
  background-color: #5a6fd8;
  transform: scale(0.95);
}

.add-tag-btn image {
  width: 20rpx;
  height: 20rpx;
  filter: brightness(0) invert(1);
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.tag-item {
  background-color: #667eea;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
}

.tag-text {
  flex: 1;
}

.tag-remove {
  width: 32rpx;
  height: 32rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-size: 20rpx;
  color: white;
  transition: all 0.3s ease;
}

.tag-remove:active {
  background-color: rgba(255, 255, 255, 0.5);
}

.tag-input-container {
  display: flex;
  gap: 12rpx;
  align-items: center;
}

.tag-input {
  flex: 1;
  height: 60rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 26rpx;
  color: #333;
  box-sizing: border-box;
}

.tag-confirm-btn {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 0 20rpx;
  height: 60rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.tag-confirm-btn:active {
  background-color: #218838;
}

.tag-cancel-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 0 20rpx;
  height: 60rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.tag-cancel-btn:active {
  background-color: #5a6268;
}

/* 底部操作栏 */
.bottom-actions {
  background-color: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #e0e0e0;
  display: flex;
  gap: 20rpx;
}

.cancel-btn {
  flex: 1;
  background-color: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

.save-btn {
  flex: 2;
  background-color: #667eea;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.save-btn:active {
  background-color: #5a6fd8;
  transform: scale(0.98);
}

.save-btn[loading] {
  opacity: 0.7;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-container {
  background-color: white;
  border-radius: 16rpx;
  padding: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .page-header {
    padding: 15rpx 20rpx;
  }
  
  .header-title {
    font-size: 32rpx;
  }
  
  .form-scroll {
    padding: 15rpx;
  }
  
  .form-section {
    padding: 20rpx;
  }
  
  .section-title {
    font-size: 28rpx;
  }
  
  .selector-row {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .nutrition-grid {
    grid-template-columns: 1fr;
  }
  
  .ingredient-inputs {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .ingredient-name-input,
  .ingredient-amount-input,
  .ingredient-unit-input {
    flex: none;
  }
  
  .bottom-actions {
    padding: 15rpx 20rpx;
    gap: 15rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .form-section,
  .bottom-actions,
  .loading-container {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .section-title,
  .item-label,
  .ingredient-name,
  .step-description {
    color: white;
  }
  
  .ingredient-amount,
  .nutrition-label,
  .available-title,
  .upload-text {
    color: #999;
  }
  
  .item-input,
  .item-textarea,
  .nutrition-input,
  .ingredient-name-input,
  .ingredient-amount-input,
  .ingredient-unit-input,
  .step-input,
  .tag-input {
    background-color: #404040;
    border-color: #555;
    color: white;
  }
  
  .item-input:focus,
  .item-textarea:focus,
  .nutrition-input:focus,
  .step-input:focus {
    background-color: #555;
    border-color: #667eea;
  }
  
  .selector-item,
  .ingredient-item,
  .step-item {
    background-color: #404040;
    border-color: #555;
  }
  
  .selector-value {
    color: white;
  }
  
  .selector-label {
    color: #999;
  }
  
  .available-item {
    background-color: #555;
    color: #ccc;
  }
  
  .available-item:active {
    background-color: #667eea;
    color: white;
  }
  
  .cancel-btn {
    background-color: #404040;
    color: #ccc;
    border-color: #555;
  }
  
  .cancel-btn:active {
    background-color: #555;
  }
  
  .loading-text {
    color: #ccc;
  }
}

/* 动画效果 */
.form-section {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ingredient-item,
.step-item {
  animation: fadeInScale 0.3s ease;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.tag-item {
  animation: bounceIn 0.3s ease;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}