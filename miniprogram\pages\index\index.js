// 首页逻辑
const app = getApp();
const { DEFAULT_IMAGES, handleImageError } = require('../../utils/image');

Page({
  data: {
    userInfo: null,
    isLogin: false,
    defaultAvatar: DEFAULT_IMAGES['default-avatar'],
    dashboardData: {
      ingredients: {
        total: 0,
        expiring_soon: 0,
        expired: 0
      },
      recipes: {
        total: 0,
        public: 0
      },
      shopping: {
        total_items: 0,
        pending_items: 0,
        total_spent: 0
      },
      health_score: 100
    },
    recentActivity: [],
    quickActions: [
      {
        id: 'add_ingredient',
        name: '添加食材',
        iconEmoji: '🥬',
        url: '/pages/ingredients/add/add'
      },
      {
        id: 'add_recipe',
        name: '添加菜谱',
        iconEmoji: '📖',
        url: '/pages/recipes/add/add'
      },
      {
        id: 'shopping_list',
        name: '购物清单',
        iconEmoji: '🛒',
        url: '/pages/shopping/list/list'
      },
      {
        id: 'analysis',
        name: '数据分析',
        iconEmoji: '📊',
        url: '/pages/analysis/dashboard/dashboard'
      }
    ],
    loading: false,
    refreshing: false
  },

  onLoad() {
    console.log('首页加载');
    this.checkLoginStatus();
  },

  onShow() {
    console.log('首页显示');
    // 重新检查登录状态
    this.checkLoginStatus();
  },

  onPullDownRefresh() {
    console.log('下拉刷新');
    this.setData({ refreshing: true });
    this.loadDashboardData().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    console.log('触底加载');
    // 可以在这里加载更多活动记录
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = app.globalData.userInfo;
    const token = app.globalData.token;

    if (userInfo && token) {
      this.setData({
        userInfo,
        isLogin: true
      });
      this.loadDashboardData();
    } else {
      this.setData({
        userInfo: null,
        isLogin: false
      });
    }
  },

  // 加载仪表板数据
  async loadDashboardData() {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    try {
      const res = await app.request({
        url: '/analysis/dashboard'
      });
      
      this.setData({
        dashboardData: res.data.summary,
        recentActivity: res.data.recent_activity.slice(0, 5) // 只显示最近5条
      });
    } catch (error) {
      console.error('加载仪表板数据失败:', error);
      app.showError('加载数据失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 跳转到登录页
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  // 快捷操作点击
  onQuickActionTap(e) {
    const { url } = e.currentTarget.dataset;
    
    if (!this.data.isLogin) {
      app.showError('请先登录');
      this.goToLogin();
      return;
    }
    
    wx.navigateTo({
      url
    });
  },

  // 查看更多活动记录
  viewMoreActivity() {
    wx.navigateTo({
      url: '/pages/records/list/list'
    });
  },

  // 查看食材详情
  viewIngredients() {
    wx.switchTab({
      url: '/pages/ingredients/list/list'
    });
  },

  // 查看菜谱详情
  viewRecipes() {
    wx.switchTab({
      url: '/pages/recipes/list/list'
    });
  },

  // 查看购物详情
  viewShopping() {
    wx.switchTab({
      url: '/pages/shopping/list/list'
    });
  },

  // 查看分析详情
  viewAnalysis() {
    wx.navigateTo({
      url: '/pages/analysis/dashboard/dashboard'
    });
  },

  // 获取健康度颜色
  getHealthScoreColor(score) {
    if (score >= 80) return '#2ed573';
    if (score >= 60) return '#ffa502';
    return '#ff4757';
  },

  // 获取健康度文本
  getHealthScoreText(score) {
    if (score >= 90) return '优秀';
    if (score >= 80) return '良好';
    if (score >= 60) return '一般';
    return '需改善';
  },

  // 格式化活动描述
  formatActivityDescription(activity) {
    const timeAgo = this.getTimeAgo(activity.created_at);
    return `${timeAgo} ${activity.description}`;
  },

  // 获取相对时间
  getTimeAgo(dateString) {
    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;
    return app.formatDate(date, 'MM-DD');
  },

  // 获取活动图标
  getActivityIcon(type) {
    const iconMap = {
      'ingredient': '🥬',
      'recipe': '📖',
      'shopping': '🛒',
      'analysis': '📊',
      'favorite': '❤️',
      'cook': '👨‍🍳',
      'default': '📝'
    };
    return iconMap[type] || iconMap.default;
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '智能冰箱食材管理助手',
      desc: '让食材管理更智能，让生活更便捷',
      path: '/pages/index/index'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '智能冰箱食材管理助手 - 让食材管理更智能',
      query: '',
      imageUrl: '/images/share-cover.jpg'
    };
  },

  // 图片加载错误处理
  handleImageError(e) {
    handleImageError(e, 'avatar');
  }
});