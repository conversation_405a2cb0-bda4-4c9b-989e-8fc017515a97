<!--数据分析页面-->
<view class="page-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">数据分析</text>
    <view class="header-actions">
      <view class="time-selector">
        <picker 
          mode="selector" 
          range="{{timeRanges}}" 
          range-key="label"
          value="{{timeRange}}"
          bindchange="switchTimeRange"
        >
          <view class="time-picker">
            <text class="time-text">{{timeRanges[timeRange].label}}</text>
            <image src="/images/icons/arrow-down.png" class="arrow-icon"></image>
          </view>
        </picker>
      </view>
      
      <view class="action-buttons">
        <button class="action-btn export-btn" bindtap="exportReport">
          <image src="/images/icons/export.png" mode="aspectFit"></image>
        </button>
        <button class="action-btn share-btn" bindtap="shareReport">
          <image src="📤" mode="aspectFit"></image>
        </button>
      </view>
    </view>
  </view>

  <!-- 标签页导航 -->
  <view class="tab-nav">
    <view 
      class="tab-item {{currentTab === index ? 'active' : ''}}"
      wx:for="{{tabs}}"
      wx:key="index"
      data-index="{{index}}"
      bindtap="switchTab"
    >
      <text class="tab-text">{{item}}</text>
    </view>
  </view>

  <!-- 内容区域 -->
  <scroll-view 
    class="content-scroll"
    scroll-y
    refresher-enabled
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onPullDownRefresh"
  >
    <!-- 健康分析 -->
    <view class="tab-content" wx:if="{{currentTab === 0}}">
      <!-- 健康评分卡片 -->
      <view class="health-score-card">
        <view class="score-circle">
          <view class="score-inner">
            <text class="score-number">{{healthData.score}}</text>
            <text class="score-label">健康评分</text>
          </view>
          <view class="score-ring" style="--score: {{healthData.score}}%"></view>
        </view>
        
        <view class="score-info">
          <view class="health-level {{healthData.level}}">
            <text class="level-text">{{healthData.level}}</text>
          </view>
          <button class="suggestions-btn" bindtap="viewHealthSuggestions">
            查看建议 ({{healthData.suggestions.length}})
          </button>
        </view>
      </view>

      <!-- 营养分析 -->
      <view class="analysis-card">
        <view class="card-header">
          <text class="card-title">营养分析</text>
          <button class="detail-btn" bindtap="viewNutritionDetail">详情</button>
        </view>
        
        <view class="nutrition-chart">
          <canvas 
            canvas-id="nutritionChart" 
            class="chart-canvas"
            disable-scroll="true"
          ></canvas>
        </view>
        
        <view class="nutrition-stats">
          <view class="nutrition-item">
            <view class="nutrition-color protein-color"></view>
            <text class="nutrition-label">蛋白质</text>
            <text class="nutrition-value">{{healthData.nutritionStats.protein}}%</text>
          </view>
          <view class="nutrition-item">
            <view class="nutrition-color carbs-color"></view>
            <text class="nutrition-label">碳水化合物</text>
            <text class="nutrition-value">{{healthData.nutritionStats.carbs}}%</text>
          </view>
          <view class="nutrition-item">
            <view class="nutrition-color fat-color"></view>
            <text class="nutrition-label">脂肪</text>
            <text class="nutrition-value">{{healthData.nutritionStats.fat}}%</text>
          </view>
          <view class="nutrition-item">
            <view class="nutrition-color vitamins-color"></view>
            <text class="nutrition-label">维生素</text>
            <text class="nutrition-value">{{healthData.nutritionStats.vitamins}}%</text>
          </view>
          <view class="nutrition-item">
            <view class="nutrition-color minerals-color"></view>
            <text class="nutrition-label">矿物质</text>
            <text class="nutrition-value">{{healthData.nutritionStats.minerals}}%</text>
          </view>
        </view>
      </view>

      <!-- 过期统计 -->
      <view class="analysis-card">
        <view class="card-header">
          <text class="card-title">食材状态</text>
        </view>
        
        <view class="expiry-stats">
          <view class="expiry-item fresh">
            <view class="expiry-icon">
              <image src="/images/icons/fresh.png" mode="aspectFit"></image>
            </view>
            <view class="expiry-info">
              <text class="expiry-number">{{healthData.expiryStats.fresh}}</text>
              <text class="expiry-label">新鲜</text>
            </view>
          </view>
          
          <view class="expiry-item warning">
            <view class="expiry-icon">
              <image src="⚠️" mode="aspectFit"></image>
            </view>
            <view class="expiry-info">
              <text class="expiry-number">{{healthData.expiryStats.expiringSoon}}</text>
              <text class="expiry-label">即将过期</text>
            </view>
          </view>
          
          <view class="expiry-item expired">
            <view class="expiry-icon">
              <image src="/images/icons/expired.png" mode="aspectFit"></image>
            </view>
            <view class="expiry-info">
              <text class="expiry-number">{{healthData.expiryStats.expired}}</text>
              <text class="expiry-label">已过期</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 消费统计 -->
    <view class="tab-content" wx:if="{{currentTab === 1}}">
      <!-- 消费概览 -->
      <view class="expense-overview">
        <view class="overview-item">
          <text class="overview-label">总消费</text>
          <text class="overview-value primary">¥{{expenseData.totalExpense}}</text>
        </view>
        <view class="overview-item">
          <text class="overview-label">月均消费</text>
          <text class="overview-value">¥{{expenseData.monthlyExpense}}</text>
        </view>
        <view class="overview-item">
          <text class="overview-label">日均消费</text>
          <text class="overview-value">¥{{expenseData.avgDailyExpense}}</text>
        </view>
      </view>

      <!-- 消费趋势图 -->
      <view class="analysis-card">
        <view class="card-header">
          <text class="card-title">消费趋势</text>
          <button class="detail-btn" bindtap="viewExpenseDetail">详情</button>
        </view>
        
        <view class="expense-chart">
          <canvas 
            canvas-id="expenseChart" 
            class="chart-canvas"
            disable-scroll="true"
          ></canvas>
        </view>
      </view>

      <!-- 分类消费 -->
      <view class="analysis-card">
        <view class="card-header">
          <text class="card-title">分类消费</text>
        </view>
        
        <view class="category-expenses">
          <view 
            class="category-item"
            wx:for="{{expenseData.categoryExpenses}}"
            wx:key="category"
          >
            <view class="category-info">
              <view class="category-icon" style="background-color: {{item.color}}">
                <image src="{{item.icon}}" mode="aspectFit"></image>
              </view>
              <text class="category-name">{{item.category}}</text>
            </view>
            
            <view class="category-amount">
              <text class="amount-value">¥{{item.amount}}</text>
              <text class="amount-percent">{{item.percent}}%</text>
            </view>
            
            <view class="category-bar">
              <view 
                class="bar-fill" 
                style="width: {{item.percent}}%; background-color: {{item.color}}"
              ></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 热门分类 -->
      <view class="analysis-card">
        <view class="card-header">
          <text class="card-title">热门分类</text>
        </view>
        
        <view class="top-categories">
          <view 
            class="top-item"
            wx:for="{{expenseData.topCategories}}"
            wx:key="index"
          >
            <view class="rank-number">{{index + 1}}</view>
            <view class="category-icon" style="background-color: {{item.color}}">
              <image src="{{item.icon}}" mode="aspectFit"></image>
            </view>
            <view class="category-info">
              <text class="category-name">{{item.name}}</text>
              <text class="category-count">{{item.count}}次购买</text>
            </view>
            <text class="category-amount">¥{{item.amount}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用趋势 -->
    <view class="tab-content" wx:if="{{currentTab === 2}}">
      <!-- 使用概览 -->
      <view class="usage-overview">
        <view class="overview-stats">
          <view class="stat-item">
            <text class="stat-number">{{usageData.totalIngredients}}</text>
            <text class="stat-label">总食材</text>
          </view>
          <view class="stat-item">
            <text class="stat-number used">{{usageData.usedIngredients}}</text>
            <text class="stat-label">已使用</text>
          </view>
          <view class="stat-item">
            <text class="stat-number wasted">{{usageData.wastedIngredients}}</text>
            <text class="stat-label">已浪费</text>
          </view>
        </view>
        
        <view class="usage-rates">
          <view class="rate-item">
            <text class="rate-label">使用率</text>
            <text class="rate-value good">{{usageData.usageRate}}%</text>
          </view>
          <view class="rate-item">
            <text class="rate-label">浪费率</text>
            <text class="rate-value bad">{{usageData.wasteRate}}%</text>
          </view>
        </view>
      </view>

      <!-- 使用率饼图 -->
      <view class="analysis-card">
        <view class="card-header">
          <text class="card-title">使用分布</text>
          <button class="detail-btn" bindtap="viewUsageDetail">详情</button>
        </view>
        
        <view class="usage-chart">
          <canvas 
            canvas-id="usageChart" 
            class="chart-canvas"
            disable-scroll="true"
          ></canvas>
        </view>
        
        <view class="usage-legend">
          <view class="legend-item">
            <view class="legend-color used-color"></view>
            <text class="legend-label">已使用</text>
            <text class="legend-value">{{usageData.usedIngredients}}</text>
          </view>
          <view class="legend-item">
            <view class="legend-color wasted-color"></view>
            <text class="legend-label">已浪费</text>
            <text class="legend-value">{{usageData.wastedIngredients}}</text>
          </view>
          <view class="legend-item">
            <view class="legend-color remaining-color"></view>
            <text class="legend-label">剩余</text>
            <text class="legend-value">{{usageData.totalIngredients - usageData.usedIngredients - usageData.wastedIngredients}}</text>
          </view>
        </view>
      </view>

      <!-- 月度使用趋势 -->
      <view class="analysis-card">
        <view class="card-header">
          <text class="card-title">月度使用趋势</text>
        </view>
        
        <view class="monthly-usage">
          <view 
            class="month-item"
            wx:for="{{usageData.monthlyUsage}}"
            wx:key="month"
          >
            <text class="month-label">{{item.month}}</text>
            <view class="usage-bars">
              <view class="usage-bar">
                <view 
                  class="bar-segment used" 
                  style="width: {{item.usedPercent}}%"
                ></view>
                <view 
                  class="bar-segment wasted" 
                  style="width: {{item.wastedPercent}}%"
                ></view>
              </view>
            </view>
            <text class="usage-total">{{item.total}}</text>
          </view>
        </view>
      </view>

      <!-- 分类使用情况 -->
      <view class="analysis-card">
        <view class="card-header">
          <text class="card-title">分类使用情况</text>
        </view>
        
        <view class="category-usage">
          <view 
            class="usage-category"
            wx:for="{{usageData.categoryUsage}}"
            wx:key="category"
          >
            <view class="category-header">
              <view class="category-icon" style="background-color: {{item.color}}">
                <image src="{{item.icon}}" mode="aspectFit"></image>
              </view>
              <text class="category-name">{{item.category}}</text>
              <text class="category-rate">使用率 {{item.usageRate}}%</text>
            </view>
            
            <view class="category-progress">
              <view class="progress-bar">
                <view 
                  class="progress-fill" 
                  style="width: {{item.usageRate}}%; background-color: {{item.color}}"
                ></view>
              </view>
            </view>
            
            <view class="category-stats">
              <text class="stat-text">总计: {{item.total}}</text>
              <text class="stat-text">使用: {{item.used}}</text>
              <text class="stat-text">浪费: {{item.wasted}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="action-button reminder-btn" bindtap="setReminder">
      <image src="🔔" mode="aspectFit"></image>
      <text>设置提醒</text>
    </button>
    
    <button class="action-button history-btn" bindtap="viewHistory">
      <image src="📜" mode="aspectFit"></image>
      <text>历史记录</text>
    </button>
  </view>

  <!-- 分享画布 -->
  <canvas 
    canvas-id="shareCanvas" 
    class="share-canvas"
    style="width: 750rpx; height: 1000rpx;"
  ></canvas>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">分析中...</text>
    </view>
  </view>
</view>