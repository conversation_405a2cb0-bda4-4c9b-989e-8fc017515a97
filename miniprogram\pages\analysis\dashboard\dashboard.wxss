/* 数据分析仪表板页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx 30rpx;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.action-icon:active {
  opacity: 0.6;
}

/* 时间范围选择 */
.time-range-tabs {
  display: flex;
  gap: 20rpx;
}

.time-tab {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.time-tab.active {
  background-color: white;
  color: #667eea;
  font-weight: 600;
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
}

.dashboard-content {
  padding: 30rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.stat-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.stat-icon {
  width: 40rpx;
  height: 40rpx;
}

.stat-title {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.stat-footer {
  margin-top: auto;
}

.stat-warning {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #dc3545;
  font-size: 22rpx;
  background-color: rgba(220, 53, 69, 0.1);
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
}

.warning-icon {
  width: 20rpx;
  height: 20rpx;
}

.stat-normal {
  color: #28a745;
  font-size: 22rpx;
  text-align: center;
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #667eea;
  font-size: 22rpx;
  background-color: rgba(102, 126, 234, 0.1);
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
}

.info-icon {
  width: 20rpx;
  height: 20rpx;
}

.stat-progress {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  font-size: 22rpx;
  color: #666;
}

.progress-bar {
  height: 6rpx;
  background-color: #e9ecef;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #28a745;
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

/* 健康评分卡片 */
.health-card .stat-content {
  gap: 12rpx;
}

.health-score {
  font-size: 56rpx;
  font-weight: 700;
  line-height: 1;
}

.health-level {
  font-size: 26rpx;
  font-weight: 600;
}

.health-progress {
  margin-top: 15rpx;
}

.health-bar {
  height: 8rpx;
  background-color: #e9ecef;
  border-radius: 4rpx;
  overflow: hidden;
}

.health-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 支出统计 */
.spending-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.spending-amount {
  font-size: 36rpx;
  font-weight: 700;
  color: #667eea;
}

.spending-chart {
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  opacity: 0.5;
}

.chart-icon {
  width: 80rpx;
  height: 80rpx;
}

.chart-text {
  font-size: 26rpx;
  color: #999;
}

/* 快捷操作 */
.quick-actions-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
  margin-top: 20rpx;
}

.quick-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.quick-action-item:active {
  background-color: #f8f9fa;
  transform: scale(0.95);
}

.quick-action-item .action-icon {
  width: 50rpx;
  height: 50rpx;
}

.action-name {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  font-weight: 500;
}

/* 数据洞察 */
.insights-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 20rpx;
}

.insight-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #f8f9fa;
}

.insight-icon {
  width: 40rpx;
  height: 40rpx;
  flex-shrink: 0;
}

.insight-icon.warning {
  filter: hue-rotate(30deg);
}

.insight-icon.danger {
  filter: hue-rotate(0deg);
}

.insight-icon.info {
  filter: hue-rotate(200deg);
}

.insight-icon.success {
  filter: hue-rotate(120deg);
}

.insight-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.insight-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.insight-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15rpx;
  }
  
  .stat-card {
    padding: 25rpx;
  }
  
  .stat-number {
    font-size: 42rpx;
  }
  
  .health-score {
    font-size: 48rpx;
  }
  
  .spending-amount {
    font-size: 32rpx;
  }
  
  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
  }
  
  .dashboard-content {
    padding: 20rpx;
  }
  
  .page-header {
    padding: 15rpx 20rpx 25rpx;
  }
  
  .header-title {
    font-size: 32rpx;
  }
  
  .time-range-tabs {
    gap: 15rpx;
  }
  
  .time-tab {
    padding: 10rpx 20rpx;
    font-size: 24rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .stat-card,
  .spending-section,
  .quick-actions-section,
  .insights-section {
    background-color: #2d2d2d;
  }
  
  .stat-number,
  .section-title,
  .insight-title {
    color: white;
  }
  
  .stat-title,
  .stat-label,
  .chart-text,
  .action-name,
  .insight-desc {
    color: #ccc;
  }
  
  .insight-item {
    background-color: #404040;
  }
  
  .quick-action-item:active {
    background-color: #404040;
  }
  
  .progress-bar,
  .health-bar {
    background-color: #404040;
  }
}

/* 动画效果 */
.stat-card {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.time-tab {
  animation: fadeInScale 0.3s ease;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.quick-action-item {
  animation: bounceIn 0.4s ease;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.insight-item {
  animation: slideInLeft 0.3s ease;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 进度条动画 */
.progress-fill,
.health-fill {
  animation: progressFill 1s ease-out;
}

@keyframes progressFill {
  from {
    width: 0;
  }
}

/* 数字计数动画 */
.stat-number,
.health-score,
.spending-amount {
  animation: countUp 0.8s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}