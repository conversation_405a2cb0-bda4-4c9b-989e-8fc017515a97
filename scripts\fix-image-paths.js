// 修复小程序中的本地图片路径问题
const fs = require('fs');
const path = require('path');

// 图片路径替换映射
const IMAGE_REPLACEMENTS = {
  // 图标替换为emoji
  '/images/icons/more.png': '⋯',
  '/images/icons/calendar.png': '📅',
  '/images/icons/shopping.png': '🛒',
  '/images/icons/note.png': '📝',
  '/images/icons/time.png': '⏰',
  '/images/icons/cart-add.png': '🛒',
  '/images/icons/bell.png': '🔔',
  '/images/icons/copy.png': '📋',
  '/images/icons/history.png': '📜',
  '/images/icons/check.png': '✅',
  '/images/icons/warning.png': '⚠️',
  '/images/icons/heart.png': '🤍',
  '/images/icons/heart-filled.png': '❤️',
  '/images/icons/share.png': '📤',
  '/images/icons/edit.png': '✏️',
  '/images/icons/ingredient.png': '🥬',
  '/images/icons/trend-up.png': '📈',
  '/images/icons/trend-down.png': '📉',
  '/images/icons/trend-flat.png': '➡️',
  '/images/icons/health-warning.png': '⚠️',
  '/images/icons/empty-list.png': '📋',
  
  // 图片替换为网络URL
  '/images/default-ingredient.png': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop&crop=center',
  '/images/tab/default-avatar.png': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
  '/images/share-cover.jpg': 'https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=400&h=300&fit=crop&crop=center',
  '/images/share-app.png': 'https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=400&h=300&fit=crop&crop=center'
};

// 需要处理的文件扩展名
const FILE_EXTENSIONS = ['.wxml', '.js', '.wxss'];

// 需要处理的目录
const DIRECTORIES_TO_PROCESS = [
  'miniprogram/pages',
  'miniprogram/components'
];

// 递归获取所有需要处理的文件
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      getAllFiles(filePath, fileList);
    } else {
      const ext = path.extname(file);
      if (FILE_EXTENSIONS.includes(ext)) {
        fileList.push(filePath);
      }
    }
  });
  
  return fileList;
}

// 处理单个文件
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 替换图片路径
    Object.entries(IMAGE_REPLACEMENTS).forEach(([oldPath, newPath]) => {
      const regex = new RegExp(oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      if (content.includes(oldPath)) {
        content = content.replace(regex, newPath);
        modified = true;
        console.log(`  替换: ${oldPath} -> ${newPath}`);
      }
    });
    
    // 如果文件被修改，写回文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`处理文件失败: ${filePath}`, error.message);
    return false;
  }
}

// 主处理函数
function fixImagePaths() {
  console.log('🔧 开始修复小程序图片路径问题...\n');
  
  let totalFiles = 0;
  let modifiedFiles = 0;
  
  DIRECTORIES_TO_PROCESS.forEach(dir => {
    const fullDir = path.resolve(dir);
    
    if (!fs.existsSync(fullDir)) {
      console.log(`⚠️  目录不存在: ${dir}`);
      return;
    }
    
    console.log(`📁 处理目录: ${dir}`);
    const files = getAllFiles(fullDir);
    
    files.forEach(filePath => {
      totalFiles++;
      const relativePath = path.relative(process.cwd(), filePath);
      console.log(`  处理文件: ${relativePath}`);
      
      if (processFile(filePath)) {
        modifiedFiles++;
      }
    });
  });
  
  console.log(`\n📊 处理结果:`);
  console.log(`  总文件数: ${totalFiles}`);
  console.log(`  修改文件数: ${modifiedFiles}`);
  console.log(`  未修改文件数: ${totalFiles - modifiedFiles}`);
  
  if (modifiedFiles > 0) {
    console.log('\n✅ 图片路径修复完成！');
    console.log('\n💡 建议:');
    console.log('1. 重新编译小程序项目');
    console.log('2. 测试所有页面的图片显示');
    console.log('3. 检查控制台是否还有图片加载错误');
  } else {
    console.log('\n✅ 没有发现需要修复的图片路径');
  }
}

// 显示替换映射
function showReplacements() {
  console.log('📋 图片路径替换映射:\n');
  
  console.log('🎨 图标替换 (本地图标 -> Emoji):');
  Object.entries(IMAGE_REPLACEMENTS).forEach(([oldPath, newPath]) => {
    if (oldPath.includes('/icons/')) {
      console.log(`  ${oldPath} -> ${newPath}`);
    }
  });
  
  console.log('\n🖼️  图片替换 (本地图片 -> 网络图片):');
  Object.entries(IMAGE_REPLACEMENTS).forEach(([oldPath, newPath]) => {
    if (!oldPath.includes('/icons/')) {
      console.log(`  ${oldPath}`);
      console.log(`    -> ${newPath}`);
    }
  });
}

// 验证替换结果
function verifyReplacements() {
  console.log('\n🔍 验证替换结果...\n');
  
  let foundIssues = 0;
  
  DIRECTORIES_TO_PROCESS.forEach(dir => {
    const fullDir = path.resolve(dir);
    if (!fs.existsSync(fullDir)) return;
    
    const files = getAllFiles(fullDir);
    
    files.forEach(filePath => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const relativePath = path.relative(process.cwd(), filePath);
        
        // 检查是否还有本地图片路径
        Object.keys(IMAGE_REPLACEMENTS).forEach(oldPath => {
          if (content.includes(oldPath)) {
            console.log(`❌ 发现未替换的路径: ${relativePath} -> ${oldPath}`);
            foundIssues++;
          }
        });
      } catch (error) {
        console.error(`验证文件失败: ${filePath}`, error.message);
      }
    });
  });
  
  if (foundIssues === 0) {
    console.log('✅ 验证通过，所有本地图片路径已成功替换');
  } else {
    console.log(`⚠️  发现 ${foundIssues} 个未替换的路径，请检查`);
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
const command = args[0];

switch (command) {
  case 'show':
    showReplacements();
    break;
  case 'verify':
    verifyReplacements();
    break;
  case 'fix':
  default:
    fixImagePaths();
    break;
}

module.exports = {
  IMAGE_REPLACEMENTS,
  fixImagePaths,
  showReplacements,
  verifyReplacements
};
