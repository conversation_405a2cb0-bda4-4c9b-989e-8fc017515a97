<!--食材分析页面-->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <view class="header-title">食材分析</view>
      <view class="header-actions">
        <image class="action-icon" src="/images/icons/refresh.png" bindtap="refreshData" />
      </view>
    </view>
    
    <!-- 时间范围选择 -->
    <view class="time-range-tabs">
      <view 
        wx:for="{{timeRanges}}" 
        wx:key="id"
        class="time-tab {{timeRange === item.id ? 'active' : ''}}"
        data-range="{{item.id}}"
        bindtap="switchTimeRange"
      >
        {{item.name}}
      </view>
    </view>
  </view>

  <!-- 内容滚动区域 -->
  <scroll-view class="content-scroll" scroll-y enhanced show-scrollbar="{{false}}" refresher-enabled bindrefresherrefresh="refreshData" refresher-triggered="{{refreshing}}">
    
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 主要内容 -->
    <view wx:else class="analysis-content">
      
      <!-- 总体统计 -->
      <view class="overview-section">
        <view class="section-header">
          <text class="section-title">总体概况</text>
        </view>
        <view class="overview-card">
          <view class="overview-item">
            <view class="overview-number">{{stats.totalCount}}</view>
            <view class="overview-label">总食材数</view>
          </view>
          <view class="overview-divider"></view>
          <view class="overview-item">
            <view class="overview-number" style="color: {{getStatusColor('fresh')}}">{{stats.statusStats.fresh}}</view>
            <view class="overview-label">新鲜食材</view>
          </view>
          <view class="overview-divider"></view>
          <view class="overview-item">
            <view class="overview-number" style="color: {{getStatusColor('expiring')}}">{{stats.statusStats.expiring}}</view>
            <view class="overview-label">即将过期</view>
          </view>
          <view class="overview-divider"></view>
          <view class="overview-item">
            <view class="overview-number" style="color: {{getStatusColor('expired')}}">{{stats.statusStats.expired}}</view>
            <view class="overview-label">已过期</view>
          </view>
        </view>
      </view>

      <!-- 图表类型切换 -->
      <view class="chart-tabs">
        <view 
          wx:for="{{chartTypes}}" 
          wx:key="id"
          class="chart-tab {{chartType === item.id ? 'active' : ''}}"
          data-type="{{item.id}}"
          bindtap="switchChartType"
        >
          {{item.name}}
        </view>
      </view>

      <!-- 分类统计图表 -->
      <view wx:if="{{chartType === 'category'}}" class="chart-section">
        <view class="section-header">
          <text class="section-title">分类统计</text>
        </view>
        <view class="chart-container">
          <view wx:for="{{stats.categoryStats}}" wx:key="category" class="category-item" data-category="{{item.category}}" bindtap="viewCategoryDetail">
            <view class="category-info">
              <image class="category-icon" src="/images/categories/{{item.category}}.png" />
              <view class="category-details">
                <text class="category-name">{{item.name}}</text>
                <text class="category-count">{{item.count}}个</text>
              </view>
            </view>
            <view class="category-progress">
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{calculatePercentage(item.count, stats.totalCount)}}%"></view>
              </view>
              <text class="progress-text">{{calculatePercentage(item.count, stats.totalCount)}}%</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 状态分布图表 -->
      <view wx:if="{{chartType === 'status'}}" class="chart-section">
        <view class="section-header">
          <text class="section-title">状态分布</text>
        </view>
        <view class="chart-container">
          <view class="status-chart">
            <view class="status-item" bindtap="viewExpiringIngredients">
              <view class="status-circle" style="background-color: {{getStatusColor('fresh')}}">
                <text class="status-number">{{stats.statusStats.fresh}}</text>
              </view>
              <text class="status-label">{{getStatusName('fresh')}}</text>
            </view>
            <view class="status-item" bindtap="viewExpiringIngredients">
              <view class="status-circle" style="background-color: {{getStatusColor('expiring')}}">
                <text class="status-number">{{stats.statusStats.expiring}}</text>
              </view>
              <text class="status-label">{{getStatusName('expiring')}}</text>
            </view>
            <view class="status-item" bindtap="viewExpiredIngredients">
              <view class="status-circle" style="background-color: {{getStatusColor('expired')}}">
                <text class="status-number">{{stats.statusStats.expired}}</text>
              </view>
              <text class="status-label">{{getStatusName('expired')}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 存储位置统计 -->
      <view wx:if="{{chartType === 'storage'}}" class="chart-section">
        <view class="section-header">
          <text class="section-title">存储位置</text>
        </view>
        <view class="chart-container">
          <view wx:for="{{stats.storageStats}}" wx:key="storage" class="storage-item" data-storage="{{item.storage}}" bindtap="viewStorageDetail">
            <view class="storage-info">
              <image class="storage-icon" src="/images/storage/{{item.storage}}.png" />
              <view class="storage-details">
                <text class="storage-name">{{item.name}}</text>
                <text class="storage-count">{{item.count}}个食材</text>
              </view>
            </view>
            <view class="storage-progress">
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{calculatePercentage(item.count, stats.totalCount)}}%"></view>
              </view>
              <text class="progress-text">{{calculatePercentage(item.count, stats.totalCount)}}%</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 趋势分析 -->
      <view wx:if="{{chartType === 'trend'}}" class="chart-section">
        <view class="section-header">
          <text class="section-title">趋势分析</text>
        </view>
        <view class="chart-container">
          <view class="trend-chart">
            <view wx:for="{{stats.monthlyTrend}}" wx:key="month" class="trend-item">
              <view class="trend-bar">
                <view class="trend-fill" style="height: {{item.percentage}}%"></view>
              </view>
              <text class="trend-label">{{item.month}}</text>
              <text class="trend-value">{{item.count}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 详细建议 -->
      <view class="suggestions-section">
        <view class="section-header">
          <text class="section-title">管理建议</text>
        </view>
        <view class="suggestions-list">
          <view wx:if="{{stats.statusStats.expiring > 0}}" class="suggestion-item warning">
            <text class="suggestion-icon emoji-icon">⚠️</text>
            <view class="suggestion-content">
              <text class="suggestion-title">过期提醒</text>
              <text class="suggestion-desc">有 {{stats.statusStats.expiring}} 个食材即将过期，建议优先使用</text>
            </view>
            <view class="suggestion-action" bindtap="viewExpiringIngredients">
              <text>查看</text>
            </view>
          </view>
          
          <view wx:if="{{stats.statusStats.expired > 0}}" class="suggestion-item danger">
            <image class="suggestion-icon" src="/images/icons/expired.png" />
            <view class="suggestion-content">
              <text class="suggestion-title">清理提醒</text>
              <text class="suggestion-desc">有 {{stats.statusStats.expired}} 个食材已过期，建议及时清理</text>
            </view>
            <view class="suggestion-action" bindtap="viewExpiredIngredients">
              <text>清理</text>
            </view>
          </view>
          
          <view class="suggestion-item info">
            <image class="suggestion-icon" src="/images/icons/tip.png" />
            <view class="suggestion-content">
              <text class="suggestion-title">存储建议</text>
              <text class="suggestion-desc">合理分配食材存储位置，延长保鲜期</text>
            </view>
          </view>
        </view>
      </view>

    </view>
  </scroll-view>
</view>