// 隐私政策页面逻辑
const app = getApp();

Page({
  data: {
    lastUpdated: '2024-01-15',
    sections: [
      {
        id: 'overview',
        title: '隐私政策概述',
        content: '本隐私政策说明了智能冰箱食材管理助手（以下简称"我们"或"本应用"）如何收集、使用、存储和保护您的个人信息。我们承诺保护您的隐私权，并遵守相关法律法规。'
      },
      {
        id: 'collection',
        title: '信息收集',
        content: '我们可能收集以下类型的信息：\n\n1. 账户信息：用户名、手机号、邮箱等注册信息\n2. 食材信息：您添加的食材名称、数量、保质期等\n3. 使用数据：应用使用情况、功能偏好等\n4. 设备信息：设备型号、操作系统版本、唯一设备标识符\n5. 位置信息：用于推荐附近商店（需要您的明确授权）'
      },
      {
        id: 'usage',
        title: '信息使用',
        content: '我们使用收集的信息用于：\n\n1. 提供和改进服务功能\n2. 个性化推荐和内容定制\n3. 发送重要通知和提醒\n4. 分析使用趋势，优化用户体验\n5. 防止欺诈和滥用行为\n6. 遵守法律法规要求'
      },
      {
        id: 'sharing',
        title: '信息共享',
        content: '我们不会出售、交易或转让您的个人信息给第三方，除非：\n\n1. 获得您的明确同意\n2. 为提供服务所必需（如支付处理）\n3. 遵守法律法规要求\n4. 保护我们的权利和安全\n5. 经过匿名化处理的统计数据'
      },
      {
        id: 'storage',
        title: '信息存储',
        content: '我们采取以下措施保护您的信息：\n\n1. 数据加密：传输和存储过程中使用加密技术\n2. 访问控制：严格限制员工访问权限\n3. 安全审计：定期进行安全评估和审计\n4. 数据备份：建立可靠的数据备份机制\n5. 存储期限：仅在必要期间内保留您的信息'
      },
      {
        id: 'rights',
        title: '您的权利',
        content: '您对个人信息享有以下权利：\n\n1. 访问权：查看我们持有的您的个人信息\n2. 更正权：要求更正不准确的个人信息\n3. 删除权：要求删除您的个人信息\n4. 限制权：限制我们处理您的个人信息\n5. 数据携带权：获取您的个人信息副本\n6. 撤回同意：随时撤回您的授权同意'
      },
      {
        id: 'cookies',
        title: 'Cookie和类似技术',
        content: '我们使用Cookie和类似技术来：\n\n1. 记住您的偏好设置\n2. 分析应用使用情况\n3. 提供个性化体验\n4. 改进服务质量\n\n您可以通过设备设置管理Cookie偏好。'
      },
      {
        id: 'children',
        title: '儿童隐私',
        content: '我们不会故意收集13岁以下儿童的个人信息。如果我们发现收集了儿童的个人信息，将立即删除相关信息。如果您是家长或监护人，发现您的孩子向我们提供了个人信息，请联系我们。'
      },
      {
        id: 'updates',
        title: '政策更新',
        content: '我们可能会不时更新本隐私政策。重大变更时，我们会通过应用内通知、邮件或其他方式告知您。继续使用我们的服务即表示您接受更新后的隐私政策。'
      },
      {
        id: 'contact',
        title: '联系我们',
        content: '如果您对本隐私政策有任何疑问或建议，请通过以下方式联系我们：\n\n邮箱：<EMAIL>\n地址：浙江省杭州市钱塘区地址\n我们会在收到您的询问后尽快回复。'
      }
    ],
    currentSection: '',
    showBackToTop: false
  },

  onLoad() {
    console.log('隐私政策页面加载');
    wx.setNavigationBarTitle({
      title: '隐私政策'
    });
  },

  onPageScroll(e) {
    const showBackToTop = e.scrollTop > 500;
    if (showBackToTop !== this.data.showBackToTop) {
      this.setData({
        showBackToTop
      });
    }
  },

  // 跳转到指定章节
  scrollToSection(e) {
    const sectionId = e.currentTarget.dataset.section;
    this.setData({
      currentSection: sectionId
    });

    wx.pageScrollTo({
      selector: `#${sectionId}`,
      duration: 300
    });
  },

  // 回到顶部
  backToTop() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
  },

  // 联系我们
  contactUs() {
    wx.showActionSheet({
      itemList: ['发送邮件', '拨打电话', '复制邮箱'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 发送邮件（小程序中无法直接发送邮件，复制邮箱地址）
            wx.setClipboardData({
              data: '<EMAIL>',
              success: () => {
                app.showSuccess('邮箱地址已复制');
              }
            });
            break;
          case 1:
            // 拨打电话
            wx.makePhoneCall({
              phoneNumber: '************'
            });
            break;
          case 2:
            // 复制邮箱
            wx.setClipboardData({
              data: '<EMAIL>',
              success: () => {
                app.showSuccess('邮箱地址已复制');
              }
            });
            break;
        }
      }
    });
  },

  // 下载隐私政策
  downloadPolicy() {
    wx.showLoading({ title: '生成中...' });

    // 模拟生成PDF文档
    setTimeout(() => {
      wx.hideLoading();
      wx.showModal({
        title: '下载提示',
        content: '隐私政策PDF文档已生成，是否保存到本地？',
        success: (res) => {
          if (res.confirm) {
            // 实际项目中这里应该调用下载API
            app.showSuccess('文档已保存');
          }
        }
      });
    }, 1500);
  },

  // 查看历史版本
  viewHistory() {
    wx.navigateTo({
      url: '/pages/privacy/history/history'
    });
  },

  // 意见反馈
  submitFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback?type=privacy'
    });
  },

  // 分享政策
  onShareAppMessage() {
    return {
      title: '智能冰箱食材管理助手 - 隐私政策',
      desc: '了解我们如何保护您的隐私',
      path: '/pages/privacy/privacy',
      imageUrl: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&crop=center'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '智能冰箱食材管理助手 - 隐私政策',
      query: '',
      imageUrl: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&crop=center'
    };
  }
});