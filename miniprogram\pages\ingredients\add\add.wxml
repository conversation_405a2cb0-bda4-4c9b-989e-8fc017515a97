<!--食材添加/编辑页面-->
<view class="page-container {{pageAnimationClass}}">
  <!-- 顶部功能栏 -->
  <view class="top-actions" wx:if="{{!isEdit}}">
    <view class="action-btn" bindtap="scanCode">
      <view class="action-icon scan-icon"></view>
      <text>扫码</text>
    </view>
    <view class="action-btn" bindtap="voiceInput">
      <view class="action-icon voice-icon"></view>
      <text>语音</text>
    </view>
    <view class="action-btn" bindtap="toggleFeatureMenu">
      <view class="action-icon menu-icon"></view>
      <text>更多</text>
    </view>
  </view>

  <!-- 常用食材快速添加 -->
  <view class="quick-add-section" wx:if="{{!isEdit && commonIngredients.length > 0}}">
    <view class="quick-add-title">常用食材</view>
    <scroll-view scroll-x class="quick-add-scroll">
      <view class="quick-add-items">
        <view class="quick-add-item" wx:for="{{commonIngredients}}" wx:key="name" bindtap="quickAddIngredient" data-index="{{index}}">
          <image class="quick-add-image" src="{{item.image}}" mode="aspectFill"></image>
          <text class="quick-add-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <form class="ingredient-form">
    <!-- 图片上传 -->
    <view class="form-section">
      <view class="section-title">食材图片</view>
      <view class="image-upload">
        <view class="image-preview" wx:if="{{formData.image_url}}">
          <image src="{{formData.image_url}}" mode="aspectFill" class="preview-img"></image>
          <view class="image-actions">
            <view class="delete-img-btn" bindtap="deleteImage">
              <text class="delete-icon">×</text>
            </view>
          </view>
        </view>
        <view class="upload-placeholder" wx:else bindtap="chooseImage">
          <text class="upload-text">{{uploading ? '上传中...' : '点击添加图片'}}</text>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>
      
      <view class="form-group {{formErrors.name ? 'has-error' : ''}}">
        <view class="form-label">食材名称 <text class="required">*</text></view>
        <view class="input-wrapper">
          <input 
            class="form-input" 
            type="text" 
            placeholder="请输入食材名称"
            value="{{formData.name}}"
            data-field="name"
            bindinput="onInputChange"
            maxlength="50"
          />
        </view>
        <view class="error-message" wx:if="{{formErrors.name}}">{{formErrors.name}}</view>
      </view>

      <view class="form-group {{formErrors.category ? 'has-error' : ''}}">
        <view class="form-label">食材分类 <text class="required">*</text></view>
        <view class="picker-wrapper" bindtap="showCategoryPicker">
          <text class="picker-text {{formData.category ? '' : 'placeholder'}}">
            {{formData.category || '请选择分类'}}
          </text>
        </view>
        <view class="error-message" wx:if="{{formErrors.category}}">{{formErrors.category}}</view>
        
        <!-- 最近使用的分类 -->
        <view class="recent-options" wx:if="{{recentCategories.length > 0 && !formData.category}}">
          <text class="recent-label">最近：</text>
          <view class="recent-tags">
            <view 
              class="recent-tag" 
              wx:for="{{recentCategories}}" 
              wx:key="*this" 
              bindtap="onCategorySelect" 
              data-value="{{item}}"
            >{{item}}</view>
          </view>
        </view>
      </view>

      <view class="form-group {{formErrors.quantity || formErrors.unit ? 'has-error' : ''}}">
        <view class="form-label">数量 <text class="required">*</text></view>
        <view class="quantity-input">
          <input 
            class="form-input quantity-field" 
            type="digit" 
            placeholder="请输入数量"
            value="{{formData.quantity}}"
            data-field="quantity"
            bindinput="onInputChange"
          />
          <view class="unit-selector" bindtap="showUnitPicker">
            <text class="unit-text {{formData.unit ? '' : 'placeholder'}}">
              {{formData.unit || '单位'}}
            </text>
          </view>
        </view>
        <view class="error-message" wx:if="{{formErrors.quantity}}">{{formErrors.quantity}}</view>
        <view class="error-message" wx:if="{{formErrors.unit && !formErrors.quantity}}">{{formErrors.unit}}</view>
        
        <!-- 最近使用的单位 -->
        <view class="recent-options" wx:if="{{recentUnits.length > 0 && !formData.unit}}">
          <text class="recent-label">常用单位：</text>
          <view class="recent-tags">
            <view 
              class="recent-tag" 
              wx:for="{{recentUnits}}" 
              wx:key="*this" 
              bindtap="onUnitSelect" 
              data-value="{{item}}"
            >{{item}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 日期信息 -->
    <view class="form-section">
      <view class="section-title">日期信息</view>

      <view class="form-group">
        <view class="form-label">购买日期</view>
        <picker 
          mode="date" 
          value="{{formData.purchase_date}}" 
          data-field="purchase_date"
          bindchange="onDateChange"
        >
          <view class="picker-wrapper">
            <text class="picker-text {{formData.purchase_date ? '' : 'placeholder'}}">
              {{formData.purchase_date || '请选择购买日期'}}
            </text>
          </view>
        </picker>
      </view>

      <view class="form-group {{formErrors.expire_date ? 'has-error' : ''}}">
        <view class="form-label">过期日期 <text class="required">*</text></view>
        <picker 
          mode="date" 
          value="{{formData.expire_date}}" 
          data-field="expire_date"
          bindchange="onDateChange"
        >
          <view class="picker-wrapper {{expireDateWarning ? 'warning-' + expireDateStatus : ''}}">
            <text class="picker-text {{formData.expire_date ? '' : 'placeholder'}}">
              {{formData.expire_date || '请选择过期日期'}}
            </text>
          </view>
        </picker>
        <view class="error-message" wx:if="{{formErrors.expire_date}}">{{formErrors.expire_date}}</view>
        <view class="warning-message" wx:if="{{expireDateWarning}}">{{expireDateMessage}}</view>
      </view>
    </view>

    <!-- 存储信息 -->
    <view class="form-section">
      <view class="section-title">存储信息</view>
      
      <view class="form-group">
        <view class="form-label">存放位置</view>
        <view class="picker-wrapper" bindtap="showLocationPicker">
          <text class="picker-text {{formData.location ? '' : 'placeholder'}}">
            {{formData.location || '请选择存放位置'}}
          </text>
        </view>
        
        <!-- 最近使用的位置 -->
        <view class="recent-options" wx:if="{{recentLocations.length > 0 && !formData.location}}">
          <text class="recent-label">常用位置：</text>
          <view class="recent-tags">
            <view 
              class="recent-tag" 
              wx:for="{{recentLocations}}" 
              wx:key="*this" 
              bindtap="onLocationSelect" 
              data-value="{{item}}"
            >{{item}}</view>
          </view>
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">备注信息</view>
        <textarea 
          class="form-textarea" 
          placeholder="请输入备注信息（可选）"
          value="{{formData.notes}}"
          data-field="notes"
          bindinput="onInputChange"
          maxlength="200"
          show-confirm-bar="false"
        ></textarea>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="form-actions">
      <button 
        class="btn btn-secondary btn-block reset-btn" 
        bindtap="resetForm"
        wx:if="{{!isEdit}}"
      >
        重置
      </button>
      <button 
        class="btn btn-primary btn-block save-btn" 
        bindtap="saveIngredient"
        loading="{{loading}}"
        disabled="{{loading}}"
      >
        {{loading ? '保存中...' : (isEdit ? '更新食材' : '添加食材')}}
      </button>
    </view>
  </form>

  <!-- 分类选择器遮罩 -->
  <view
    class="picker-mask {{showCategoryPicker ? 'show' : ''}}"
    wx:if="{{showCategoryPicker}}"
    bindtap="onCategoryCancel"
  ></view>

  <!-- 分类选择器 -->
  <view
    class="custom-picker {{showCategoryPicker ? 'show' : ''}}"
    wx:if="{{showCategoryPicker}}"
  >
    <view class="picker-header">
      <text class="picker-cancel" bindtap="onCategoryCancel">取消</text>
      <text class="picker-title">选择分类</text>
      <text class="picker-confirm" bindtap="onCategoryChange">确定</text>
    </view>
    <view class="picker-content">
      <view
        class="picker-item {{formData.category === item ? 'active' : ''}}"
        wx:for="{{categories}}"
        wx:key="*this"
        data-index="{{index}}"
        bindtap="onCategorySelect"
        data-value="{{item}}"
      >
        {{item}}
      </view>
    </view>
    <view class="picker-safe-area"></view>
  </view>

  <!-- 单位选择器遮罩 -->
  <view
    class="picker-mask {{showUnitPicker ? 'show' : ''}}"
    wx:if="{{showUnitPicker}}"
    bindtap="onUnitCancel"
  ></view>

  <!-- 单位选择器 -->
  <view
    class="custom-picker {{showUnitPicker ? 'show' : ''}}"
    wx:if="{{showUnitPicker}}"
  >
    <view class="picker-header">
      <text class="picker-cancel" bindtap="onUnitCancel">取消</text>
      <text class="picker-title">选择单位</text>
      <text class="picker-confirm" bindtap="onUnitChange">确定</text>
    </view>
    <view class="picker-content">
      <view
        class="picker-item {{formData.unit === item ? 'active' : ''}}"
        wx:for="{{units}}"
        wx:key="*this"
        data-index="{{index}}"
        bindtap="onUnitSelect"
        data-value="{{item}}"
      >
        {{item}}
      </view>
    </view>
    <view class="picker-safe-area"></view>
  </view>

  <!-- 位置选择器遮罩 -->
  <view
    class="picker-mask {{showLocationPicker ? 'show' : ''}}"
    wx:if="{{showLocationPicker}}"
    bindtap="onLocationCancel"
  ></view>

  <!-- 位置选择器 -->
  <view
    class="custom-picker {{showLocationPicker ? 'show' : ''}}"
    wx:if="{{showLocationPicker}}"
  >
    <view class="picker-header">
      <text class="picker-cancel" bindtap="onLocationCancel">取消</text>
      <text class="picker-title">选择位置</text>
      <text class="picker-confirm" bindtap="onLocationChange">确定</text>
    </view>
    <view class="picker-content">
      <view
        class="picker-item {{formData.location === item ? 'active' : ''}}"
        wx:for="{{locations}}"
        wx:key="*this"
        data-index="{{index}}"
        bindtap="onLocationSelect"
        data-value="{{item}}"
      >
        {{item}}
      </view>
    </view>
    <view class="picker-safe-area"></view>
  </view>
  
  <!-- 功能菜单 -->
  <view class="feature-menu {{showFeatureMenu ? 'show' : ''}}" wx:if="{{showFeatureMenu}}">
    <view class="feature-menu-mask" bindtap="toggleFeatureMenu"></view>
    <view class="feature-menu-content">
      <view class="feature-menu-header">
        <text class="feature-menu-title">更多功能</text>
        <view class="feature-menu-close" bindtap="toggleFeatureMenu">×</view>
      </view>
      <view class="feature-menu-items">
        <view class="feature-menu-item" bindtap="scanCode">
          <view class="feature-icon scan-icon"></view>
          <text>扫码添加</text>
        </view>
        <view class="feature-menu-item" bindtap="voiceInput">
          <view class="feature-icon voice-icon"></view>
          <text>语音输入</text>
        </view>
        <view class="feature-menu-item" bindtap="toggleScanTips">
          <view class="feature-icon help-icon"></view>
          <text>扫码帮助</text>
        </view>
        <view class="feature-menu-item" bindtap="toggleVoiceTips">
          <view class="feature-icon help-icon"></view>
          <text>语音帮助</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 扫码提示 -->
  <view class="tips-modal {{showScanTips ? 'show' : ''}}" wx:if="{{showScanTips}}">
    <view class="tips-mask" bindtap="toggleScanTips"></view>
    <view class="tips-content">
      <view class="tips-header">
        <text class="tips-title">扫码添加说明</text>
        <view class="tips-close" bindtap="toggleScanTips">×</view>
      </view>
      <view class="tips-body">
        <view class="tips-item">
          <text class="tips-number">1</text>
          <text class="tips-text">扫描食材包装上的条形码</text>
        </view>
        <view class="tips-item">
          <text class="tips-number">2</text>
          <text class="tips-text">系统会自动识别食材信息</text>
        </view>
        <view class="tips-item">
          <text class="tips-number">3</text>
          <text class="tips-text">您可以修改或补充其他信息</text>
        </view>
        <view class="tips-note">注：目前支持大部分常见食材的条形码识别</view>
      </view>
    </view>
  </view>
  
  <!-- 语音提示 -->
  <view class="tips-modal {{showVoiceTips ? 'show' : ''}}" wx:if="{{showVoiceTips}}">
    <view class="tips-mask" bindtap="toggleVoiceTips"></view>
    <view class="tips-content">
      <view class="tips-header">
        <text class="tips-title">语音输入说明</text>
        <view class="tips-close" bindtap="toggleVoiceTips">×</view>
      </view>
      <view class="tips-body">
        <view class="tips-item">
          <text class="tips-number">1</text>
          <text class="tips-text">点击语音按钮开始录音</text>
        </view>
        <view class="tips-item">
          <text class="tips-number">2</text>
          <text class="tips-text">清晰说出食材名称</text>
        </view>
        <view class="tips-item">
          <text class="tips-number">3</text>
          <text class="tips-text">系统会自动识别并填写表单</text>
        </view>
        <view class="tips-note">注：请在安静环境下使用，语音识别准确率更高</view>
      </view>
    </view>
  </view>
</view>