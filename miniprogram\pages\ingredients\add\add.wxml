<!--食材添加/编辑页面-->
<view class="page-container">
  <form class="ingredient-form">
    <!-- 图片上传 -->
    <view class="form-section">
      <view class="section-title">食材图片</view>
      <view class="image-upload">
        <view class="image-preview" wx:if="{{formData.image_url}}">
          <image src="{{formData.image_url}}" mode="aspectFill" class="preview-img"></image>
          <view class="image-actions">
            <view class="action-btn delete-img-btn" bindtap="deleteImage">
              <text class="delete-icon">删除</text>
            </view>
          </view>
        </view>
        <view class="upload-placeholder" wx:else bindtap="chooseImage">

          <text class="upload-text">{{uploading ? '上传中...' : '点击添加图片'}}</text>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>
      
      <view class="form-group">
        <view class="form-label">食材名称 <text class="required">*</text></view>
        <view class="input-wrapper">
          <input 
            class="form-input" 
            type="text" 
            placeholder="请输入食材名称"
            value="{{formData.name}}"
            data-field="name"
            bindinput="onInputChange"
            maxlength="50"
          />
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">食材分类 <text class="required">*</text></view>
        <view class="picker-wrapper" bindtap="showCategoryPicker">
          <text class="picker-text {{formData.category ? '' : 'placeholder'}}">
            {{formData.category || '请选择分类'}}
          </text>

        </view>
      </view>

      <view class="form-group">
        <view class="form-label">数量 <text class="required">*</text></view>
        <view class="quantity-input">
          <input 
            class="form-input quantity-field" 
            type="digit" 
            placeholder="请输入数量"
            value="{{formData.quantity}}"
            data-field="quantity"
            bindinput="onInputChange"
          />
          <view class="unit-selector" bindtap="showUnitPicker">
            <text class="unit-text {{formData.unit ? '' : 'placeholder'}}">
              {{formData.unit || '单位'}}
            </text>

          </view>
        </view>
      </view>
    </view>

    <!-- 日期信息 -->
    <view class="form-section">
      <view class="section-title">日期信息</view>

      <view class="form-group">
        <view class="form-label">购买日期</view>
        <picker 
          mode="date" 
          value="{{formData.purchase_date}}" 
          data-field="purchase_date"
          bindchange="onDateChange"
        >
          <view class="picker-wrapper">
            <text class="picker-text {{formData.purchase_date ? '' : 'placeholder'}}">
              {{formData.purchase_date || '请选择购买日期'}}
            </text>

          </view>
        </picker>
      </view>

      <view class="form-group">
        <view class="form-label">过期日期 <text class="required">*</text></view>
        <picker 
          mode="date" 
          value="{{formData.expire_date}}" 
          data-field="expire_date"
          bindchange="onDateChange"
        >
          <view class="picker-wrapper">
            <text class="picker-text {{formData.expire_date ? '' : 'placeholder'}}">
              {{formData.expire_date || '请选择过期日期'}}
            </text>

          </view>
        </picker>
      </view>
    </view>

    <!-- 存储信息 -->
    <view class="form-section">
      <view class="section-title">存储信息</view>
      
      <view class="form-group">
        <view class="form-label">存放位置</view>
        <view class="picker-wrapper" bindtap="showLocationPicker">
          <text class="picker-text {{formData.location ? '' : 'placeholder'}}">
            {{formData.location || '请选择存放位置'}}
          </text>

        </view>
      </view>

      <view class="form-group">
        <view class="form-label">备注信息</view>
        <textarea 
          class="form-textarea" 
          placeholder="请输入备注信息（可选）"
          value="{{formData.notes}}"
          data-field="notes"
          bindinput="onInputChange"
          maxlength="200"
          show-confirm-bar="false"
        ></textarea>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="form-actions">
      <button 
        class="btn btn-secondary btn-block reset-btn" 
        bindtap="resetForm"
        wx:if="{{!isEdit}}"
      >
        重置
      </button>
      <button 
        class="btn btn-primary btn-block save-btn" 
        bindtap="saveIngredient"
        loading="{{loading}}"
        disabled="{{loading}}"
      >
        {{loading ? '保存中...' : (isEdit ? '更新食材' : '添加食材')}}
      </button>
    </view>
  </form>

  <!-- 分类选择器遮罩 -->
  <view
    class="picker-mask {{showCategoryPicker ? 'show' : ''}}"
    wx:if="{{showCategoryPicker}}"
    bindtap="onCategoryCancel"
  ></view>

  <!-- 分类选择器 -->
  <picker-view
    class="custom-picker {{showCategoryPicker ? 'show' : ''}}"
    wx:if="{{showCategoryPicker}}"
  >
    <view class="picker-header">
      <text class="picker-cancel" bindtap="onCategoryCancel">取消</text>
      <text class="picker-title">选择分类</text>
      <text class="picker-confirm" bindtap="onCategoryChange">确定</text>
    </view>
    <picker-view-column>
      <view 
        class="picker-item"
        wx:for="{{categories}}"
        wx:key="*this"
        data-index="{{index}}"
        bindtap="onCategoryChange"
      >
        {{item}}
      </view>
    </picker-view-column>
  </picker-view>

  <!-- 单位选择器遮罩 -->
  <view
    class="picker-mask {{showUnitPicker ? 'show' : ''}}"
    wx:if="{{showUnitPicker}}"
    bindtap="onUnitCancel"
  ></view>

  <!-- 单位选择器 -->
  <picker-view
    class="custom-picker {{showUnitPicker ? 'show' : ''}}"
    wx:if="{{showUnitPicker}}"
  >
    <view class="picker-header">
      <text class="picker-cancel" bindtap="onUnitCancel">取消</text>
      <text class="picker-title">选择单位</text>
      <text class="picker-confirm" bindtap="onUnitChange">确定</text>
    </view>
    <picker-view-column>
      <view 
        class="picker-item"
        wx:for="{{units}}"
        wx:key="*this"
        data-index="{{index}}"
        bindtap="onUnitChange"
      >
        {{item}}
      </view>
    </picker-view-column>
  </picker-view>

  <!-- 位置选择器遮罩 -->
  <view
    class="picker-mask {{showLocationPicker ? 'show' : ''}}"
    wx:if="{{showLocationPicker}}"
    bindtap="onLocationCancel"
  ></view>

  <!-- 位置选择器 -->
  <picker-view
    class="custom-picker {{showLocationPicker ? 'show' : ''}}"
    wx:if="{{showLocationPicker}}"
  >
    <view class="picker-header">
      <text class="picker-cancel" bindtap="onLocationCancel">取消</text>
      <text class="picker-title">选择位置</text>
      <text class="picker-confirm" bindtap="onLocationChange">确定</text>
    </view>
    <picker-view-column>
      <view 
        class="picker-item"
        wx:for="{{locations}}"
        wx:key="*this"
        data-index="{{index}}"
        bindtap="onLocationChange"
      >
        {{item}}
      </view>
    </picker-view-column>
  </picker-view>

  <!-- 遮罩层 -->
  <view 
    class="picker-mask {{showCategoryPicker || showUnitPicker || showLocationPicker ? 'show' : ''}}"
    bindtap="onCategoryCancel"
  ></view>
</view>