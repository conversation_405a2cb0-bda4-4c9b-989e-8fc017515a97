<view class="container" bindtap="hideDropdowns">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">添加食材</text>
    <text class="page-subtitle">记录您的食材信息</text>
  </view>

  <!-- 食材基本信息 -->
  <view class="form-section">
    <view class="section-title">基本信息</view>
    
    <!-- 食材名称 -->
    <view class="form-item">
      <view class="form-label">
        <text class="label-text">食材名称</text>
        <text class="required">*</text>
      </view>
      <picker
        class="form-picker"
        mode="selector"
        range="{{commonNames}}"
        value="{{nameIndex}}"
        bindchange="onNameChange"
      >
        <view class="picker-content">
          <text class="{{nameIndex === -1 ? 'placeholder' : ''}}">
            {{nameIndex === -1 ? '请选择食材名称' : commonNames[nameIndex]}}
          </text>
          <text class="picker-arrow">▶</text>
        </view>
      </picker>
    </view>

    <!-- 食材分类 -->
    <view class="form-item">
      <view class="form-label">
        <text class="label-text">食材分类</text>
        <text class="required">*</text>
      </view>
      <picker 
        class="form-picker" 
        mode="selector" 
        range="{{categories}}" 
        value="{{categoryIndex}}"
        bindchange="onCategoryChange"
      >
        <view class="picker-content">
          <text class="{{categoryIndex === -1 ? 'placeholder' : ''}}">
            {{categoryIndex === -1 ? '请选择食材分类' : categories[categoryIndex]}}
          </text>
          <text class="picker-arrow">▶</text>
        </view>
      </picker>
    </view>

    <!-- 数量 -->
    <view class="form-item">
      <view class="form-label">
        <text class="label-text">数量</text>
        <text class="required">*</text>
      </view>
      <view class="quantity-input">
        <button class="quantity-btn" bindtap="decreaseQuantity" disabled="{{formData.quantity <= 1}}">-</button>
        <input 
          class="quantity-value" 
          type="number" 
          value="{{formData.quantity}}"
          bindinput="onQuantityInput"
        />
        <button class="quantity-btn" bindtap="increaseQuantity">+</button>
        <picker 
          class="unit-picker" 
          mode="selector" 
          range="{{units}}" 
          value="{{unitIndex}}"
          bindchange="onUnitChange"
        >
          <view class="unit-display">{{unitIndex === -1 ? '单位' : units[unitIndex]}}</view>
        </picker>
      </view>
    </view>
  </view>

  <!-- 存储信息 -->
  <view class="form-section">
    <view class="section-title">存储信息</view>
    
    <!-- 存储位置 -->
    <view class="form-item">
      <view class="form-label">
        <text class="label-text">存储位置</text>
      </view>
      <picker 
        class="form-picker" 
        mode="selector" 
        range="{{storageLocations}}" 
        value="{{storageIndex}}"
        bindchange="onStorageChange"
      >
        <view class="picker-content">
          <text class="{{storageIndex === -1 ? 'placeholder' : ''}}">
            {{storageIndex === -1 ? '请选择存储位置' : storageLocations[storageIndex]}}
          </text>
          <text class="picker-arrow">▶</text>
        </view>
      </picker>
    </view>

    <!-- 购买日期 -->
    <view class="form-item">
      <view class="form-label">
        <text class="label-text">购买日期</text>
      </view>
      <picker 
        class="form-picker" 
        mode="date" 
        value="{{formData.purchaseDate}}"
        bindchange="onPurchaseDateChange"
      >
        <view class="picker-content">
          <text class="{{!formData.purchaseDate ? 'placeholder' : ''}}">
            {{formData.purchaseDate || '请选择购买日期'}}
          </text>
          <text class="picker-arrow">▶</text>
        </view>
      </picker>
    </view>

    <!-- 保质期 -->
    <view class="form-item">
      <view class="form-label">
        <text class="label-text">保质期</text>
        <text class="required">*</text>
      </view>
      <picker 
        class="form-picker" 
        mode="date" 
        value="{{formData.expiryDate}}"
        bindchange="onExpiryDateChange"
      >
        <view class="picker-content">
          <text class="{{!formData.expiryDate ? 'placeholder' : ''}}">
            {{formData.expiryDate || '请选择保质期'}}
          </text>
          <text class="picker-arrow">▶</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 价格信息 -->
  <view class="form-section">
    <view class="section-title">价格信息</view>
    
    <view class="form-item">
      <view class="form-label">
        <text class="label-text">购买价格</text>
      </view>
      <view class="price-input">
        <text class="currency">¥</text>
        <input 
          class="form-input price-value" 
          type="digit" 
          placeholder="0.00" 
          value="{{formData.price}}"
          bindinput="onPriceInput"
        />
      </view>
    </view>
  </view>

  <!-- 备注信息 -->
  <view class="form-section">
    <view class="section-title">备注信息</view>
    
    <view class="form-item">
      <view class="form-label">
        <text class="label-text">备注</text>
      </view>
      <textarea 
        class="form-textarea" 
        placeholder="请输入备注信息（可选）" 
        value="{{formData.notes}}"
        bindinput="onNotesInput"
        maxlength="200"
        show-confirm-bar="{{false}}"
      />
</text>
      <view class="textarea-counter">{{formData.notes.length}}/200</view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="button-group">
    <button class="btn btn-secondary" bindtap="onCancel">取消</button>
    <button 
      class="btn btn-primary" 
      bindtap="onSubmit"
      disabled="{{!canSubmit}}"
      loading="{{submitting}}"
    >
      {{submitting ? '保存中...' : '保存'}}
    </button>
  </view>

</view>