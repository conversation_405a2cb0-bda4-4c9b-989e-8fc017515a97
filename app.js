const express = require('express');
const cors = require('cors');
const path = require('path');
const { initDatabase } = require('./config/database');

// 导入路由
const usersRouter = require('./routes/users');
const ingredientsRouter = require('./routes/ingredients');
const recipesRouter = require('./routes/recipes');
const shoppingRouter = require('./routes/shopping');
const favoritesRouter = require('./routes/favorites');
const recordsRouter = require('./routes/records');
const analysisRouter = require('./routes/analysis');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件配置
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : ['*'],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use('/public', express.static(path.join(__dirname, 'public')));

// API路由
app.use('/api/users', usersRouter);
app.use('/api/ingredients', ingredientsRouter);
app.use('/api/recipes', recipesRouter);
app.use('/api/shopping', shoppingRouter);
app.use('/api/favorites', favoritesRouter);
app.use('/api/records', recordsRouter);
app.use('/api/analysis', analysisRouter);

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: '智能冰箱食材管理助手服务运行正常',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 根路径
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '欢迎使用智能冰箱食材管理助手API',
    version: '1.0.0',
    endpoints: {
      users: '/api/users',
      ingredients: '/api/ingredients',
      recipes: '/api/recipes',
      shopping: '/api/shopping',
      favorites: '/api/favorites',
      records: '/api/records',
      analysis: '/api/analysis',
      health: '/health'
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    path: req.originalUrl
  });
});

// 全局错误处理中间件
app.use((error, req, res, next) => {
  console.error('全局错误:', error);
  
  // 数据库连接错误
  if (error.code === 'ECONNREFUSED' || error.code === 'ER_ACCESS_DENIED_ERROR') {
    return res.status(500).json({
      success: false,
      message: '数据库连接失败，请检查数据库配置'
    });
  }

  // JSON解析错误
  if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
    return res.status(400).json({
      success: false,
      message: '请求数据格式错误'
    });
  }

  // 文件上传错误
  if (error.code === 'LIMIT_FILE_SIZE') {
    return res.status(400).json({
      success: false,
      message: '文件大小超出限制'
    });
  }

  // 默认错误响应
  res.status(error.status || 500).json({
    success: false,
    message: error.message || '服务器内部错误',
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

// 启动服务器
async function startServer() {
  try {
    // 连接数据库
    await initDatabase();
    console.log('✅ 数据库连接成功');

    // 创建上传目录
    const fs = require('fs');
    const uploadDirs = ['uploads', 'uploads/avatars', 'uploads/ingredients', 'uploads/recipes'];
    
    uploadDirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`✅ 创建目录: ${dir}`);
      }
    });

    // 启动服务器
    app.listen(PORT, () => {
      console.log(`🚀 智能冰箱食材管理助手服务已启动`);
      console.log(`📍 服务地址: http://127.0.0.1:${PORT}`);
      console.log(`🏥 健康检查: http://127.0.0.1:${PORT}/health`);
      console.log(`📚 API文档: http://127.0.0.1:${PORT}/`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
    });

  } catch (error) {
    console.error('❌ 服务启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭处理
process.on('SIGTERM', () => {
  console.log('📴 收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('📴 收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 启动应用
if (require.main === module) {
  startServer();
}

module.exports = app;