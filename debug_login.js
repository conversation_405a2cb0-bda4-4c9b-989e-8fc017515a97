const { query } = require('./config/database');
const bcrypt = require('bcryptjs');

async function debugLogin() {
  try {
    console.log('开始调试登录问题...');
    
    // 查询所有用户
    const allUsers = await query('SELECT id, phone, nickname, status, is_deleted FROM users');
    console.log('所有用户:', allUsers);
    
    // 测试特定用户登录
    const testPhone = '18800000000'; // 使用数据库中存在的手机号
    
    const users = await query(
      'SELECT id, phone, password, nickname, avatar, status FROM users WHERE phone = ? AND is_deleted = 0',
      [testPhone]
    );
    
    console.log('查询结果:', users);
    
    if (users.length > 0) {
      const user = users[0];
      console.log('用户信息:', {
        id: user.id,
        phone: user.phone,
        nickname: user.nickname,
        status: user.status,
        statusType: typeof user.status
      });
      
      // 检查状态判断
      console.log('状态检查:');
      console.log('user.status:', user.status);
      console.log('user.status !== 1:', user.status !== 1);
      console.log('user.status == 1:', user.status == 1);
      console.log('user.status === 1:', user.status === 1);
    }
    
  } catch (error) {
    console.error('调试错误:', error);
  }
  
  process.exit(0);
}

debugLogin();