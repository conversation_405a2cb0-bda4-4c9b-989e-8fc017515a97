/* 收藏列表页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.action-icon:active {
  opacity: 0.6;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-input-wrapper {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 25rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  gap: 15rpx;
  height: 70rpx;
}

.search-icon {
  width: 30rpx;
  height: 30rpx;
  opacity: 0.7;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: white;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.clear-icon {
  width: 30rpx;
  height: 30rpx;
  opacity: 0.7;
}

.search-cancel {
  font-size: 28rpx;
  color: white;
  opacity: 0.9;
}

/* 分类标签 */
.category-tabs {
  background-color: white;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e0e0e0;
}

.tab-list {
  display: flex;
  padding: 0 30rpx;
  gap: 30rpx;
}

.tab-item {
  white-space: nowrap;
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.tab-item.active {
  background-color: #667eea;
  color: white;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 编辑工具栏 */
.edit-toolbar {
  background-color: #fff3cd;
  border-bottom: 1rpx solid #ffeaa7;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.edit-info {
  display: flex;
  align-items: center;
}

.selected-count {
  font-size: 28rpx;
  color: #856404;
  font-weight: 500;
}

.edit-actions {
  display: flex;
  gap: 20rpx;
}

.edit-btn,
.delete-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
}

.edit-btn {
  background-color: #6c757d;
  color: white;
}

.delete-btn {
  background-color: #dc3545;
  color: white;
}

.delete-btn[disabled] {
  background-color: #ccc;
  color: #999;
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
  padding: 20rpx;
}

.favorites-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 收藏项 */
.favorite-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.favorite-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  transition: all 0.3s ease;
  position: relative;
}

.favorite-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

.favorite-item.edit-mode {
  padding-left: 80rpx;
}

/* 选择框 */
.select-checkbox {
  position: absolute;
  left: 30rpx;
  top: 30rpx;
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.select-checkbox.checked {
  border-color: #667eea;
  background-color: #667eea;
}

.check-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 收藏项内容 */
.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.item-header {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.type-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  background-color: #f8f9fa;
  padding: 10rpx;
  box-sizing: border-box;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.item-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.item-type {
  font-size: 24rpx;
  color: #667eea;
  background-color: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.item-time {
  font-size: 24rpx;
  color: #999;
}

.remove-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.remove-icon:active {
  opacity: 1;
  transform: scale(1.1);
}

/* 收藏项描述 */
.item-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 标签 */
.item-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag {
  font-size: 22rpx;
  color: #666;
  background-color: #f1f3f4;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  gap: 20rpx;
}

.no-more {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 26rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  gap: 30rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

/* 编辑模式遮罩 */
.edit-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .page-header {
    padding: 15rpx 20rpx;
  }
  
  .header-title {
    font-size: 32rpx;
  }
  
  .content-scroll {
    padding: 15rpx;
  }
  
  .favorite-item {
    padding: 20rpx;
  }
  
  .favorite-item.edit-mode {
    padding-left: 70rpx;
  }
  
  .select-checkbox {
    left: 20rpx;
    top: 20rpx;
    width: 35rpx;
    height: 35rpx;
  }
  
  .item-header {
    gap: 15rpx;
  }
  
  .type-icon {
    width: 50rpx;
    height: 50rpx;
  }
  
  .item-title {
    font-size: 28rpx;
  }
  
  .edit-toolbar {
    padding: 15rpx 20rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .category-tabs,
  .favorite-item,
  .edit-toolbar {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .tab-item {
    background-color: #404040;
    color: white;
  }
  
  .tab-item.active {
    background-color: #667eea;
  }
  
  .item-title {
    color: white;
  }
  
  .item-description {
    color: #ccc;
  }
  
  .item-time {
    color: #999;
  }
  
  .tag {
    background-color: #404040;
    color: #ccc;
  }
  
  .empty-title {
    color: white;
  }
  
  .empty-desc {
    color: #999;
  }
  
  .loading-text {
    color: #ccc;
  }
  
  .selected-count {
    color: #ffc107;
  }
  
  .edit-toolbar {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: rgba(255, 193, 7, 0.3);
  }
}

/* 动画效果 */
.favorite-item {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tab-item {
  animation: fadeInScale 0.3s ease;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.edit-toolbar {
  animation: slideInDown 0.3s ease;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.select-checkbox {
  animation: bounceIn 0.3s ease;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}