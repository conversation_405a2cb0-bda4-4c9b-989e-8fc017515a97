/* 食材分析页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  padding: 20rpx 30rpx 30rpx;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.action-icon:active {
  opacity: 0.6;
}

/* 时间范围选择 */
.time-range-tabs {
  display: flex;
  gap: 20rpx;
}

.time-tab {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.time-tab.active {
  background-color: white;
  color: #4CAF50;
  font-weight: 600;
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
}

.analysis-content {
  padding: 30rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 总体统计 */
.overview-section {
  margin-bottom: 30rpx;
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.overview-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.overview-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  line-height: 1;
}

.overview-label {
  font-size: 24rpx;
  color: #999;
}

.overview-divider {
  width: 1rpx;
  height: 60rpx;
  background-color: #e9ecef;
  margin: 0 20rpx;
}

/* 图表类型切换 */
.chart-tabs {
  display: flex;
  background-color: white;
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.chart-tab {
  flex: 1;
  text-align: center;
  padding: 15rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.chart-tab.active {
  background-color: #4CAF50;
  color: white;
  font-weight: 600;
}

/* 图表容器 */
.chart-section {
  margin-bottom: 30rpx;
}

.chart-container {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 分类统计 */
.category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.category-item:last-child {
  border-bottom: none;
}

.category-item:active {
  background-color: #f8f9fa;
  margin: 0 -30rpx;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.category-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 12rpx;
  background-color: #f8f9fa;
  padding: 8rpx;
  box-sizing: border-box;
}

.category-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.category-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.category-count {
  font-size: 24rpx;
  color: #999;
}

.category-progress {
  display: flex;
  align-items: center;
  gap: 15rpx;
  min-width: 150rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background-color: #e9ecef;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #4CAF50;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  min-width: 50rpx;
  text-align: right;
}

/* 状态分布 */
.status-chart {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 40rpx 0;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  transition: all 0.3s ease;
}

.status-item:active {
  transform: scale(0.95);
}

.status-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.status-number {
  font-size: 36rpx;
  font-weight: 700;
  color: white;
}

.status-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 存储位置统计 */
.storage-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.storage-item:last-child {
  border-bottom: none;
}

.storage-item:active {
  background-color: #f8f9fa;
  margin: 0 -30rpx;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
}

.storage-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.storage-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 12rpx;
  background-color: #f8f9fa;
  padding: 8rpx;
  box-sizing: border-box;
}

.storage-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.storage-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.storage-count {
  font-size: 24rpx;
  color: #999;
}

.storage-progress {
  display: flex;
  align-items: center;
  gap: 15rpx;
  min-width: 150rpx;
}

/* 趋势分析 */
.trend-chart {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 200rpx;
  padding: 20rpx 0;
  gap: 10rpx;
}

.trend-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  height: 100%;
}

.trend-bar {
  flex: 1;
  width: 30rpx;
  background-color: #f0f0f0;
  border-radius: 15rpx;
  display: flex;
  align-items: end;
  overflow: hidden;
}

.trend-fill {
  width: 100%;
  background-color: #4CAF50;
  border-radius: 15rpx;
  transition: height 0.5s ease;
  min-height: 10rpx;
}

.trend-label {
  font-size: 22rpx;
  color: #999;
}

.trend-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 600;
}

/* 管理建议 */
.suggestions-section {
  margin-bottom: 30rpx;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.suggestion-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 25rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 20rpx;
  border-left: 6rpx solid #e9ecef;
}

.suggestion-item.warning {
  border-left-color: #ffc107;
}

.suggestion-item.danger {
  border-left-color: #dc3545;
}

.suggestion-item.info {
  border-left-color: #17a2b8;
}

.suggestion-icon {
  width: 40rpx;
  height: 40rpx;
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.suggestion-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.suggestion-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.suggestion-action {
  padding: 12rpx 20rpx;
  background-color: #4CAF50;
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.suggestion-action:active {
  background-color: #45a049;
  transform: scale(0.95);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .analysis-content {
    padding: 20rpx;
  }
  
  .overview-card {
    padding: 25rpx;
  }
  
  .overview-number {
    font-size: 42rpx;
  }
  
  .overview-divider {
    margin: 0 15rpx;
  }
  
  .chart-container {
    padding: 25rpx;
  }
  
  .status-circle {
    width: 100rpx;
    height: 100rpx;
  }
  
  .status-number {
    font-size: 32rpx;
  }
  
  .trend-chart {
    height: 180rpx;
  }
  
  .page-header {
    padding: 15rpx 20rpx 25rpx;
  }
  
  .header-title {
    font-size: 32rpx;
  }
  
  .time-range-tabs {
    gap: 15rpx;
  }
  
  .time-tab {
    padding: 10rpx 20rpx;
    font-size: 24rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .overview-card,
  .chart-container,
  .suggestion-item {
    background-color: #2d2d2d;
  }
  
  .chart-tabs {
    background-color: #2d2d2d;
  }
  
  .chart-tab {
    color: #ccc;
  }
  
  .chart-tab.active {
    background-color: #4CAF50;
    color: white;
  }
  
  .overview-number,
  .section-title,
  .category-name,
  .storage-name,
  .trend-value,
  .suggestion-title {
    color: white;
  }
  
  .overview-label,
  .category-count,
  .storage-count,
  .trend-label,
  .progress-text,
  .status-label,
  .suggestion-desc {
    color: #ccc;
  }
  
  .overview-divider {
    background-color: #404040;
  }
  
  .progress-bar,
  .trend-bar {
    background-color: #404040;
  }
  
  .category-item:active,
  .storage-item:active {
    background-color: #404040;
  }
  
  .category-icon,
  .storage-icon {
    background-color: #404040;
  }
}

/* 动画效果 */
.overview-card,
.chart-container,
.suggestion-item {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chart-tab {
  animation: fadeInScale 0.3s ease;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.status-item {
  animation: bounceIn 0.4s ease;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.category-item,
.storage-item {
  animation: slideInLeft 0.3s ease;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.trend-item {
  animation: slideInUp 0.5s ease;
}

.suggestion-item {
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 进度条动画 */
.progress-fill,
.trend-fill {
  animation: progressFill 1s ease-out;
}

@keyframes progressFill {
  from {
    width: 0;
    height: 0;
  }
}

/* 数字计数动画 */
.overview-number,
.status-number,
.trend-value {
  animation: countUp 0.8s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}