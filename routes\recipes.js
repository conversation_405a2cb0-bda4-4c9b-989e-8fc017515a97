const express = require('express');
const { query } = require('../config/database');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const { uploadSingle } = require('../middleware/upload');

const router = express.Router();

// 获取菜谱列表
router.get('/', optionalAuth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      difficulty,
      search,
      sort = 'created_at',
      order = 'DESC'
    } = req.query;

    // 验证排序字段
    const validSortFields = ['created_at', 'updated_at', 'name', 'like_count', 'view_count', 'cook_time'];
    const sortField = validSortFields.includes(sort) ? sort : 'created_at';
    
    const offset = (page - 1) * limit;
    let whereConditions = ['is_public = 1', 'is_deleted = 0'];
    let queryParams = [];

    // 按分类筛选
    if (category) {
      whereConditions.push('category = ?');
      queryParams.push(category);
    }

    // 按难度筛选
    if (difficulty) {
      whereConditions.push('difficulty = ?');
      queryParams.push(difficulty);
    }

    // 搜索功能
    if (search) {
      whereConditions.push('(name LIKE ? OR description LIKE ?)');
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // 构建查询语句
    const whereClause = whereConditions.join(' AND ');
    const orderClause = `ORDER BY ${sortField} ${order}`;

    const recipes = await query(`
      SELECT 
        r.*,
        CASE WHEN uf.recipe_id IS NOT NULL THEN 1 ELSE 0 END as is_favorited
      FROM recipes r
      LEFT JOIN user_favorites uf ON r.id = uf.recipe_id AND uf.user_id = ?
      WHERE ${whereClause}
      ${orderClause}
      LIMIT ? OFFSET ?
    `, [req.user?.id || null, ...queryParams, parseInt(limit), offset]);

    // 解析JSON字段
    recipes.forEach(recipe => {
      try {
        recipe.ingredients = recipe.ingredients ? JSON.parse(recipe.ingredients) : [];
        recipe.steps = recipe.steps ? JSON.parse(recipe.steps) : [];
      } catch (error) {
        console.error('解析菜谱JSON字段错误:', error);
        recipe.ingredients = [];
        recipe.steps = [];
      }
    });

    // 获取总数
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM recipes
      WHERE ${whereClause}
    `, queryParams);

    const total = countResult[0].total;

    res.json({
      success: true,
      data: {
        recipes,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取菜谱列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取菜谱列表失败'
    });
  }
});

// 获取菜谱详情
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const recipes = await query(`
      SELECT 
        r.*,
        CASE WHEN uf.recipe_id IS NOT NULL THEN 1 ELSE 0 END as is_favorited
      FROM recipes r
      LEFT JOIN user_favorites uf ON r.id = uf.recipe_id AND uf.user_id = ?
      WHERE r.id = ? AND r.is_public = 1 AND r.is_deleted = 0
    `, [req.user?.id || null, id]);

    if (recipes.length === 0) {
      return res.status(404).json({
        success: false,
        message: '菜谱不存在'
      });
    }

    const recipe = recipes[0];

    // 解析JSON字段
    try {
      recipe.ingredients = recipe.ingredients ? JSON.parse(recipe.ingredients) : [];
      recipe.steps = recipe.steps ? JSON.parse(recipe.steps) : [];
    } catch (error) {
      console.error('解析菜谱JSON字段错误:', error);
      recipe.ingredients = [];
      recipe.steps = [];
    }

    // 增加浏览数
    await query(
      'UPDATE recipes SET view_count = view_count + 1 WHERE id = ?',
      [id]
    );

    res.json({
      success: true,
      data: recipe
    });
  } catch (error) {
    console.error('获取菜谱详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取菜谱详情失败'
    });
  }
});

// 智能推荐菜谱
router.get('/recommend/list', authenticateToken, async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    // 获取用户的食材
    const userIngredients = await query(`
      SELECT DISTINCT name 
      FROM user_ingredients 
      WHERE user_id = ? AND is_deleted = 0 AND status IN (1, 2)
    `, [req.user.id]);

    if (userIngredients.length === 0) {
      // 如果用户没有食材，返回热门菜谱
      const popularRecipes = await query(`
        SELECT 
          r.*,
          CASE WHEN uf.recipe_id IS NOT NULL THEN 1 ELSE 0 END as is_favorited
        FROM recipes r
        LEFT JOIN user_favorites uf ON r.id = uf.recipe_id AND uf.user_id = ?
        WHERE r.is_public = 1 AND r.is_deleted = 0
        ORDER BY r.like_count DESC, r.view_count DESC
        LIMIT ?
      `, [req.user.id, parseInt(limit)]);

      // 解析JSON字段
      popularRecipes.forEach(recipe => {
        try {
          recipe.ingredients = recipe.ingredients ? JSON.parse(recipe.ingredients) : [];
          recipe.steps = recipe.steps ? JSON.parse(recipe.steps) : [];
        } catch (error) {
          recipe.ingredients = [];
          recipe.steps = [];
        }
      });

      return res.json({
        success: true,
        data: {
          recipes: popularRecipes,
          recommendation_type: 'popular'
        }
      });
    }

    // 基于用户食材推荐菜谱
    const ingredientNames = userIngredients.map(item => item.name);
    const ingredientConditions = ingredientNames.map(() => 'JSON_SEARCH(ingredients, "one", ?) IS NOT NULL').join(' OR ');

    const recommendedRecipes = await query(`
      SELECT 
        r.*,
        CASE WHEN uf.recipe_id IS NOT NULL THEN 1 ELSE 0 END as is_favorited
      FROM recipes r
      LEFT JOIN user_favorites uf ON r.id = uf.recipe_id AND uf.user_id = ?
      WHERE r.is_public = 1 AND r.is_deleted = 0 AND (${ingredientConditions})
      ORDER BY r.like_count DESC, r.view_count DESC
      LIMIT ?
    `, [req.user.id, ...ingredientNames, parseInt(limit)]);

    // 解析JSON字段并计算匹配度
    recommendedRecipes.forEach(recipe => {
      try {
        recipe.ingredients = recipe.ingredients ? JSON.parse(recipe.ingredients) : [];
        recipe.steps = recipe.steps ? JSON.parse(recipe.steps) : [];
        
        // 计算食材匹配度
        const recipeIngredients = recipe.ingredients.map(item => item.name);
        const matchedIngredients = recipeIngredients.filter(ingredient => 
          ingredientNames.some(userIngredient => 
            userIngredient.includes(ingredient) || ingredient.includes(userIngredient)
          )
        );
        recipe.match_rate = Math.round((matchedIngredients.length / recipeIngredients.length) * 100);
        recipe.matched_ingredients = matchedIngredients;
      } catch (error) {
        recipe.ingredients = [];
        recipe.steps = [];
        recipe.match_rate = 0;
        recipe.matched_ingredients = [];
      }
    });

    // 按匹配度排序
    recommendedRecipes.sort((a, b) => b.match_rate - a.match_rate);

    res.json({
      success: true,
      data: {
        recipes: recommendedRecipes,
        recommendation_type: 'ingredient_based',
        user_ingredients: ingredientNames
      }
    });
  } catch (error) {
    console.error('智能推荐菜谱错误:', error);
    res.status(500).json({
      success: false,
      message: '智能推荐失败'
    });
  }
});

// 获取菜谱分类
router.get('/categories/list', async (req, res) => {
  try {
    const categories = await query(`
      SELECT 
        category,
        COUNT(*) as count
      FROM recipes 
      WHERE is_public = 1 AND is_deleted = 0 AND category IS NOT NULL
      GROUP BY category
      ORDER BY count DESC
    `);

    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('获取菜谱分类错误:', error);
    res.status(500).json({
      success: false,
      message: '获取菜谱分类失败'
    });
  }
});

// 搜索菜谱
router.get('/search/advanced', optionalAuth, async (req, res) => {
  try {
    const { 
      keyword, 
      ingredients, 
      category, 
      difficulty, 
      max_time, 
      page = 1, 
      limit = 20 
    } = req.query;
    
    const offset = (page - 1) * limit;
    let whereConditions = ['is_public = 1', 'is_deleted = 0'];
    let queryParams = [];

    // 关键词搜索
    if (keyword) {
      whereConditions.push('(name LIKE ? OR description LIKE ? OR tips LIKE ?)');
      queryParams.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }

    // 食材搜索
    if (ingredients) {
      const ingredientList = ingredients.split(',').map(item => item.trim());
      const ingredientConditions = ingredientList.map(() => 'JSON_SEARCH(ingredients, "one", ?) IS NOT NULL').join(' AND ');
      whereConditions.push(`(${ingredientConditions})`);
      queryParams.push(...ingredientList);
    }

    // 分类筛选
    if (category) {
      whereConditions.push('category = ?');
      queryParams.push(category);
    }

    // 难度筛选
    if (difficulty) {
      whereConditions.push('difficulty = ?');
      queryParams.push(difficulty);
    }

    // 制作时间筛选
    if (max_time) {
      whereConditions.push('cook_time <= ?');
      queryParams.push(parseInt(max_time));
    }

    const whereClause = whereConditions.join(' AND ');

    const recipes = await query(`
      SELECT 
        r.*,
        CASE WHEN uf.recipe_id IS NOT NULL THEN 1 ELSE 0 END as is_favorited
      FROM recipes r
      LEFT JOIN user_favorites uf ON r.id = uf.recipe_id AND uf.user_id = ?
      WHERE ${whereClause}
      ORDER BY r.like_count DESC, r.view_count DESC
      LIMIT ? OFFSET ?
    `, [req.user?.id || null, ...queryParams, parseInt(limit), offset]);

    // 解析JSON字段
    recipes.forEach(recipe => {
      try {
        recipe.ingredients = recipe.ingredients ? JSON.parse(recipe.ingredients) : [];
        recipe.steps = recipe.steps ? JSON.parse(recipe.steps) : [];
      } catch (error) {
        recipe.ingredients = [];
        recipe.steps = [];
      }
    });

    // 获取总数
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM recipes
      WHERE ${whereClause}
    `, queryParams);

    const total = countResult[0].total;

    res.json({
      success: true,
      data: {
        recipes,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('高级搜索菜谱错误:', error);
    res.status(500).json({
      success: false,
      message: '搜索失败'
    });
  }
});

// 点赞菜谱
router.post('/:id/like', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // 检查菜谱是否存在
    const recipes = await query(
      'SELECT id, like_count FROM recipes WHERE id = ? AND is_public = 1 AND is_deleted = 0',
      [id]
    );

    if (recipes.length === 0) {
      return res.status(404).json({
        success: false,
        message: '菜谱不存在'
      });
    }

    // 增加点赞数
    await query(
      'UPDATE recipes SET like_count = like_count + 1 WHERE id = ?',
      [id]
    );

    // 记录用户操作
    await query(
      'INSERT INTO user_records (user_id, type, content) VALUES (?, ?, ?)',
      [
        req.user.id,
        'recipe_like',
        JSON.stringify({
          recipe_id: id,
          action: 'like'
        })
      ]
    );

    res.json({
      success: true,
      message: '点赞成功'
    });
  } catch (error) {
    console.error('点赞菜谱错误:', error);
    res.status(500).json({
      success: false,
      message: '点赞失败'
    });
  }
});

// 获取热门菜谱
router.get('/popular/list', optionalAuth, async (req, res) => {
  try {
    const { limit = 10, period = 'all' } = req.query;
    
    let dateCondition = '';
    const queryParams = [req.user?.id || null];

    // 根据时间段筛选
    switch (period) {
      case 'week':
        dateCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        break;
      case 'month':
        dateCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
        break;
      case 'year':
        dateCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 365 DAY)';
        break;
      default:
        dateCondition = '';
    }

    const popularRecipes = await query(`
      SELECT 
        r.*,
        CASE WHEN uf.recipe_id IS NOT NULL THEN 1 ELSE 0 END as is_favorited,
        (r.like_count * 0.7 + r.view_count * 0.3) as popularity_score
      FROM recipes r
      LEFT JOIN user_favorites uf ON r.id = uf.recipe_id AND uf.user_id = ?
      WHERE r.is_public = 1 AND r.is_deleted = 0 ${dateCondition}
      ORDER BY popularity_score DESC
      LIMIT ?
    `, [...queryParams, parseInt(limit)]);

    // 解析JSON字段
    popularRecipes.forEach(recipe => {
      try {
        recipe.ingredients = recipe.ingredients ? JSON.parse(recipe.ingredients) : [];
        recipe.steps = recipe.steps ? JSON.parse(recipe.steps) : [];
      } catch (error) {
        recipe.ingredients = [];
        recipe.steps = [];
      }
    });

    res.json({
      success: true,
      data: popularRecipes
    });
  } catch (error) {
    console.error('获取热门菜谱错误:', error);
    res.status(500).json({
      success: false,
      message: '获取热门菜谱失败'
    });
  }
});

// 根据食材推荐菜谱
router.post('/recommend/by-ingredients', async (req, res) => {
  try {
    const { ingredients, limit = 10 } = req.body;

    if (!ingredients || !Array.isArray(ingredients) || ingredients.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供食材列表'
      });
    }

    // 构建搜索条件
    const ingredientConditions = ingredients.map(() => 'JSON_SEARCH(ingredients, "one", ?) IS NOT NULL').join(' OR ');

    const recommendedRecipes = await query(`
      SELECT *
      FROM recipes
      WHERE is_public = 1 AND is_deleted = 0 AND (${ingredientConditions})
      ORDER BY like_count DESC, view_count DESC
      LIMIT ?
    `, [...ingredients, parseInt(limit)]);

    // 解析JSON字段并计算匹配度
    recommendedRecipes.forEach(recipe => {
      try {
        recipe.ingredients = recipe.ingredients ? JSON.parse(recipe.ingredients) : [];
        recipe.steps = recipe.steps ? JSON.parse(recipe.steps) : [];
        
        // 计算食材匹配度
        const recipeIngredients = recipe.ingredients.map(item => item.name);
        const matchedIngredients = recipeIngredients.filter(ingredient => 
          ingredients.some(userIngredient => 
            userIngredient.includes(ingredient) || ingredient.includes(userIngredient)
          )
        );
        recipe.match_rate = Math.round((matchedIngredients.length / recipeIngredients.length) * 100);
        recipe.matched_ingredients = matchedIngredients;
        recipe.missing_ingredients = recipeIngredients.filter(ingredient => 
          !ingredients.some(userIngredient => 
            userIngredient.includes(ingredient) || ingredient.includes(userIngredient)
          )
        );
      } catch (error) {
        recipe.ingredients = [];
        recipe.steps = [];
        recipe.match_rate = 0;
        recipe.matched_ingredients = [];
        recipe.missing_ingredients = [];
      }
    });

    // 按匹配度排序
    recommendedRecipes.sort((a, b) => b.match_rate - a.match_rate);

    res.json({
      success: true,
      data: {
        recipes: recommendedRecipes,
        search_ingredients: ingredients
      }
    });
  } catch (error) {
    console.error('根据食材推荐菜谱错误:', error);
    res.status(500).json({
      success: false,
      message: '推荐失败'
    });
  }
});

module.exports = router;