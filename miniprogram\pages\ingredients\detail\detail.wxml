<!--食材详情页面-->
<view class="page-container" wx:if="{{!loading}}">
  <!-- 食材图片和基本信息 -->
  <view class="ingredient-header">
    <view class="ingredient-image" bindtap="previewImage">
      <image
        src="{{ingredient.image_url || 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop&crop=center'}}"
        mode="aspectFill"
        class="ingredient-img"
        binderror="onImageError"
      ></image>
      <view class="status-badge status-{{statusInfo.status}}">
        {{statusInfo.text}}
      </view>
    </view>
    
    <view class="ingredient-info">
      <text class="ingredient-name">{{ingredient.name}}</text>
      <text class="ingredient-category">{{ingredient.category}}</text>
      <view class="ingredient-quantity">
        <text class="quantity-text">{{ingredient.quantity}} {{ingredient.unit}}</text>
      </view>
    </view>

    <view class="header-actions">
      <view class="action-btn" bindtap="showActionSheet">
        <text class="emoji-icon">⋯</text>
      </view>
    </view>
  </view>

  <!-- 详细信息 -->
  <view class="detail-section">
    <view class="section-title">详细信息</view>
    
    <view class="detail-list">
      <view class="detail-item">
        <view class="detail-label">
          <text class="detail-icon emoji-icon">📅</text>
          <text>过期时间</text>
        </view>
        <text class="detail-value expire-date" style="color: {{statusInfo.color}}">
          {{ingredient.expire_date}}
        </text>
      </view>

      <view class="detail-item" wx:if="{{ingredient.purchase_date}}">
        <view class="detail-label">
          <text class="detail-icon emoji-icon">🛒</text>
          <text>购买时间</text>
        </view>
        <text class="detail-value">{{ingredient.purchase_date}}</text>
      </view>

      <view class="detail-item" wx:if="{{ingredient.location}}">
        <view class="detail-label">
          <image src="/images/icons/location.png" mode="aspectFit" class="detail-icon"></image>
          <text>存放位置</text>
        </view>
        <text class="detail-value">{{ingredient.location}}</text>
      </view>

      <view class="detail-item">
        <view class="detail-label">
          <text class="detail-icon emoji-icon">⏰</text>
          <text>添加时间</text>
        </view>
        <text class="detail-value">{{ingredient.created_at}}</text>
      </view>

      <view class="detail-item" wx:if="{{ingredient.notes}}">
        <view class="detail-label">
          <text class="detail-icon emoji-icon">📝</text>
          <text>备注信息</text>
        </view>
        <text class="detail-value notes-text">{{ingredient.notes}}</text>
      </view>
    </view>
  </view>

  <!-- 营养信息 -->
  <view class="nutrition-section" wx:if="{{ingredient.nutrition}}">
    <view class="section-title">营养信息</view>
    <view class="nutrition-grid">
      <view class="nutrition-item">
        <text class="nutrition-value">{{ingredient.nutrition.calories || '--'}}</text>
        <text class="nutrition-label">卡路里</text>
      </view>
      <view class="nutrition-item">
        <text class="nutrition-value">{{ingredient.nutrition.protein || '--'}}</text>
        <text class="nutrition-label">蛋白质(g)</text>
      </view>
      <view class="nutrition-item">
        <text class="nutrition-value">{{ingredient.nutrition.fat || '--'}}</text>
        <text class="nutrition-label">脂肪(g)</text>
      </view>
      <view class="nutrition-item">
        <text class="nutrition-value">{{ingredient.nutrition.carbs || '--'}}</text>
        <text class="nutrition-label">碳水(g)</text>
      </view>
    </view>
  </view>

  <!-- 相关菜谱 -->
  <view class="recipes-section" wx:if="{{relatedRecipes.length > 0}}">
    <view class="section-header">
      <text class="section-title">相关菜谱</text>
      <text class="section-more">查看更多</text>
    </view>
    
    <scroll-view class="recipes-scroll" scroll-x="true" show-scrollbar="false">
      <view class="recipes-list">
        <view 
          class="recipe-item"
          wx:for="{{relatedRecipes}}"
          wx:key="id"
          data-id="{{item.id}}"
          bindtap="viewRecipeDetail"
        >
          <image src="{{item.image_url || '/images/default-recipe.png'}}" mode="aspectFill" class="recipe-img"></image>
          <text class="recipe-name">{{item.name}}</text>
          <text class="recipe-time">{{item.cook_time}}分钟</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="action-grid">
      <view class="quick-action-item" bindtap="addToShoppingList">
        <text class="action-icon emoji-icon">🛒</text>
        <text class="action-text">加入购物清单</text>
      </view>

      <view class="quick-action-item" bindtap="setReminder">
        <text class="action-icon emoji-icon">🔔</text>
        <text class="action-text">设置提醒</text>
      </view>

      <view class="quick-action-item" bindtap="copyIngredientInfo">
        <text class="action-icon emoji-icon">📋</text>
        <text class="action-text">复制信息</text>
      </view>

      <view class="quick-action-item" bindtap="viewHistory">
        <text class="action-icon emoji-icon">📜</text>
        <text class="action-text">查看记录</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="bottom-actions">
    <button class="btn btn-secondary btn-half" bindtap="editIngredient">
      编辑食材
    </button>
    <button class="btn btn-primary btn-half" open-type="share">
      分享食材
    </button>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-spinner"></view>
  <text class="loading-text">加载中...</text>
</view>