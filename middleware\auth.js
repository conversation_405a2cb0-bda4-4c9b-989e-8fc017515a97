const jwt = require('jsonwebtoken');
const { query } = require('../config/database');

// JWT认证中间件
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    // 开发环境下允许无token访问，使用默认用户
    if (!token) {
      if (process.env.NODE_ENV === 'development') {
        console.log('开发环境：使用默认用户');
        req.user = {
          id: 1,
          phone: 'dev_user',
          nickname: '开发用户',
          avatar: null,
          status: 1
        };
        return next();
      }
      
      return res.status(401).json({
        success: false,
        message: '访问令牌缺失'
      });
    }

    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default_secret');
    
    // 检查用户是否存在
    const users = await query(
      'SELECT id, phone, nickname, avatar, status FROM users WHERE id = ? AND is_deleted = 0',
      [decoded.userId]
    );

    if (users.length === 0) {
      // 开发环境下创建默认用户
      if (process.env.NODE_ENV === 'development') {
        req.user = {
          id: decoded.userId || 1,
          phone: 'dev_user',
          nickname: '开发用户',
          avatar: null,
          status: 1
        };
        return next();
      }
      
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    const user = users[0];

    // 检查用户状态
    if (!user.status) {
      return res.status(403).json({
        success: false,
        message: '用户账号已被禁用'
      });
    }

    // 将用户信息添加到请求对象
    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      // 开发环境下使用默认用户
      if (process.env.NODE_ENV === 'development') {
        req.user = {
          id: 1,
          phone: 'dev_user',
          nickname: '开发用户',
          avatar: null,
          status: 1
        };
        return next();
      }
      
      return res.status(401).json({
        success: false,
        message: '无效的访问令牌'
      });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '访问令牌已过期'
      });
    } else {
      console.error('认证中间件错误:', error);
      return res.status(500).json({
        success: false,
        message: '认证失败'
      });
    }
  }
};

// 生成JWT token
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

// 可选认证中间件（不强制要求登录）
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const users = await query(
        'SELECT id, phone, nickname, avatar, status FROM users WHERE id = ? AND is_deleted = 0',
        [decoded.userId]
      );

      if (users.length > 0 && users[0].status) {
        req.user = users[0];
      }
    }
    
    next();
  } catch (error) {
    // 可选认证失败时不返回错误，继续执行
    next();
  }
};

module.exports = {
  authenticateToken,
  generateToken,
  optionalAuth
};