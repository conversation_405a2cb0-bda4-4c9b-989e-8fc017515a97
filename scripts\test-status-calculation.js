// 测试过期时间计算逻辑
function getIngredientStatus(expireDate) {
  if (!expireDate) {
    return { 
      status: 'fresh', 
      text: '无过期日期', 
      color: '#718096',
      days: undefined 
    };
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0); // 设置为当天开始时间
  
  const expire = new Date(expireDate);
  expire.setHours(0, 0, 0, 0); // 设置为过期日期开始时间
  
  const diffDays = Math.ceil((expire - today) / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) {
    return { 
      status: 'expired', 
      text: `已过期${Math.abs(diffDays)}天`, 
      color: '#e53e3e',
      days: diffDays 
    };
  } else if (diffDays === 0) {
    return { 
      status: 'expiring', 
      text: '今天过期', 
      color: '#d69e2e',
      days: diffDays 
    };
  } else if (diffDays <= 3) {
    return { 
      status: 'expiring', 
      text: `${diffDays}天后过期`, 
      color: '#d69e2e',
      days: diffDays 
    };
  } else {
    return { 
      status: 'fresh', 
      text: `${diffDays}天后过期`, 
      color: '#38a169',
      days: diffDays 
    };
  }
}

// 测试用例
function runTests() {
  console.log('🧪 开始测试过期时间计算逻辑...\n');
  
  const today = new Date();
  const testCases = [
    {
      name: '无过期日期',
      expireDate: null,
      expected: { status: 'fresh', text: '无过期日期' }
    },
    {
      name: '已过期3天',
      expireDate: new Date(today.getTime() - 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      expected: { status: 'expired', text: '已过期3天' }
    },
    {
      name: '已过期1天',
      expireDate: new Date(today.getTime() - 1 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      expected: { status: 'expired', text: '已过期1天' }
    },
    {
      name: '今天过期',
      expireDate: today.toISOString().split('T')[0],
      expected: { status: 'expiring', text: '今天过期' }
    },
    {
      name: '1天后过期',
      expireDate: new Date(today.getTime() + 1 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      expected: { status: 'expiring', text: '1天后过期' }
    },
    {
      name: '3天后过期',
      expireDate: new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      expected: { status: 'expiring', text: '3天后过期' }
    },
    {
      name: '7天后过期',
      expireDate: new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      expected: { status: 'fresh', text: '7天后过期' }
    },
    {
      name: '30天后过期',
      expireDate: new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      expected: { status: 'fresh', text: '30天后过期' }
    }
  ];
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  testCases.forEach((testCase, index) => {
    const result = getIngredientStatus(testCase.expireDate);
    const passed = result.status === testCase.expected.status && result.text === testCase.expected.text;
    
    console.log(`${passed ? '✅' : '❌'} 测试 ${index + 1}: ${testCase.name}`);
    console.log(`   输入: ${testCase.expireDate || 'null'}`);
    console.log(`   期望: ${testCase.expected.status} - ${testCase.expected.text}`);
    console.log(`   实际: ${result.status} - ${result.text}`);
    console.log(`   天数: ${result.days}`);
    console.log('');
    
    if (passed) passedTests++;
  });
  
  console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！过期时间计算逻辑正确。');
  } else {
    console.log('⚠️  部分测试失败，需要检查逻辑。');
  }
}

// 测试图片URL生成
function testImageUrls() {
  console.log('\n🖼️  测试图片URL生成...\n');
  
  const categories = ['蔬菜', '水果', '肉类', '海鲜', '蛋奶', '调料', '零食', '饮品', '其他'];
  const defaultImages = [
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1542838132-92c53300491e?w=200&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=200&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=200&h=200&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=200&h=200&fit=crop&crop=center'
  ];
  
  categories.forEach((category, index) => {
    console.log(`${category}: 使用分类默认图片`);
  });
  
  console.log('\n默认图片池:');
  defaultImages.forEach((url, index) => {
    console.log(`${index + 1}. ${url}`);
  });
  
  console.log('\n✅ 图片URL配置正确');
}

// 运行所有测试
if (require.main === module) {
  runTests();
  testImageUrls();
}

module.exports = { getIngredientStatus, runTests, testImageUrls };
