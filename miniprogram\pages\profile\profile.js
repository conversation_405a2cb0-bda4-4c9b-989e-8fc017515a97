// 个人中心页面逻辑
const app = getApp();

Page({
  data: {
    userInfo: null,
    stats: {
      ingredientsCount: 0,
      recipesCount: 0,
      shoppingCount: 0,
      favoritesCount: 0
    },
    settings: {
      notifications: true,
      darkMode: false,
      autoSync: true,
      language: 'zh-CN'
    },
    loading: false
  },

  onLoad() {
    console.log('个人中心页面加载');
    this.loadUserData();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.userInfo) {
      wx.redirectTo({
        url: '/pages/login/login'
      });
      return;
    }
    this.loadUserData();
  },

  // 加载用户数据
  async loadUserData() {
    this.setData({ loading: true });

    try {
      // 获取用户信息
      const userRes = await app.request({
        url: '/users/profile'
      });

      // 获取统计数据
      const statsRes = await app.request({
        url: '/users/stats'
      });

      // 获取设置
      const settingsRes = await app.request({
        url: '/users/settings'
      });

      this.setData({
        userInfo: userRes.data,
        stats: statsRes.data,
        settings: settingsRes.data || this.data.settings,
        loading: false
      });

    } catch (error) {
      console.error('加载用户数据失败:', error);
      this.setData({ loading: false });
    }
  },

  // 编辑个人信息
  editProfile() {
    wx.navigateTo({
      url: '/pages/profile/edit/edit'
    });
  },

  // 查看我的食材
  viewMyIngredients() {
    wx.switchTab({
      url: '/pages/ingredients/list/list'
    });
  },

  // 查看我的菜谱
  viewMyRecipes() {
    wx.navigateTo({
      url: '/pages/recipes/my/my'
    });
  },

  // 查看购物清单
  viewShoppingList() {
    wx.switchTab({
      url: '/pages/shopping/list/list'
    });
  },

  // 查看收藏
  viewFavorites() {
    wx.navigateTo({
      url: '/pages/favorites/list/list'
    });
  },

  // 数据分析
  viewAnalysis() {
    wx.navigateTo({
      url: '/pages/analysis/analysis'
    });
  },

  // 设置通知
  async toggleNotifications(e) {
    const enabled = e.detail.value;
    
    try {
      await app.request({
        url: '/users/settings',
        method: 'PUT',
        data: {
          notifications: enabled
        }
      });

      this.setData({
        'settings.notifications': enabled
      });

      if (enabled) {
        // 请求通知权限
        wx.requestSubscribeMessage({
          tmplIds: ['notification_template_id'],
          success: (res) => {
            console.log('通知权限请求成功:', res);
          }
        });
      }

    } catch (error) {
      console.error('设置通知失败:', error);
      app.showError('设置失败');
    }
  },

  // 切换深色模式
  async toggleDarkMode(e) {
    const enabled = e.detail.value;
    
    try {
      await app.request({
        url: '/users/settings',
        method: 'PUT',
        data: {
          darkMode: enabled
        }
      });

      this.setData({
        'settings.darkMode': enabled
      });

      // 应用主题
      if (enabled) {
        wx.setBackgroundColor({
          backgroundColor: '#1a1a1a',
          backgroundColorTop: '#1a1a1a',
          backgroundColorBottom: '#1a1a1a'
        });
      } else {
        wx.setBackgroundColor({
          backgroundColor: '#f5f5f5',
          backgroundColorTop: '#f5f5f5',
          backgroundColorBottom: '#f5f5f5'
        });
      }

    } catch (error) {
      console.error('设置深色模式失败:', error);
      app.showError('设置失败');
    }
  },

  // 切换自动同步
  async toggleAutoSync(e) {
    const enabled = e.detail.value;
    
    try {
      await app.request({
        url: '/users/settings',
        method: 'PUT',
        data: {
          autoSync: enabled
        }
      });

      this.setData({
        'settings.autoSync': enabled
      });

    } catch (error) {
      console.error('设置自动同步失败:', error);
      app.showError('设置失败');
    }
  },

  // 数据备份
  async backupData() {
    wx.showLoading({ title: '备份中...' });

    try {
      const res = await app.request({
        url: '/users/backup',
        method: 'POST'
      });

      wx.hideLoading();
      app.showSuccess('备份成功');

      // 显示备份信息
      wx.showModal({
        title: '备份完成',
        content: `备份文件已生成，备份ID: ${res.data.backupId}`,
        showCancel: false
      });

    } catch (error) {
      wx.hideLoading();
      console.error('数据备份失败:', error);
      app.showError('备份失败');
    }
  },

  // 数据恢复
  restoreData() {
    wx.showActionSheet({
      itemList: ['从备份恢复', '从文件恢复'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.restoreFromBackup();
        } else {
          this.restoreFromFile();
        }
      }
    });
  },

  // 从备份恢复
  async restoreFromBackup() {
    // 获取备份列表
    try {
      const res = await app.request({
        url: '/users/backups'
      });

      const backups = res.data.backups || [];
      if (backups.length === 0) {
        app.showError('没有找到备份文件');
        return;
      }

      const itemList = backups.map(backup => 
        `${backup.created_at} (${backup.size})`
      );

      wx.showActionSheet({
        itemList,
        success: async (res) => {
          const backup = backups[res.tapIndex];
          await this.performRestore(backup.id);
        }
      });

    } catch (error) {
      console.error('获取备份列表失败:', error);
      app.showError('获取备份失败');
    }
  },

  // 从文件恢复
  restoreFromFile() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      success: async (res) => {
        const file = res.tempFiles[0];
        
        wx.showLoading({ title: '恢复中...' });

        try {
          const uploadRes = await wx.uploadFile({
            url: `${app.globalData.apiBase}/users/restore`,
            filePath: file.path,
            name: 'backup',
            header: {
              'Authorization': `Bearer ${app.globalData.token}`
            }
          });

          wx.hideLoading();
          
          const result = JSON.parse(uploadRes.data);
          if (result.success) {
            app.showSuccess('恢复成功');
            this.loadUserData();
          } else {
            app.showError(result.message || '恢复失败');
          }

        } catch (error) {
          wx.hideLoading();
          console.error('文件恢复失败:', error);
          app.showError('恢复失败');
        }
      }
    });
  },

  // 执行恢复
  async performRestore(backupId) {
    const confirmed = await app.showConfirm({
      title: '确认恢复',
      content: '恢复数据将覆盖当前所有数据，确定要继续吗？'
    });

    if (!confirmed) return;

    wx.showLoading({ title: '恢复中...' });

    try {
      await app.request({
        url: `/users/restore/${backupId}`,
        method: 'POST'
      });

      wx.hideLoading();
      app.showSuccess('恢复成功');
      this.loadUserData();

    } catch (error) {
      wx.hideLoading();
      console.error('数据恢复失败:', error);
      app.showError('恢复失败');
    }
  },

  // 清空数据
  async clearAllData() {
    const confirmed = await app.showConfirm({
      title: '危险操作',
      content: '此操作将清空所有数据且不可恢复，确定要继续吗？'
    });

    if (!confirmed) return;

    // 二次确认
    const doubleConfirmed = await app.showConfirm({
      title: '最后确认',
      content: '请再次确认要清空所有数据'
    });

    if (!doubleConfirmed) return;

    wx.showLoading({ title: '清空中...' });

    try {
      await app.request({
        url: '/users/clear',
        method: 'DELETE'
      });

      wx.hideLoading();
      app.showSuccess('清空成功');
      this.loadUserData();

    } catch (error) {
      wx.hideLoading();
      console.error('清空数据失败:', error);
      app.showError('清空失败');
    }
  },

  // 关于我们
  showAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    });
  },

  // 帮助中心
  showHelp() {
    wx.navigateTo({
      url: '/pages/help/help'
    });
  },

  // 意见反馈
  showFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  },

  // 隐私政策
  showPrivacy() {
    wx.navigateTo({
      url: '/pages/privacy/privacy'
    });
  },

  // 检查更新
  checkUpdate() {
    const updateManager = wx.getUpdateManager();

    updateManager.onCheckForUpdate((res) => {
      if (res.hasUpdate) {
        wx.showModal({
          title: '发现新版本',
          content: '发现新版本，是否立即更新？',
          success: (res) => {
            if (res.confirm) {
              updateManager.onUpdateReady(() => {
                updateManager.applyUpdate();
              });
            }
          }
        });
      } else {
        app.showSuccess('已是最新版本');
      }
    });

    updateManager.onUpdateFailed(() => {
      app.showError('更新失败');
    });
  },

  // 退出登录
  async logout() {
    const confirmed = await app.showConfirm({
      title: '确认退出',
      content: '确定要退出登录吗？'
    });

    if (!confirmed) return;

    try {
      await app.request({
        url: '/users/logout',
        method: 'POST'
      });

      // 清除本地数据
      app.globalData.userInfo = null;
      app.globalData.token = null;
      wx.removeStorageSync('token');
      wx.removeStorageSync('userInfo');

      app.showSuccess('已退出登录');

      // 跳转到登录页
      wx.redirectTo({
        url: '/pages/login/login'
      });

    } catch (error) {
      console.error('退出登录失败:', error);
      app.showError('退出失败');
    }
  },

  // 分享给好友
  onShareAppMessage() {
    return {
      title: '智能冰箱食材管理助手',
      desc: '让食材管理更智能，让生活更便捷',
      path: '/pages/index/index',
      imageUrl: '/images/share-app.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '智能冰箱食材管理助手',
      query: '',
      imageUrl: '/images/share-app.png'
    };
  },

  // 图片加载错误处理
  handleImageError(e) {
    const target = e.target || e.currentTarget;
    if (target) {
      target.src = '/images/tab/default-avatar.png';
    }
  }
});