<!-- 菜谱详情页面 -->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="content-container">
    <!-- 菜谱头部信息 -->
    <view class="recipe-header">
      <image class="recipe-image" src="{{recipe.image}}" mode="aspectFill" bindtap="previewImage" data-url="{{recipe.image}}"></image>
      
      <view class="recipe-overlay">
        <view class="recipe-info">
          <text class="recipe-name">{{recipe.name}}</text>
          <text class="recipe-description">{{recipe.description}}</text>
          
          <view class="recipe-meta">
            <view class="meta-item">
              <image class="meta-icon" src="⏰"></image>
              <text class="meta-text">{{recipe.cookTime}}分钟</text>
            </view>
            <view class="meta-item">
              <image class="meta-icon" src="/images/icons/difficulty.png"></image>
              <text class="meta-text">{{getDifficultyName(recipe.difficulty)}}</text>
            </view>
            <view class="meta-item">
              <image class="meta-icon" src="/images/icons/servings.png"></image>
              <text class="meta-text">{{recipe.servings}}人份</text>
            </view>
          </view>
          
          <view class="recipe-tags">
            <text wx:for="{{recipe.tags}}" wx:key="*this" class="tag-item">{{item}}</text>
          </view>
        </view>
        
        <view class="header-actions">
          <button class="action-btn favorite {{isFavorited ? 'active' : ''}}" bindtap="toggleFavorite">
            <image class="action-icon" src="/images/icons/{{isFavorited ? 'heart-filled' : 'heart'}}.png"></image>
          </button>
          <button class="action-btn share" bindtap="showShareModal">
            <image class="action-icon" src="📤"></image>
          </button>
          <button wx:if="{{recipe.isOwner}}" class="action-btn edit" bindtap="editRecipe">
            <image class="action-icon" src="✏️"></image>
          </button>
        </view>
      </view>
    </view>

    <!-- 快速操作栏 -->
    <view class="quick-actions">
      <button class="quick-btn primary" bindtap="startCooking" wx:if="{{!cookingMode}}">
        <image class="quick-icon" src="/images/icons/cook.png"></image>
        <text class="quick-text">开始制作</text>
      </button>
      <button class="quick-btn danger" bindtap="exitCooking" wx:else>
        <image class="quick-icon" src="/images/icons/stop.png"></image>
        <text class="quick-text">退出制作</text>
      </button>
      
      <button class="quick-btn" bindtap="checkIngredients">
        <image class="quick-icon" src="✅"></image>
        <text class="quick-text">检查食材</text>
      </button>
      
      <button class="quick-btn" bindtap="addToShoppingList">
        <image class="quick-icon" src="/images/icons/cart.png"></image>
        <text class="quick-text">加入清单</text>
      </button>
    </view>

    <!-- 制作模式导航 -->
    <view wx:if="{{cookingMode}}" class="cooking-nav">
      <button class="nav-btn" bindtap="prevStep" disabled="{{currentStep === 0}}">
        <image class="nav-icon" src="/images/icons/prev.png"></image>
        <text class="nav-text">上一步</text>
      </button>
      
      <view class="step-indicator">
        <text class="step-current">{{currentStep + 1}}</text>
        <text class="step-total">/ {{recipe.steps.length}}</text>
      </view>
      
      <button class="nav-btn" bindtap="nextStep" disabled="{{currentStep === recipe.steps.length - 1}}">
        <text class="nav-text">下一步</text>
        <image class="nav-icon" src="/images/icons/next.png"></image>
      </button>
    </view>

    <!-- 标签页导航 -->
    <view wx:if="{{!cookingMode}}" class="tab-nav">
      <view wx:for="{{tabs}}" wx:key="id" class="tab-item {{currentTab === item.id ? 'active' : ''}}" bindtap="switchTab" data-tab="{{item.id}}">
        <image class="tab-icon" src="{{item.icon}}"></image>
        <text class="tab-text">{{item.name}}</text>
      </view>
    </view>

    <!-- 标签页内容 -->
    <view class="tab-content">
      <!-- 食材列表 -->
      <view wx:if="{{currentTab === 0 && !cookingMode}}" class="ingredients-content">
        <view class="ingredients-list">
          <view wx:for="{{recipe.ingredients}}" wx:key="id" class="ingredient-item">
            <view class="ingredient-info">
              <text class="ingredient-name">{{item.name}}</text>
              <text class="ingredient-amount">{{item.amount}} {{item.unit}}</text>
            </view>
            <view class="ingredient-status">
              <image wx:if="{{item.available}}" class="status-icon" src="/images/icons/check-green.png"></image>
              <image wx:else class="status-icon" src="/images/icons/close-red.png"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 制作步骤 -->
      <view wx:if="{{currentTab === 1 && !cookingMode}}" class="steps-content">
        <view class="steps-list">
          <view wx:for="{{recipe.steps}}" wx:key="index" class="step-item">
            <view class="step-number">{{index + 1}}</view>
            <view class="step-content">
              <text class="step-description">{{item.description}}</text>
              <image wx:if="{{item.image}}" class="step-image" src="{{item.image}}" mode="aspectFill" bindtap="previewImage" data-url="{{item.image}}"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 制作模式步骤 -->
      <view wx:if="{{cookingMode}}" class="cooking-content">
        <view class="current-step">
          <view class="step-header">
            <text class="step-title">步骤 {{currentStep + 1}}</text>
            <button class="complete-btn {{completedSteps.indexOf(currentStep) !== -1 ? 'completed' : ''}}" bindtap="completeStep" data-index="{{currentStep}}">
              <image class="complete-icon" src="/images/icons/{{completedSteps.indexOf(currentStep) !== -1 ? 'check-white' : 'check'}}.png"></image>
              <text class="complete-text">{{completedSteps.indexOf(currentStep) !== -1 ? '已完成' : '完成'}}</text>
            </button>
          </view>
          
          <view class="step-detail">
            <text class="step-description">{{recipe.steps[currentStep].description}}</text>
            <image wx:if="{{recipe.steps[currentStep].image}}" class="step-image" src="{{recipe.steps[currentStep].image}}" mode="aspectFill" bindtap="previewImage" data-url="{{recipe.steps[currentStep].image}}"></image>
          </view>
        </view>
        
        <!-- 步骤进度 -->
        <view class="steps-progress">
          <view wx:for="{{recipe.steps}}" wx:key="index" class="progress-dot {{completedSteps.indexOf(index) !== -1 ? 'completed' : ''}} {{currentStep === index ? 'current' : ''}}"></view>
        </view>
      </view>

      <!-- 营养信息 -->
      <view wx:if="{{currentTab === 2 && !cookingMode}}" class="nutrition-content">
        <view class="nutrition-summary">
          <view class="nutrition-item">
            <text class="nutrition-label">热量</text>
            <text class="nutrition-value">{{formatNutritionValue(recipe.nutrition.calories, 'kcal')}}</text>
          </view>
          <view class="nutrition-item">
            <text class="nutrition-label">蛋白质</text>
            <text class="nutrition-value">{{formatNutritionValue(recipe.nutrition.protein)}}</text>
          </view>
          <view class="nutrition-item">
            <text class="nutrition-label">脂肪</text>
            <text class="nutrition-value">{{formatNutritionValue(recipe.nutrition.fat)}}</text>
          </view>
          <view class="nutrition-item">
            <text class="nutrition-label">碳水</text>
            <text class="nutrition-value">{{formatNutritionValue(recipe.nutrition.carbs)}}</text>
          </view>
        </view>
        
        <button class="detail-btn" bindtap="showNutritionDetail">
          <text class="detail-text">查看详细营养信息</text>
          <image class="detail-icon" src="/images/icons/arrow-right.png"></image>
        </button>
      </view>

      <!-- 评价信息 -->
      <view wx:if="{{currentTab === 3 && !cookingMode}}" class="rating-content">
        <view class="rating-summary">
          <view class="rating-score">
            <text class="score-number">{{recipe.rating || 0}}</text>
            <view class="score-stars">
              <image wx:for="{{[1,2,3,4,5]}}" wx:key="*this" class="star-icon {{item <= (recipe.rating || 0) ? 'filled' : ''}}" src="/images/icons/star{{item <= (recipe.rating || 0) ? '-filled' : ''}}.png"></image>
            </view>
            <text class="score-count">{{recipe.ratingCount || 0}}人评价</text>
          </view>
        </view>
        
        <view class="user-rating">
          <text class="rating-title">您的评分</text>
          <view class="rating-stars">
            <image wx:for="{{[1,2,3,4,5]}}" wx:key="*this" class="rating-star {{item <= userRating ? 'active' : ''}}" src="/images/icons/star{{item <= userRating ? '-filled' : ''}}.png" bindtap="rateRecipe" data-rating="{{item}}"></image>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 分享弹窗 -->
  <view wx:if="{{showShareModal}}" class="modal-overlay" bindtap="hideShareModal">
    <view class="share-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">分享菜谱</text>
        <button class="close-btn" bindtap="hideShareModal">
          <image class="close-icon" src="/images/icons/close.png"></image>
        </button>
      </view>
      
      <view class="share-options">
        <button class="share-option" bindtap="shareToFriend">
          <image class="share-icon" src="/images/icons/wechat.png"></image>
          <text class="share-text">分享给朋友</text>
        </button>
        
        <button class="share-option" bindtap="generateShareImage">
          <image class="share-icon" src="/images/icons/image.png"></image>
          <text class="share-text">生成分享图</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 营养详情弹窗 -->
  <view wx:if="{{showNutritionModal}}" class="modal-overlay" bindtap="hideNutritionDetail">
    <view class="nutrition-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">营养详情</text>
        <button class="close-btn" bindtap="hideNutritionDetail">
          <image class="close-icon" src="/images/icons/close.png"></image>
        </button>
      </view>
      
      <view class="nutrition-detail">
        <view class="nutrition-row">
          <text class="nutrition-name">热量</text>
          <text class="nutrition-amount">{{formatNutritionValue(recipe.nutrition.calories, 'kcal')}}</text>
        </view>
        <view class="nutrition-row">
          <text class="nutrition-name">蛋白质</text>
          <text class="nutrition-amount">{{formatNutritionValue(recipe.nutrition.protein)}}</text>
        </view>
        <view class="nutrition-row">
          <text class="nutrition-name">脂肪</text>
          <text class="nutrition-amount">{{formatNutritionValue(recipe.nutrition.fat)}}</text>
        </view>
        <view class="nutrition-row">
          <text class="nutrition-name">碳水化合物</text>
          <text class="nutrition-amount">{{formatNutritionValue(recipe.nutrition.carbs)}}</text>
        </view>
        <view class="nutrition-row">
          <text class="nutrition-name">纤维</text>
          <text class="nutrition-amount">{{formatNutritionValue(recipe.nutrition.fiber)}}</text>
        </view>
        <view class="nutrition-row">
          <text class="nutrition-name">钠</text>
          <text class="nutrition-amount">{{formatNutritionValue(recipe.nutrition.sodium, 'mg')}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 分享画布 -->
  <canvas canvas-id="shareCanvas" class="share-canvas"></canvas>
</view>