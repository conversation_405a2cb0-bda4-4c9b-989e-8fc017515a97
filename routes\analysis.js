const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 获取食材分析数据
router.get('/ingredients', authenticateToken, async (req, res) => {
  try {
    const { days = 30 } = req.query;

    // 获取食材库存分析
    const [stockAnalysis, categoryAnalysis, expiryAnalysis, consumptionAnalysis] = await Promise.all([
      // 库存状态分析
      query(`
        SELECT 
          status,
          COUNT(*) as count,
          CASE 
            WHEN status = 1 THEN '充足'
            WHEN status = 2 THEN '即将过期'
            WHEN status = 3 THEN '已过期'
            ELSE '未知'
          END as status_name
        FROM user_ingredients
        WHERE user_id = ? AND is_deleted = 0
        GROUP BY status
      `, [req.user.id]),

      // 分类分析
      query(`
        SELECT 
          ic.name as category_name,
          COUNT(ui.id) as ingredient_count,
          SUM(ui.quantity) as total_quantity,
          AVG(ui.quantity) as avg_quantity
        FROM user_ingredients ui
        LEFT JOIN ingredient_categories ic ON ui.category_id = ic.id
        WHERE ui.user_id = ? AND ui.is_deleted = 0
        GROUP BY ui.category_id, ic.name
        ORDER BY ingredient_count DESC
      `, [req.user.id]),

      // 过期分析
      query(`
        SELECT 
          DATE(expire_date) as expire_date,
          COUNT(*) as count,
          SUM(quantity) as total_quantity
        FROM user_ingredients
        WHERE user_id = ? AND is_deleted = 0 
        AND expire_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(expire_date)
        ORDER BY expire_date ASC
      `, [req.user.id, parseInt(days)]),

      // 消耗趋势分析
      query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as consumption_count,
          JSON_EXTRACT(content, '$.consumed_quantity') as consumed_quantity
        FROM user_records
        WHERE user_id = ? AND type = 'ingredient_consume'
        AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
      `, [req.user.id, parseInt(days)])
    ]);

    // 计算总体统计
    const totalIngredients = await query(`
      SELECT 
        COUNT(*) as total_count,
        SUM(quantity) as total_quantity,
        COUNT(DISTINCT category_id) as category_count
      FROM user_ingredients
      WHERE user_id = ? AND is_deleted = 0
    `, [req.user.id]);

    res.json({
      success: true,
      data: {
        summary: {
          total_ingredients: totalIngredients[0].total_count,
          total_quantity: parseFloat(totalIngredients[0].total_quantity || 0),
          category_count: totalIngredients[0].category_count
        },
        stock_analysis: stockAnalysis,
        category_analysis: categoryAnalysis.map(item => ({
          ...item,
          total_quantity: parseFloat(item.total_quantity || 0),
          avg_quantity: parseFloat(item.avg_quantity || 0)
        })),
        expiry_analysis: expiryAnalysis.map(item => ({
          ...item,
          total_quantity: parseFloat(item.total_quantity || 0)
        })),
        consumption_trend: consumptionAnalysis
      }
    });
  } catch (error) {
    console.error('获取食材分析数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取食材分析数据失败'
    });
  }
});

// 获取菜谱分析数据
router.get('/recipes', authenticateToken, async (req, res) => {
  try {
    const { days = 30 } = req.query;

    // 获取菜谱分析数据
    const [recipeStats, cookingTrend, popularRecipes, difficultyAnalysis] = await Promise.all([
      // 菜谱基本统计
      query(`
        SELECT 
          COUNT(*) as total_recipes,
          AVG(difficulty) as avg_difficulty,
          AVG(cook_time) as avg_cook_time,
          COUNT(CASE WHEN is_public = 1 THEN 1 END) as public_recipes,
          COUNT(CASE WHEN is_public = 0 THEN 1 END) as private_recipes
        FROM recipes
        WHERE user_id = ? AND is_deleted = 0
      `, [req.user.id]),

      // 制作趋势
      query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as cook_count
        FROM user_records
        WHERE user_id = ? AND type = 'recipe_cook'
        AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
      `, [req.user.id, parseInt(days)]),

      // 热门菜谱
      query(`
        SELECT 
          r.id,
          r.name,
          r.difficulty,
          r.cook_time,
          COUNT(ur.id) as cook_count
        FROM recipes r
        LEFT JOIN user_records ur ON ur.type = 'recipe_cook' 
          AND JSON_EXTRACT(ur.content, '$.recipe_id') = r.id
          AND ur.user_id = ?
        WHERE r.user_id = ? AND r.is_deleted = 0
        GROUP BY r.id, r.name, r.difficulty, r.cook_time
        ORDER BY cook_count DESC
        LIMIT 10
      `, [req.user.id, req.user.id]),

      // 难度分析
      query(`
        SELECT 
          difficulty,
          COUNT(*) as count,
          CASE 
            WHEN difficulty = 1 THEN '简单'
            WHEN difficulty = 2 THEN '中等'
            WHEN difficulty = 3 THEN '困难'
            ELSE '未知'
          END as difficulty_name
        FROM recipes
        WHERE user_id = ? AND is_deleted = 0
        GROUP BY difficulty
        ORDER BY difficulty
      `, [req.user.id])
    ]);

    res.json({
      success: true,
      data: {
        summary: {
          total_recipes: recipeStats[0].total_recipes,
          avg_difficulty: parseFloat(recipeStats[0].avg_difficulty || 0),
          avg_cook_time: parseFloat(recipeStats[0].avg_cook_time || 0),
          public_recipes: recipeStats[0].public_recipes,
          private_recipes: recipeStats[0].private_recipes
        },
        cooking_trend: cookingTrend,
        popular_recipes: popularRecipes.map(recipe => ({
          ...recipe,
          cook_time: parseFloat(recipe.cook_time || 0)
        })),
        difficulty_analysis: difficultyAnalysis
      }
    });
  } catch (error) {
    console.error('获取菜谱分析数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取菜谱分析数据失败'
    });
  }
});

// 获取购物分析数据
router.get('/shopping', authenticateToken, async (req, res) => {
  try {
    const { days = 30 } = req.query;

    // 获取购物分析数据
    const [shoppingStats, spendingTrend, categorySpending, frequentItems] = await Promise.all([
      // 购物基本统计
      query(`
        SELECT 
          COUNT(*) as total_items,
          COUNT(CASE WHEN is_purchased = 1 THEN 1 END) as purchased_items,
          COUNT(CASE WHEN is_purchased = 0 THEN 1 END) as pending_items,
          SUM(CASE WHEN is_purchased = 1 THEN actual_price ELSE 0 END) as total_spent,
          AVG(CASE WHEN is_purchased = 1 THEN actual_price ELSE 0 END) as avg_item_price
        FROM shopping_list
        WHERE user_id = ? AND is_deleted = 0
      `, [req.user.id]),

      // 消费趋势
      query(`
        SELECT 
          DATE(purchase_date) as date,
          COUNT(*) as purchase_count,
          SUM(actual_price) as daily_spending
        FROM shopping_list
        WHERE user_id = ? AND is_purchased = 1 AND is_deleted = 0
        AND purchase_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(purchase_date)
        ORDER BY date DESC
      `, [req.user.id, parseInt(days)]),

      // 分类消费
      query(`
        SELECT 
          ic.name as category_name,
          COUNT(sl.id) as item_count,
          SUM(CASE WHEN sl.is_purchased = 1 THEN sl.actual_price ELSE 0 END) as category_spending,
          AVG(CASE WHEN sl.is_purchased = 1 THEN sl.actual_price ELSE 0 END) as avg_price
        FROM shopping_list sl
        LEFT JOIN ingredient_categories ic ON sl.category_id = ic.id
        WHERE sl.user_id = ? AND sl.is_deleted = 0
        GROUP BY sl.category_id, ic.name
        ORDER BY category_spending DESC
      `, [req.user.id]),

      // 常购商品
      query(`
        SELECT 
          name,
          COUNT(*) as purchase_count,
          SUM(actual_price) as total_spent,
          AVG(actual_price) as avg_price,
          MAX(purchase_date) as last_purchase
        FROM shopping_list
        WHERE user_id = ? AND is_purchased = 1 AND is_deleted = 0
        AND purchase_date >= DATE_SUB(NOW(), INTERVAL 90 DAY)
        GROUP BY name
        HAVING purchase_count >= 2
        ORDER BY purchase_count DESC, total_spent DESC
        LIMIT 10
      `, [req.user.id])
    ]);

    res.json({
      success: true,
      data: {
        summary: {
          total_items: shoppingStats[0].total_items,
          purchased_items: shoppingStats[0].purchased_items,
          pending_items: shoppingStats[0].pending_items,
          total_spent: parseFloat(shoppingStats[0].total_spent || 0),
          avg_item_price: parseFloat(shoppingStats[0].avg_item_price || 0),
          completion_rate: shoppingStats[0].total_items > 0 
            ? Math.round((shoppingStats[0].purchased_items / shoppingStats[0].total_items) * 100)
            : 0
        },
        spending_trend: spendingTrend.map(item => ({
          ...item,
          daily_spending: parseFloat(item.daily_spending || 0)
        })),
        category_spending: categorySpending.map(item => ({
          ...item,
          category_spending: parseFloat(item.category_spending || 0),
          avg_price: parseFloat(item.avg_price || 0)
        })),
        frequent_items: frequentItems.map(item => ({
          ...item,
          total_spent: parseFloat(item.total_spent || 0),
          avg_price: parseFloat(item.avg_price || 0)
        }))
      }
    });
  } catch (error) {
    console.error('获取购物分析数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取购物分析数据失败'
    });
  }
});

// 获取用户行为分析
router.get('/behavior', authenticateToken, async (req, res) => {
  try {
    const { days = 30 } = req.query;

    // 获取用户行为分析数据
    const [activityStats, operationTrend, peakHours, weeklyPattern] = await Promise.all([
      // 活动统计
      query(`
        SELECT 
          type,
          COUNT(*) as count,
          CASE 
            WHEN type LIKE 'ingredient_%' THEN '食材管理'
            WHEN type LIKE 'recipe_%' THEN '菜谱管理'
            WHEN type LIKE 'shopping_%' THEN '购物管理'
            WHEN type LIKE 'favorite_%' THEN '收藏管理'
            ELSE '其他'
          END as category
        FROM user_records
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY type
        ORDER BY count DESC
      `, [req.user.id, parseInt(days)]),

      // 操作趋势
      query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as operation_count
        FROM user_records
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
      `, [req.user.id, parseInt(days)]),

      // 使用高峰时段
      query(`
        SELECT 
          HOUR(created_at) as hour,
          COUNT(*) as count
        FROM user_records
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY HOUR(created_at)
        ORDER BY hour
      `, [req.user.id, parseInt(days)]),

      // 周使用模式
      query(`
        SELECT 
          DAYOFWEEK(created_at) as day_of_week,
          COUNT(*) as count,
          CASE DAYOFWEEK(created_at)
            WHEN 1 THEN '周日'
            WHEN 2 THEN '周一'
            WHEN 3 THEN '周二'
            WHEN 4 THEN '周三'
            WHEN 5 THEN '周四'
            WHEN 6 THEN '周五'
            WHEN 7 THEN '周六'
          END as day_name
        FROM user_records
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DAYOFWEEK(created_at)
        ORDER BY day_of_week
      `, [req.user.id, parseInt(days)])
    ]);

    // 计算活动分类统计
    const categoryStats = {};
    activityStats.forEach(stat => {
      if (categoryStats[stat.category]) {
        categoryStats[stat.category] += stat.count;
      } else {
        categoryStats[stat.category] = stat.count;
      }
    });

    // 找出最活跃的时段
    const mostActiveHour = peakHours.reduce((max, current) => 
      current.count > max.count ? current : max, 
      { hour: 0, count: 0 }
    );

    // 找出最活跃的日期
    const mostActiveDay = weeklyPattern.reduce((max, current) => 
      current.count > max.count ? current : max, 
      { day_name: '未知', count: 0 }
    );

    res.json({
      success: true,
      data: {
        summary: {
          total_operations: activityStats.reduce((sum, stat) => sum + stat.count, 0),
          most_active_hour: mostActiveHour.hour,
          most_active_day: mostActiveDay.day_name,
          avg_daily_operations: operationTrend.length > 0 
            ? Math.round(operationTrend.reduce((sum, day) => sum + day.operation_count, 0) / operationTrend.length)
            : 0
        },
        activity_stats: activityStats,
        category_stats: Object.entries(categoryStats).map(([category, count]) => ({
          category,
          count
        })),
        operation_trend: operationTrend,
        peak_hours: peakHours,
        weekly_pattern: weeklyPattern
      }
    });
  } catch (error) {
    console.error('获取用户行为分析错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户行为分析失败'
    });
  }
});

// 获取综合仪表板数据
router.get('/dashboard', authenticateToken, async (req, res) => {
  try {
    // 获取各模块的关键指标
    const [ingredientSummary, recipeSummary, shoppingSummary, recentActivity] = await Promise.all([
      // 食材概览
      query(`
        SELECT 
          COUNT(*) as total_ingredients,
          COUNT(CASE WHEN status = 2 THEN 1 END) as expiring_soon,
          COUNT(CASE WHEN status = 3 THEN 1 END) as expired
        FROM user_ingredients
        WHERE user_id = ? AND is_deleted = 0
      `, [req.user.id]),

      // 菜谱概览 (注意：recipes表没有user_id字段，这里查询所有公开菜谱)
      query(`
        SELECT
          COUNT(*) as total_recipes,
          COUNT(CASE WHEN is_public = 1 THEN 1 END) as public_recipes
        FROM recipes
        WHERE is_deleted = 0
      `),

      // 购物概览
      query(`
        SELECT 
          COUNT(*) as total_items,
          COUNT(CASE WHEN is_purchased = 0 THEN 1 END) as pending_items,
          SUM(CASE WHEN is_purchased = 1 THEN actual_price ELSE 0 END) as total_spent
        FROM shopping_list
        WHERE user_id = ? AND is_deleted = 0
      `, [req.user.id]),

      // 最近活动
      query(`
        SELECT 
          type,
          content,
          created_at
        FROM user_records
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 10
      `, [req.user.id])
    ]);

    // 获取本周统计
    const weeklyStats = await query(`
      SELECT 
        type,
        COUNT(*) as count
      FROM user_records
      WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY type
    `, [req.user.id]);

    // 处理最近活动数据
    const processedActivity = recentActivity.map(activity => {
      try {
        const content = JSON.parse(activity.content);
        return {
          type: activity.type,
          content: content,
          created_at: activity.created_at,
          description: generateActivityDescription(activity.type, content)
        };
      } catch (error) {
        return {
          type: activity.type,
          content: activity.content,
          created_at: activity.created_at,
          description: activity.content
        };
      }
    });

    // 计算健康度评分
    const healthScore = calculateHealthScore({
      total_ingredients: ingredientSummary[0].total_ingredients,
      expiring_soon: ingredientSummary[0].expiring_soon,
      expired: ingredientSummary[0].expired,
      pending_items: shoppingSummary[0].pending_items
    });

    res.json({
      success: true,
      data: {
        summary: {
          ingredients: {
            total: ingredientSummary[0].total_ingredients,
            expiring_soon: ingredientSummary[0].expiring_soon,
            expired: ingredientSummary[0].expired
          },
          recipes: {
            total: recipeSummary[0].total_recipes,
            public: recipeSummary[0].public_recipes
          },
          shopping: {
            total_items: shoppingSummary[0].total_items,
            pending_items: shoppingSummary[0].pending_items,
            total_spent: parseFloat(shoppingSummary[0].total_spent || 0)
          },
          health_score: healthScore
        },
        weekly_stats: weeklyStats,
        recent_activity: processedActivity
      }
    });
  } catch (error) {
    console.error('获取仪表板数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取仪表板数据失败'
    });
  }
});

// 生成活动描述
function generateActivityDescription(type, content) {
  try {
    switch (type) {
      case 'ingredient_add':
        return `添加了食材：${content.ingredient_name}`;
      case 'ingredient_consume':
        return `消耗了食材：${content.ingredient_name}`;
      case 'recipe_add':
        return `添加了菜谱：${content.recipe_name}`;
      case 'recipe_cook':
        return `制作了菜谱：${content.recipe_name}`;
      case 'shopping_add':
        return `添加购物项：${content.item_name}`;
      case 'shopping_purchase':
        return `购买了商品：${content.item_name}`;
      case 'favorite_add':
        return `收藏了${content.favorite_type === 'recipe' ? '菜谱' : '食材'}：${content.title}`;
      default:
        return '进行了操作';
    }
  } catch (error) {
    return '进行了操作';
  }
}

// 计算健康度评分
function calculateHealthScore(data) {
  let score = 100;

  // 过期食材扣分
  if (data.expired > 0) {
    score -= Math.min(data.expired * 10, 30);
  }

  // 即将过期食材扣分
  if (data.expiring_soon > 0) {
    score -= Math.min(data.expiring_soon * 5, 20);
  }

  // 待购买商品过多扣分
  if (data.pending_items > 10) {
    score -= Math.min((data.pending_items - 10) * 2, 15);
  }

  // 食材库存过少扣分
  if (data.total_ingredients < 5) {
    score -= (5 - data.total_ingredients) * 5;
  }

  return Math.max(score, 0);
}

// 获取食材浪费分析
router.get('/waste', authenticateToken, async (req, res) => {
  try {
    const { days = 30 } = req.query;

    // 获取过期食材统计
    const wasteStats = await query(`
      SELECT 
        ui.name,
        ui.quantity,
        ui.unit,
        ui.expire_date,
        ic.name as category_name,
        DATEDIFF(NOW(), ui.expire_date) as days_expired
      FROM user_ingredients ui
      LEFT JOIN ingredient_categories ic ON ui.category_id = ic.id
      WHERE ui.user_id = ? AND ui.status = 3 AND ui.is_deleted = 0
      AND ui.expire_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
      ORDER BY ui.expire_date DESC
    `, [req.user.id, parseInt(days)]);

    // 按分类统计浪费
    const categoryWaste = await query(`
      SELECT 
        ic.name as category_name,
        COUNT(ui.id) as waste_count,
        SUM(ui.quantity) as total_wasted_quantity
      FROM user_ingredients ui
      LEFT JOIN ingredient_categories ic ON ui.category_id = ic.id
      WHERE ui.user_id = ? AND ui.status = 3 AND ui.is_deleted = 0
      AND ui.expire_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
      GROUP BY ui.category_id, ic.name
      ORDER BY waste_count DESC
    `, [req.user.id, parseInt(days)]);

    // 浪费趋势
    const wasteTrend = await query(`
      SELECT 
        DATE(expire_date) as date,
        COUNT(*) as waste_count,
        SUM(quantity) as wasted_quantity
      FROM user_ingredients
      WHERE user_id = ? AND status = 3 AND is_deleted = 0
      AND expire_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
      GROUP BY DATE(expire_date)
      ORDER BY date DESC
    `, [req.user.id, parseInt(days)]);

    const totalWasted = wasteStats.length;
    const totalQuantityWasted = wasteStats.reduce((sum, item) => sum + parseFloat(item.quantity || 0), 0);

    res.json({
      success: true,
      data: {
        summary: {
          total_wasted_items: totalWasted,
          total_wasted_quantity: totalQuantityWasted,
          avg_days_expired: totalWasted > 0 
            ? Math.round(wasteStats.reduce((sum, item) => sum + item.days_expired, 0) / totalWasted)
            : 0
        },
        waste_details: wasteStats.map(item => ({
          ...item,
          quantity: parseFloat(item.quantity || 0)
        })),
        category_waste: categoryWaste.map(item => ({
          ...item,
          total_wasted_quantity: parseFloat(item.total_wasted_quantity || 0)
        })),
        waste_trend: wasteTrend.map(item => ({
          ...item,
          wasted_quantity: parseFloat(item.wasted_quantity || 0)
        }))
      }
    });
  } catch (error) {
    console.error('获取食材浪费分析错误:', error);
    res.status(500).json({
      success: false,
      message: '获取食材浪费分析失败'
    });
  }
});

// 获取营养分析（基于菜谱制作记录）
router.get('/nutrition', authenticateToken, async (req, res) => {
  try {
    const { days = 30 } = req.query;

    // 获取制作的菜谱及其营养信息
    const nutritionData = await query(`
      SELECT 
        r.name as recipe_name,
        r.nutrition_info,
        COUNT(ur.id) as cook_count,
        MAX(ur.created_at) as last_cooked
      FROM user_records ur
      JOIN recipes r ON JSON_EXTRACT(ur.content, '$.recipe_id') = r.id
      WHERE ur.user_id = ? AND ur.type = 'recipe_cook'
      AND ur.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      AND r.is_deleted = 0
      GROUP BY r.id, r.name, r.nutrition_info
      ORDER BY cook_count DESC
    `, [req.user.id, parseInt(days)]);

    // 处理营养数据
    const processedNutrition = nutritionData.map(item => {
      try {
        const nutrition = JSON.parse(item.nutrition_info || '{}');
        return {
          recipe_name: item.recipe_name,
          cook_count: item.cook_count,
          last_cooked: item.last_cooked,
          nutrition: nutrition
        };
      } catch (error) {
        return {
          recipe_name: item.recipe_name,
          cook_count: item.cook_count,
          last_cooked: item.last_cooked,
          nutrition: {}
        };
      }
    });

    // 计算总营养摄入（简化计算）
    const totalNutrition = {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0
    };

    processedNutrition.forEach(item => {
      const nutrition = item.nutrition;
      const count = item.cook_count;
      
      totalNutrition.calories += (nutrition.calories || 0) * count;
      totalNutrition.protein += (nutrition.protein || 0) * count;
      totalNutrition.carbs += (nutrition.carbs || 0) * count;
      totalNutrition.fat += (nutrition.fat || 0) * count;
    });

    res.json({
      success: true,
      data: {
        summary: {
          total_recipes_cooked: processedNutrition.length,
          total_cooking_times: processedNutrition.reduce((sum, item) => sum + item.cook_count, 0),
          estimated_nutrition: totalNutrition
        },
        recipe_nutrition: processedNutrition
      }
    });
  } catch (error) {
    console.error('获取营养分析错误:', error);
    res.status(500).json({
      success: false,
      message: '获取营养分析失败'
    });
  }
});

module.exports = router;