// 验证recipes/add页面的标签匹配
const fs = require('fs');

function verifyRecipesAdd() {
  const filePath = 'miniprogram/pages/recipes/add/add.wxml';
  
  if (!fs.existsSync(filePath)) {
    console.log('❌ 文件不存在');
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  console.log('🔍 验证recipes/add页面标签匹配...\n');
  
  // 检查关键标签的匹配
  const keyTags = [
    { name: 'page-container', start: 2, expectedEnd: 306 },
    { name: 'page-header', start: 4, expectedEnd: 14 },
    { name: 'scroll-view', start: 17, expectedEnd: 297 },
    { name: 'form-container', start: 18, expectedEnd: 296 },
    { name: 'bottom-actions', start: 300, expectedEnd: 305 }
  ];
  
  keyTags.forEach(tag => {
    const startLine = lines[tag.start - 1];
    const endLine = lines[tag.expectedEnd - 1];
    
    console.log(`📋 检查 ${tag.name}:`);
    console.log(`  第${tag.start}行: ${startLine.trim()}`);
    console.log(`  第${tag.expectedEnd}行: ${endLine.trim()}`);
    
    if (startLine.includes(`class="${tag.name}"`) && endLine.trim() === '</view>') {
      console.log(`  ✅ ${tag.name} 标签匹配正确\n`);
    } else if (tag.name === 'scroll-view' && endLine.trim() === '</scroll-view>') {
      console.log(`  ✅ ${tag.name} 标签匹配正确\n`);
    } else {
      console.log(`  ❌ ${tag.name} 标签匹配有问题\n`);
    }
  });
  
  // 检查是否有明显的语法错误
  const errors = [];
  
  // 检查连续的结束标签
  for (let i = 0; i < lines.length - 1; i++) {
    const currentLine = lines[i].trim();
    const nextLine = lines[i + 1].trim();
    
    if (currentLine === '</view>' && nextLine === '</view>') {
      // 这可能是正常的嵌套结构，检查是否合理
      const prevLine = i > 0 ? lines[i - 1].trim() : '';
      if (!prevLine.includes('<view') && !prevLine.includes('</')) {
        errors.push(`第${i + 1}-${i + 2}行: 可能的多余结束标签`);
      }
    }
  }
  
  // 检查特定的错误模式
  if (content.includes('</view></scroll-view>')) {
    errors.push('发现可能的错误模式: </view></scroll-view>');
  }
  
  console.log('📊 验证结果:');
  if (errors.length === 0) {
    console.log('✅ 没有发现明显的语法错误');
    console.log('🎉 recipes/add页面应该可以正常编译了！');
  } else {
    console.log(`❌ 发现 ${errors.length} 个潜在问题:`);
    errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });
  }
}

verifyRecipesAdd();
