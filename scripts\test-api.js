const axios = require('axios');

// 测试API端点
const API_BASE = 'http://127.0.0.1:3000';

async function testIngredientAPI() {
  try {
    console.log('🧪 开始测试食材API...');
    
    // 测试添加食材API
    const testData = {
      name: '测试食材',
      category_id: 1,
      quantity: 2,
      unit: '个',
      purchase_date: new Date().toISOString().split('T')[0],
      expire_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      storage_location: '冰箱',
      price: 10.5,
      notes: '测试备注'
    };
    
    console.log('📤 发送测试数据:', testData);
    
    const response = await axios.post(`${API_BASE}/api/ingredients`, testData, {
      headers: {
        'Content-Type': 'application/json',
        // 注意：这里需要有效的token，实际测试时需要先登录获取token
        'Authorization': 'Bearer test_token'
      }
    });
    
    console.log('✅ API响应状态码:', response.status);
    console.log('📥 API响应数据:', response.data);
    
    if (response.status === 201) {
      console.log('🎉 测试成功！API正确返回201状态码');
    } else {
      console.log('⚠️  API返回了意外的状态码:', response.status);
    }
    
  } catch (error) {
    console.error('❌ API测试失败:');
    
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应数据:', error.response.data);
      console.error('响应头:', error.response.headers);
    } else if (error.request) {
      console.error('请求失败:', error.request);
    } else {
      console.error('错误信息:', error.message);
    }
  }
}

// 测试状态码处理
function testStatusCodeHandling() {
  console.log('\n🧪 测试状态码处理逻辑...');
  
  const testCases = [
    { code: 200, expected: true },
    { code: 201, expected: true },
    { code: 202, expected: true },
    { code: 299, expected: true },
    { code: 300, expected: false },
    { code: 400, expected: false },
    { code: 401, expected: false },
    { code: 500, expected: false }
  ];
  
  testCases.forEach(testCase => {
    const isSuccess = testCase.code >= 200 && testCase.code < 300;
    const result = isSuccess === testCase.expected ? '✅' : '❌';
    console.log(`${result} 状态码 ${testCase.code}: ${isSuccess ? '成功' : '失败'} (期望: ${testCase.expected ? '成功' : '失败'})`);
  });
}

// 运行测试
if (require.main === module) {
  console.log('🚀 开始API测试...\n');
  
  testStatusCodeHandling();
  
  console.log('\n' + '='.repeat(50));
  console.log('注意：要测试实际API，请确保：');
  console.log('1. 服务器正在运行 (npm start)');
  console.log('2. 数据库连接正常');
  console.log('3. 有有效的认证token');
  console.log('='.repeat(50) + '\n');
  
  // 如果需要测试实际API，取消下面的注释
  // testIngredientAPI();
}

module.exports = { testIngredientAPI, testStatusCodeHandling };
