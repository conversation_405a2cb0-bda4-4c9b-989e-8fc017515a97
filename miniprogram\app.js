// 智能冰箱食材管理助手小程序
App({
  globalData: {
    userInfo: null,
    token: null,
    serverUrl: 'http://127.0.0.1:3000/api', // 开发环境API地址
    version: '1.0.0'
  },

  onLaunch() {
    console.log('智能冰箱食材管理助手启动');
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 检查更新
    this.checkForUpdate();
    
    // 初始化云开发（如果使用）
    if (wx.cloud) {
      wx.cloud.init({
        traceUser: true
      });
    }
  },

  onShow() {
    console.log('小程序显示');
  },

  onHide() {
    console.log('小程序隐藏');
  },

  onError(error) {
    console.error('小程序错误:', error);
    // 可以在这里上报错误到服务器
    this.reportError(error);
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    if (token && userInfo) {
      this.globalData.token = token;
      this.globalData.userInfo = userInfo;
      console.log('用户已登录:', userInfo.username);
    } else {
      console.log('用户未登录');
    }
  },

  // 检查小程序更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本');
        }
      });
      
      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });
      
      updateManager.onUpdateFailed(() => {
        wx.showToast({
          title: '更新失败',
          icon: 'error'
        });
      });
    }
  },

  // 用户登录
  login(loginData) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.globalData.serverUrl}/users/login`,
        method: 'POST',
        data: loginData,
        success: (res) => {
          if (res.data.success) {
            const { token, user } = res.data.data;
            
            // 保存登录信息
            wx.setStorageSync('token', token);
            wx.setStorageSync('userInfo', user);
            
            this.globalData.token = token;
            this.globalData.userInfo = user;
            
            wx.showToast({
              title: '登录成功',
              icon: 'success'
            });
            
            resolve(res.data);
          } else {
            wx.showToast({
              title: res.data.message || '登录失败',
              icon: 'error'
            });
            reject(res.data);
          }
        },
        fail: (error) => {
          wx.showToast({
            title: '网络错误',
            icon: 'error'
          });
          reject(error);
        }
      });
    });
  },

  // 用户注册
  register(registerData) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.globalData.serverUrl}/users/register`,
        method: 'POST',
        data: registerData,
        success: (res) => {
          if (res.data.success) {
            wx.showToast({
              title: '注册成功',
              icon: 'success'
            });
            resolve(res.data);
          } else {
            wx.showToast({
              title: res.data.message || '注册失败',
              icon: 'error'
            });
            reject(res.data);
          }
        },
        fail: (error) => {
          wx.showToast({
            title: '网络错误',
            icon: 'error'
          });
          reject(error);
        }
      });
    });
  },

  // 用户登出
  logout() {
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    
    this.globalData.token = null;
    this.globalData.userInfo = null;
    
    wx.showToast({
      title: '已退出登录',
      icon: 'success'
    });
    
    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    });
  },

  // 发起API请求
  request(options) {
    return new Promise((resolve, reject) => {
      const { url, method = 'GET', data = {}, header = {} } = options;
      
      // 添加认证头
      if (this.globalData.token) {
        header.Authorization = `Bearer ${this.globalData.token}`;
      }
      
      wx.request({
        url: `${this.globalData.serverUrl}${url}`,
        method,
        data,
        header: {
          'Content-Type': 'application/json',
          ...header
        },
        success: (res) => {
          if (res.statusCode === 401) {
            // Token过期，重新登录
            this.logout();
            return;
          }
          
          if (res.data.success) {
            resolve(res.data);
          } else {
            wx.showToast({
              title: res.data.message || '请求失败',
              icon: 'error'
            });
            reject(res.data);
          }
        },
        fail: (error) => {
          wx.showToast({
            title: '网络错误',
            icon: 'error'
          });
          reject(error);
        }
      });
    });
  },

  // 上传文件
  uploadFile(options) {
    return new Promise((resolve, reject) => {
      const { filePath, name = 'file', formData = {} } = options;
      
      wx.uploadFile({
        url: `${this.globalData.serverUrl}/upload`,
        filePath,
        name,
        formData,
        header: {
          Authorization: `Bearer ${this.globalData.token}`
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.success) {
              resolve(data);
            } else {
              wx.showToast({
                title: data.message || '上传失败',
                icon: 'error'
              });
              reject(data);
            }
          } catch (error) {
            wx.showToast({
              title: '上传失败',
              icon: 'error'
            });
            reject(error);
          }
        },
        fail: (error) => {
          wx.showToast({
            title: '上传失败',
            icon: 'error'
          });
          reject(error);
        }
      });
    });
  },

  // 格式化日期
  formatDate(date, format = 'YYYY-MM-DD') {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hour = String(d.getHours()).padStart(2, '0');
    const minute = String(d.getMinutes()).padStart(2, '0');
    const second = String(d.getSeconds()).padStart(2, '0');
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second);
  },

  // 计算日期差
  dateDiff(date1, date2) {
    const d1 = new Date(date1);
    const d2 = new Date(date2);
    return Math.ceil((d2 - d1) / (1000 * 60 * 60 * 24));
  },

  // 获取食材状态
  getIngredientStatus(expireDate) {
    const today = new Date();
    const expire = new Date(expireDate);
    const diffDays = Math.ceil((expire - today) / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return { status: 3, text: '已过期', color: '#ff4757' };
    } else if (diffDays <= 3) {
      return { status: 2, text: '即将过期', color: '#ffa502' };
    } else {
      return { status: 1, text: '新鲜', color: '#2ed573' };
    }
  },

  // 错误上报
  reportError(error) {
    // 可以在这里实现错误上报逻辑
    console.error('错误上报:', error);
  },

  // 显示加载中
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    });
  },

  // 隐藏加载中
  hideLoading() {
    wx.hideLoading();
  },

  // 显示成功提示
  showSuccess(title) {
    wx.showToast({
      title,
      icon: 'success',
      duration: 2000
    });
  },

  // 显示错误提示
  showError(title) {
    wx.showToast({
      title,
      icon: 'error',
      duration: 2000
    });
  },

  // 确认对话框
  showConfirm(options) {
    return new Promise((resolve) => {
      wx.showModal({
        title: options.title || '提示',
        content: options.content || '',
        confirmText: options.confirmText || '确定',
        cancelText: options.cancelText || '取消',
        success: (res) => {
          resolve(res.confirm);
        }
      });
    });
  }
});