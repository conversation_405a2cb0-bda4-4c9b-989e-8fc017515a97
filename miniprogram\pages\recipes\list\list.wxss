/* 菜谱列表页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 搜索区域 */
.search-section {
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 24rpx;
  padding: 16rpx 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  border-color: #4CAF50;
  background-color: white;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  background-color: transparent;
  border: none;
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-left: 16rpx;
}

.action-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.action-btn:active {
  opacity: 1;
}

.action-btn image {
  width: 28rpx;
  height: 28rpx;
}

.filter-btn {
  width: 80rpx;
  height: 64rpx;
  background-color: #4CAF50;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.filter-btn:active {
  background-color: #45a049;
}

.filter-btn image {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

/* 分类滚动 */
.category-scroll {
  white-space: nowrap;
  margin-bottom: 20rpx;
}

.category-list {
  display: flex;
  gap: 16rpx;
  padding: 0 4rpx;
}

.category-item {
  flex-shrink: 0;
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.category-item.active {
  background-color: #4CAF50;
  color: white;
}

/* 排序区域 */
.sort-section {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.sort-list {
  display: flex;
  gap: 40rpx;
}

.sort-item {
  font-size: 26rpx;
  color: #666;
  padding: 8rpx 0;
  position: relative;
  transition: color 0.3s ease;
}

.sort-item.active {
  color: #4CAF50;
  font-weight: 500;
}

.sort-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background-color: #4CAF50;
}

/* 菜谱网格 */
.recipes-container {
  padding: 0 20rpx;
}

.recipes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.recipe-card {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.recipe-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

.recipe-image {
  position: relative;
  width: 100%;
  height: 240rpx;
  overflow: hidden;
}

.recipe-img {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
}

.favorite-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.favorite-btn.favorited {
  background-color: rgba(255, 71, 87, 0.1);
}

.favorite-btn image {
  width: 28rpx;
  height: 28rpx;
}

.difficulty-badge {
  position: absolute;
  bottom: 16rpx;
  left: 16rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: white;
}

.difficulty-easy {
  background-color: #2ed573;
}

.difficulty-medium {
  background-color: #ffa502;
}

.difficulty-hard {
  background-color: #ff4757;
}

.recipe-info {
  padding: 24rpx;
}

.recipe-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recipe-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recipe-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.meta-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

.meta-text {
  font-size: 22rpx;
  color: #666;
}

.recipe-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.recipe-tag {
  font-size: 20rpx;
  color: #4CAF50;
  background-color: rgba(76, 175, 80, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.share-btn {
  position: absolute;
  bottom: 24rpx;
  right: 24rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.share-btn:active {
  background-color: #e0e0e0;
  opacity: 1;
}

.share-btn image {
  width: 24rpx;
  height: 24rpx;
}

/* 加载状态 */
.load-more,
.loading-more,
.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  margin-top: 20rpx;
}

.load-more-text,
.loading-text,
.no-more-text {
  font-size: 26rpx;
  color: #999;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  width: 200rpx;
}

/* 筛选面板 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-mask.show {
  opacity: 1;
  visibility: visible;
}

.filter-panel {
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  width: 600rpx;
  background-color: white;
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.filter-panel.show {
  transform: translateX(0);
}

.filter-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.filter-actions {
  display: flex;
  gap: 30rpx;
}

.filter-reset,
.filter-close {
  font-size: 28rpx;
  color: #4CAF50;
}

.filter-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
  font-weight: 500;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.filter-option {
  padding: 16rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.filter-option.active {
  background-color: #4CAF50;
  color: white;
}

.filter-footer {
  margin-top: auto;
  padding: 30rpx;
}

/* 悬浮按钮 */
.fab-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(76, 175, 80, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
}

.fab-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 12rpx rgba(76, 175, 80, 0.3);
}

.fab-btn image {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .recipes-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  .recipe-card {
    display: flex;
    height: 200rpx;
  }
  
  .recipe-image {
    width: 200rpx;
    height: 100%;
    flex-shrink: 0;
  }
  
  .recipe-info {
    flex: 1;
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .recipe-name {
    font-size: 28rpx;
    margin-bottom: 6rpx;
  }
  
  .recipe-desc {
    font-size: 22rpx;
    margin-bottom: 12rpx;
    -webkit-line-clamp: 1;
  }
  
  .recipe-meta {
    gap: 16rpx;
    margin-bottom: 12rpx;
  }
  
  .meta-text {
    font-size: 20rpx;
  }
  
  .share-btn {
    bottom: 20rpx;
    right: 20rpx;
    width: 36rpx;
    height: 36rpx;
  }
  
  .share-btn image {
    width: 20rpx;
    height: 20rpx;
  }
  
  .filter-panel {
    width: 100%;
  }
  
  .fab-btn {
    width: 80rpx;
    height: 80rpx;
    bottom: 30rpx;
    right: 30rpx;
  }
  
  .fab-btn image {
    width: 40rpx;
    height: 40rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .search-section,
  .recipe-card,
  .filter-panel {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .search-input-wrapper {
    background-color: #404040;
  }
  
  .search-input-wrapper:focus-within {
    background-color: #2d2d2d;
  }
  
  .search-input {
    color: white;
  }
  
  .category-item {
    background-color: #404040;
    color: #ccc;
  }
  
  .recipe-name,
  .filter-title,
  .filter-label,
  .empty-text {
    color: white;
  }
  
  .recipe-desc,
  .meta-text,
  .sort-item,
  .empty-desc {
    color: #999;
  }
  
  .favorite-btn {
    background-color: rgba(45, 45, 45, 0.9);
  }
  
  .share-btn {
    background-color: #404040;
  }
  
  .filter-option {
    background-color: #404040;
    color: #ccc;
  }
  
  .filter-section {
    border-color: #404040;
  }
}

/* 动画效果 */
.recipe-card {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 收藏动画 */
.favorite-btn.favorited {
  animation: heartBeat 0.6s ease;
}

@keyframes heartBeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}
/* Emoji图标样式 */
.emoji-icon {
  font-size: 32rpx;
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
}

/* 小尺寸emoji图标 */
.emoji-icon.small {
  font-size: 24rpx;
}

/* 大尺寸emoji图标 */
.emoji-icon.large {
  font-size: 48rpx;
}

/* 图标与文字对齐 */
.emoji-icon.inline {
  margin-right: 8rpx;
}
