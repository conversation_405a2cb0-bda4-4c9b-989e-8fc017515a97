/* 菜谱列表页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 搜索区域 */
.search-section {
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 24rpx;
  padding: 16rpx 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  border-color: #4CAF50;
  background-color: white;
}

.search-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

/* Emoji图标通用样式 */
.emoji-icon {
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  display: inline-block;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
}

/* 卡片悬浮效果增强 */
.recipe-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

.recipe-card:hover::before {
  opacity: 1;
}

/* 图片遮罩效果 */
.recipe-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.1) 100%);
  pointer-events: none;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  background-color: transparent;
  border: none;
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-left: 16rpx;
}

.action-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.action-btn:active {
  opacity: 1;
}

.action-icon {
  font-size: 28rpx;
}

.filter-btn {
  width: 80rpx;
  height: 64rpx;
  background-color: #4CAF50;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.filter-btn:active {
  background-color: #45a049;
}

.filter-btn image {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

/* 分类滚动 */
.category-scroll {
  white-space: nowrap;
  margin-bottom: 20rpx;
}

.category-list {
  display: flex;
  gap: 16rpx;
  padding: 0 4rpx;
}

.category-item {
  flex-shrink: 0;
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.category-item.active {
  background-color: #4CAF50;
  color: white;
}

/* 排序区域 */
.sort-section {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.sort-list {
  display: flex;
  gap: 40rpx;
}

.sort-item {
  font-size: 26rpx;
  color: #666;
  padding: 8rpx 0;
  position: relative;
  transition: color 0.3s ease;
}

.sort-item.active {
  color: #4CAF50;
  font-weight: 500;
}

.sort-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background-color: #4CAF50;
}

/* 菜谱网格 */
.recipes-container {
  padding: 0 20rpx;
}

.recipes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  padding: 24rpx 0;
}

.recipe-card {
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
}

.recipe-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
}

.recipe-card:active {
  transform: scale(0.98) translateY(-2rpx);
  box-shadow: 0 6rpx 25rpx rgba(0, 0, 0, 0.1);
}

.recipe-image {
  position: relative;
  width: 100%;
  height: 260rpx;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.recipe-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
  background-color: #f8f9fa;
}

.recipe-card:hover .recipe-img {
  transform: scale(1.05);
}

.favorite-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 56rpx;
  height: 56rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.favorite-btn:active {
  transform: scale(0.9);
}

.favorite-btn.favorited {
  background-color: rgba(255, 71, 87, 0.15);
  color: #ff4757;
}

.favorite-btn image {
  width: 28rpx;
  height: 28rpx;
}

.difficulty-badge {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  color: white;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.difficulty-easy {
  background: linear-gradient(135deg, #2ed573, #17c0eb);
}

.difficulty-medium {
  background: linear-gradient(135deg, #ffa502, #ff6348);
}

.difficulty-hard {
  background: linear-gradient(135deg, #ff4757, #c44569);
}

.recipe-info {
  padding: 28rpx 24rpx 24rpx;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(248, 249, 250, 0.3) 100%);
}

.recipe-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  letter-spacing: 0.5rpx;
}

.recipe-desc {
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recipe-meta {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 20rpx;
  padding: 16rpx 20rpx;
  background-color: rgba(248, 249, 250, 0.6);
  border-radius: 16rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex: 1;
}

.meta-icon {
  font-size: 26rpx;
  opacity: 0.8;
}

.meta-text {
  font-size: 24rpx;
  color: #495057;
  font-weight: 500;
}

.recipe-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 8rpx;
}

.recipe-tag {
  font-size: 22rpx;
  color: #4CAF50;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(76, 175, 80, 0.2);
  font-weight: 500;
  transition: all 0.3s ease;
}

.recipe-tag:active {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1));
  transform: scale(0.95);
}

.share-btn {
  position: absolute;
  bottom: 24rpx;
  right: 24rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.share-btn:active {
  background-color: #e0e0e0;
  opacity: 1;
}

.share-btn image {
  width: 24rpx;
  height: 24rpx;
}

/* 加载状态 */
.load-more,
.loading-more,
.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  margin-top: 20rpx;
}

.load-more-text,
.loading-text,
.no-more-text {
  font-size: 26rpx;
  color: #999;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-image {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
  display: block;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  width: 200rpx;
}

/* 筛选面板 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-mask.show {
  opacity: 1;
  visibility: visible;
}

.filter-panel {
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  width: 600rpx;
  background-color: white;
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.filter-panel.show {
  transform: translateX(0);
}

.filter-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.filter-actions {
  display: flex;
  gap: 30rpx;
}

.filter-reset,
.filter-close {
  font-size: 28rpx;
  color: #4CAF50;
}

.filter-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
  font-weight: 500;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.filter-option {
  padding: 16rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.filter-option.active {
  background-color: #4CAF50;
  color: white;
}

.filter-footer {
  margin-top: auto;
  padding: 30rpx;
}

/* 悬浮按钮 */
.fab-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(76, 175, 80, 0.4);
  z-index: 100;
  transition: all 0.3s ease;
}

.fab-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 12rpx rgba(76, 175, 80, 0.3);
}

.fab-icon {
  font-size: 48rpx;
  color: white;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .recipes-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  .recipe-card {
    display: flex;
    height: 200rpx;
  }
  
  .recipe-image {
    width: 200rpx;
    height: 100%;
    flex-shrink: 0;
  }
  
  .recipe-info {
    flex: 1;
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .recipe-name {
    font-size: 28rpx;
    margin-bottom: 6rpx;
  }
  
  .recipe-desc {
    font-size: 22rpx;
    margin-bottom: 12rpx;
    -webkit-line-clamp: 1;
  }
  
  .recipe-meta {
    gap: 16rpx;
    margin-bottom: 12rpx;
  }
  
  .meta-text {
    font-size: 20rpx;
  }
  
  .share-btn {
    bottom: 20rpx;
    right: 20rpx;
    width: 36rpx;
    height: 36rpx;
  }
  
  .share-btn image {
    width: 20rpx;
    height: 20rpx;
  }
  
  .filter-panel {
    width: 100%;
  }
  
  .fab-btn {
    width: 80rpx;
    height: 80rpx;
    bottom: 30rpx;
    right: 30rpx;
  }
  
  .fab-icon {
    font-size: 40rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .search-section,
  .recipe-card,
  .filter-panel {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .search-input-wrapper {
    background-color: #404040;
  }
  
  .search-input-wrapper:focus-within {
    background-color: #2d2d2d;
  }
  
  .search-input {
    color: white;
  }
  
  .category-item {
    background-color: #404040;
    color: #ccc;
  }
  
  .recipe-name,
  .filter-title,
  .filter-label,
  .empty-text {
    color: white;
  }
  
  .recipe-desc,
  .meta-text,
  .sort-item,
  .empty-desc {
    color: #999;
  }
  
  .favorite-btn {
    background-color: rgba(45, 45, 45, 0.9);
  }
  
  .share-btn {
    background-color: #404040;
  }
  
  .filter-option {
    background-color: #404040;
    color: #ccc;
  }
  
  .filter-section {
    border-color: #404040;
  }
}

/* 动画效果 */
.recipe-card {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 收藏动画 */
.favorite-btn.favorited {
  animation: heartBeat 0.6s ease;
}

@keyframes heartBeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}
/* Emoji图标样式 */
.emoji-icon {
  font-size: 32rpx;
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
}

/* 小尺寸emoji图标 */
.emoji-icon.small {
  font-size: 24rpx;
}

/* 大尺寸emoji图标 */
.emoji-icon.large {
  font-size: 48rpx;
}

/* 图标与文字对齐 */
.emoji-icon.inline {
  margin-right: 8rpx;
}
