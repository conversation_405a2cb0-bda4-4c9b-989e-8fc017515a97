const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 确保上传目录存在
const uploadDir = process.env.UPLOAD_PATH || './uploads';
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // 根据文件类型创建不同的子目录
    let subDir = 'others';
    
    if (file.fieldname === 'avatar') {
      subDir = 'avatars';
    } else if (file.fieldname === 'ingredient_image') {
      subDir = 'ingredients';
    } else if (file.fieldname === 'recipe_image') {
      subDir = 'recipes';
    }
    
    const fullPath = path.join(uploadDir, subDir);
    
    // 确保子目录存在
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
    }
    
    cb(null, fullPath);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const filename = `${file.fieldname}-${uniqueSuffix}${ext}`;
    cb(null, filename);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 检查文件类型
  const allowedTypes = /jpeg|jpg|png|gif|webp/;
  const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = allowedTypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error('只支持上传图片文件 (jpeg, jpg, png, gif, webp)'));
  }
};

// 创建multer实例
const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024, // 默认5MB
    files: 1
  },
  fileFilter: fileFilter
});

// 单文件上传中间件
const uploadSingle = (fieldName) => {
  return (req, res, next) => {
    const singleUpload = upload.single(fieldName);
    
    singleUpload(req, res, (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              success: false,
              message: '文件大小超出限制'
            });
          } else if (err.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
              success: false,
              message: '文件数量超出限制'
            });
          } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
            return res.status(400).json({
              success: false,
              message: '意外的文件字段'
            });
          }
        }
        
        return res.status(400).json({
          success: false,
          message: err.message || '文件上传失败'
        });
      }
      
      // 如果有文件上传，添加文件URL到req.file
      if (req.file) {
        const baseUrl = process.env.BASE_URL || `http://localhost:${process.env.PORT || 3000}`;
        const relativePath = req.file.path.replace(uploadDir, '').replace(/\\/g, '/');
        req.file.url = `${baseUrl}/uploads${relativePath}`;
      }
      
      next();
    });
  };
};

// 多文件上传中间件
const uploadMultiple = (fieldName, maxCount = 5) => {
  return (req, res, next) => {
    const multipleUpload = upload.array(fieldName, maxCount);
    
    multipleUpload(req, res, (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              success: false,
              message: '文件大小超出限制'
            });
          } else if (err.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
              success: false,
              message: '文件数量超出限制'
            });
          }
        }
        
        return res.status(400).json({
          success: false,
          message: err.message || '文件上传失败'
        });
      }
      
      // 如果有文件上传，添加文件URL到req.files
      if (req.files && req.files.length > 0) {
        const baseUrl = process.env.BASE_URL || `http://localhost:${process.env.PORT || 3000}`;
        req.files.forEach(file => {
          const relativePath = file.path.replace(uploadDir, '').replace(/\\/g, '/');
          file.url = `${baseUrl}/uploads${relativePath}`;
        });
      }
      
      next();
    });
  };
};

// 删除文件的工具函数
const deleteFile = (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      return true;
    }
    return false;
  } catch (error) {
    console.error('删除文件错误:', error);
    return false;
  }
};

// 从URL获取文件路径
const getFilePathFromUrl = (fileUrl) => {
  if (!fileUrl) return null;
  
  try {
    const url = new URL(fileUrl);
    const relativePath = url.pathname.replace('/uploads', '');
    return path.join(uploadDir, relativePath);
  } catch (error) {
    console.error('解析文件URL错误:', error);
    return null;
  }
};

// 清理过期文件的工具函数
const cleanupExpiredFiles = (directory, maxAge = 7 * 24 * 60 * 60 * 1000) => {
  try {
    const files = fs.readdirSync(directory);
    const now = Date.now();
    
    files.forEach(file => {
      const filePath = path.join(directory, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isFile() && (now - stats.mtime.getTime()) > maxAge) {
        fs.unlinkSync(filePath);
        console.log(`已清理过期文件: ${filePath}`);
      }
    });
  } catch (error) {
    console.error('清理过期文件错误:', error);
  }
};

// 获取文件信息
const getFileInfo = (filePath) => {
  try {
    if (!fs.existsSync(filePath)) {
      return null;
    }
    
    const stats = fs.statSync(filePath);
    return {
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      isFile: stats.isFile(),
      isDirectory: stats.isDirectory()
    };
  } catch (error) {
    console.error('获取文件信息错误:', error);
    return null;
  }
};

module.exports = {
  upload,
  uploadSingle,
  uploadMultiple,
  deleteFile,
  getFilePathFromUrl,
  cleanupExpiredFiles,
  getFileInfo
};