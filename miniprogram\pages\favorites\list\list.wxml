<!-- 收藏列表页面 -->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">我的收藏</text>
      <view class="header-actions">
        <image wx:if="{{!showSearch}}" class="action-icon" src="/images/icons/search.png" bindtap="showSearch"></image>
        <image class="action-icon" src="✏️" bindtap="toggleEditMode"></image>
      </view>
    </view>
    
    <!-- 搜索栏 -->
    <view wx:if="{{showSearch}}" class="search-bar">
      <view class="search-input-wrapper">
        <image class="search-icon" src="/images/icons/search.png"></image>
        <input class="search-input" placeholder="搜索收藏内容" value="{{searchKeyword}}" bindinput="onSearchInput" bindconfirm="onSearch" />
        <image wx:if="{{searchKeyword}}" class="clear-icon" src="/images/icons/close.png" bindtap="clearSearch"></image>
      </view>
      <text class="search-cancel" bindtap="hideSearch">取消</text>
    </view>
  </view>

  <!-- 分类筛选 -->
  <scroll-view class="category-tabs" scroll-x>
    <view class="tab-list">
      <view wx:for="{{categories}}" wx:key="id" class="tab-item {{currentCategory === item.id ? 'active' : ''}}" bindtap="switchCategory" data-category="{{item.id}}">
        <text class="tab-text">{{item.name}}</text>
      </view>
    </view>
  </scroll-view>

  <!-- 编辑模式工具栏 -->
  <view wx:if="{{editMode}}" class="edit-toolbar">
    <view class="edit-info">
      <text class="selected-count">已选择 {{selectedItems.length}} 项</text>
    </view>
    <view class="edit-actions">
      <button class="edit-btn" bindtap="toggleSelectAll">
        {{selectedItems.length === favorites.length && favorites.length > 0 ? '取消全选' : '全选'}}
      </button>
      <button class="delete-btn" bindtap="batchDelete" disabled="{{selectedItems.length === 0}}">删除</button>
    </view>
  </view>

  <!-- 收藏列表 -->
  <scroll-view class="content-scroll" scroll-y refresher-enabled refresher-triggered="{{refreshing}}" bindrefresherrefresh="refreshData" bindscrolltolower="loadMore">
    <view class="favorites-list">
      <view wx:if="{{loading && favorites.length === 0}}" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <view wx:elif="{{favorites.length === 0}}" class="empty-container">
        <image class="empty-icon" src="/images/empty/favorites.png"></image>
        <text class="empty-title">暂无收藏</text>
        <text class="empty-desc">快去收藏你喜欢的菜谱和食材吧</text>
      </view>

      <view wx:else class="favorite-items">
        <view wx:for="{{favorites}}" wx:key="id" class="favorite-item {{editMode ? 'edit-mode' : ''}}" bindtap="onItemTap" data-item="{{item}}" data-id="{{item.id}}">
          <!-- 选择框 -->
          <view wx:if="{{editMode}}" class="select-checkbox {{selectedItems.indexOf(item.id) > -1 ? 'checked' : ''}}">
            <image wx:if="{{selectedItems.indexOf(item.id) > -1}}" class="check-icon" src="✅"></image>
          </view>

          <!-- 收藏项内容 -->
          <view class="item-content">
            <view class="item-header">
              <text class="type-icon">{{getTypeIcon(item.type)}}</text>
              <view class="item-info">
                <text class="item-title">{{item.title}}</text>
                <view class="item-meta">
                  <text class="item-type">{{getTypeName(item.type)}}</text>
                  <text class="item-time">{{formatTime(item.createdAt)}}</text>
                </view>
              </view>
              <text wx:if="{{!editMode}}" class="remove-icon" bindtap="removeFavorite" data-id="{{item.id}}">❤️</text>
            </view>

            <view wx:if="{{item.description}}" class="item-description">
              <text>{{item.description}}</text>
            </view>

            <view wx:if="{{item.tags && item.tags.length > 0}}" class="item-tags">
              <text wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag" class="tag">{{tag}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view wx:if="{{loadingMore}}" class="loading-more">
        <view class="loading-spinner small"></view>
        <text class="loading-text">加载更多...</text>
      </view>

      <view wx:if="{{!hasMore && favorites.length > 0}}" class="no-more">
        <text>没有更多收藏了</text>
      </view>
    </view>
  </scroll-view>

  <!-- 编辑模式遮罩 -->
  <view wx:if="{{editMode}}" class="edit-mask" bindtap="toggleEditMode"></view>
</view>