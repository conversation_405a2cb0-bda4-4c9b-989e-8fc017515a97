<!-- 购物清单编辑页面 -->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{isEdit ? '加载购物项信息...' : '准备添加购物项...'}}</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="content-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <text class="header-title">{{isEdit ? '编辑购物项' : '添加购物项'}}</text>
        <text class="header-subtitle">{{isEdit ? '修改购物清单中的商品信息' : '添加新的购物商品到清单'}}</text>
      </view>
    </view>

    <!-- 表单内容 -->
    <scroll-view class="form-scroll" scroll-y enhanced show-scrollbar="{{false}}">
      <view class="form-container">
        <!-- 基本信息卡片 -->
        <view class="info-card">
          <view class="card-header">
            <text class="card-icon">🛍️</text>
            <text class="card-title">基本信息</text>
          </view>
          
          <view class="card-content">
            <view class="input-group">
              <view class="input-label">
                <text class="label-text">商品名称</text>
                <text class="required-mark">*</text>
              </view>
              <view class="input-wrapper">
                <input 
                  class="modern-input" 
                  placeholder="请输入商品名称" 
                  value="{{name}}" 
                  bindinput="onNameInput" 
                  maxlength="50"
                  placeholder-class="input-placeholder"
                />
              </view>
            </view>
            
            <view class="input-group">
              <view class="input-label">
                <text class="label-text">商品分类</text>
                <text class="required-mark">*</text>
              </view>
              <view class="selector-wrapper" bindtap="showCategoryPicker">
                <view class="modern-selector">
                  <text class="selector-text {{!category ? 'placeholder' : ''}}">
                    {{getCategoryName(category) || '请选择商品分类'}}
                  </text>
                  <text class="selector-icon">▼</text>
                </view>
              </view>
            </view>
            
            <!-- 购买数量行 -->
            <view class="quantity-row">
              <text class="quantity-label">购买数量 <text class="required-mark">*</text></text>
              <view class="quantity-input-container">
                <input 
                  class="quantity-input" 
                  placeholder="数量" 
                  value="{{amount}}" 
                  bindinput="onAmountInput" 
                  type="digit"
                />
                <view class="unit-selector" bindtap="showUnitPicker">
                  <text class="unit-text">{{unit || '斤'}}</text>
                  <text class="unit-icon">▼</text>
                </view>
              </view>
            </view>
            
            <!-- 价格行 -->
            <view class="price-row">
              <view class="price-column">
                <text class="price-label">单价 (元)</text>
                <view class="price-input-container">
                  <text class="price-symbol">¥</text>
                  <input 
                    class="price-input" 
                    placeholder="0.00" 
                    value="{{price}}" 
                    bindinput="onPriceInput" 
                    type="digit"
                  />
                </view>
              </view>
              
              <view class="price-column">
                <text class="price-label">总价 (自动计算)</text>
                <view class="total-price-container">
                  <text class="price-symbol">¥</text>
                  <text class="total-price-value">{{price && amount ? getTotalPrice() : '0.00'}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 优先级设置卡片 -->
        <view class="info-card">
          <view class="card-header">
            <text class="card-icon">⭐</text>
            <text class="card-title">优先级设置</text>
          </view>
          
          <view class="card-content">
            <view class="input-group">
              <view class="input-label">
                <text class="label-text">购买优先级</text>
              </view>
              <view class="priority-grid">
                <view 
                  wx:for="{{priorities}}" 
                  wx:key="value" 
                  class="priority-option {{priority === item.value ? 'selected' : ''}}" 
                  bindtap="onPriorityChange" 
                  data-index="{{index}}"
                >
                  <view class="priority-indicator" style="background-color: {{item.color}};"></view>
                  <text class="priority-label">{{item.name}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 备注信息卡片 -->
        <view class="info-card">
          <view class="card-header">
            <text class="card-icon">📝</text>
            <text class="card-title">备注信息</text>
          </view>
          
          <view class="card-content">
            <view class="input-group">
              <view class="input-label">
                <text class="label-text">购买备注</text>
                <text class="label-hint">（可选）</text>
              </view>
              <view class="textarea-wrapper">
                <textarea 
                  class="modern-textarea" 
                  placeholder="请输入购买备注，如品牌偏好、规格要求等..." 
                  value="{{notes}}" 
                  bindinput="onNotesInput" 
                  maxlength="200"
                  placeholder-class="input-placeholder"
                  auto-height
                />
                <view class="char-count">{{notes.length || 0}}/200</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="action-bar">
      <view class="action-buttons">
        <button class="action-btn secondary" bindtap="cancel">
          <text class="btn-icon">✖️</text>
          <text class="btn-text">取消</text>
        </button>
        <button wx:if="{{isEdit}}" class="action-btn danger" bindtap="deleteItem" loading="{{saving}}">
          <text class="btn-icon">🗑️</text>
          <text class="btn-text">删除</text>
        </button>
        <button class="action-btn primary" bindtap="saveItem" loading="{{saving}}" disabled="{{!canSave}}">
          <text class="btn-icon">{{isEdit ? '✏️' : '➕'}}</text>
          <text class="btn-text">{{isEdit ? '更新' : '添加'}}</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 分类选择器 -->
  <view wx:if="{{showCategoryPicker}}" class="modal-overlay" bindtap="onCategoryCancel">
    <view class="picker-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">选择商品分类</text>
        <button class="close-btn" bindtap="onCategoryCancel">
          <text class="close-icon">✖️</text>
        </button>
      </view>
      
      <view class="picker-body">
        <picker-view class="modern-picker" bindchange="onCategoryPickerChange" value="{{[categoryIndex]}}">
          <picker-view-column>
            <view wx:for="{{categories}}" wx:key="id" class="picker-option">
              <text class="option-icon">{{item.icon || '📦'}}</text>
              <text class="option-text">{{item.name}}</text>
            </view>
          </picker-view-column>
        </picker-view>
      </view>
      
      <view class="modal-actions">
        <button class="modal-btn secondary" bindtap="onCategoryCancel">取消</button>
        <button class="modal-btn primary" bindtap="onCategoryChange">确定</button>
      </view>
    </view>
  </view>

  <!-- 单位选择器 -->
  <view wx:if="{{showUnitPicker}}" class="modal-overlay" bindtap="onUnitCancel">
    <view class="picker-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">选择数量单位</text>
        <button class="close-btn" bindtap="onUnitCancel">
          <text class="close-icon">✖️</text>
        </button>
      </view>
      
      <view class="picker-body">
        <picker-view class="modern-picker" bindchange="onUnitPickerChange" value="{{[unitIndex]}}">
          <picker-view-column>
            <view wx:for="{{units}}" wx:key="*this" class="picker-option">
              <text class="option-text">{{item}}</text>
            </view>
          </picker-view-column>
        </picker-view>
      </view>
      
      <view class="modal-actions">
        <button class="modal-btn secondary" bindtap="onUnitCancel">取消</button>
        <button class="modal-btn primary" bindtap="onUnitChange">确定</button>
      </view>
    </view>
  </view>
</view>
