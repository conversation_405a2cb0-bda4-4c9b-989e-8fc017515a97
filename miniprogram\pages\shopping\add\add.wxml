<!-- 购物清单添加页面 -->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="header-title">{{isEdit ? '编辑购物项' : '添加购物项'}}</text>
  </view>

  <!-- 表单内容 -->
  <scroll-view class="form-scroll" scroll-y>
    <view class="form-container">
      <!-- 基本信息 -->
      <view class="form-section">
        <text class="section-title">基本信息</text>
        
        <view class="form-item">
          <text class="item-label">商品名称 <text class="required">*</text></text>
          <input class="item-input" placeholder="请输入商品名称" value="{{name}}" bindinput="onNameInput" maxlength="50" />
        </view>
        
        <view class="form-item">
          <text class="item-label">商品分类 <text class="required">*</text></text>
          <view class="selector-item" bindtap="showCategoryPicker">
            <text class="selector-value">{{getCategoryName(category)}}</text>
            <text class="selector-arrow">▼</text>
          </view>
        </view>
        
        <view class="form-row">
          <view class="form-item flex-item">
            <text class="item-label">购买数量 <text class="required">*</text></text>
            <input class="item-input" placeholder="数量" value="{{amount}}" bindinput="onAmountInput" type="digit" />
          </view>
          
          <view class="form-item flex-item">
            <text class="item-label">数量单位 <text class="required">*</text></text>
            <view class="selector-item" bindtap="showUnitPicker">
              <text class="selector-value">{{unit || '请选择'}}</text>
              <text class="selector-arrow">▼</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 优先级设置 -->
      <view class="form-section">
        <text class="section-title">优先级设置</text>
        
        <view class="form-item">
          <text class="item-label">购买优先级</text>
          <view class="priority-selector">
            <view wx:for="{{priorities}}" wx:key="value" class="priority-item {{priority === item.value ? 'active' : ''}}" bindtap="onPriorityChange" data-index="{{index}}">
              <view class="priority-dot" style="background-color: {{item.color}};"></view>
              <text class="priority-text">{{item.name}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 备注信息 -->
      <view class="form-section">
        <text class="section-title">备注信息</text>
        
        <view class="form-item">
          <text class="item-label">购买备注</text>
          <textarea class="item-textarea" placeholder="请输入购买备注（可选）" value="{{notes}}" bindinput="onNotesInput" maxlength="200" />
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="cancel-btn" bindtap="cancel">取消</button>
    <button class="save-btn" bindtap="saveItem" loading="{{loading}}">
      {{isEdit ? '更新' : '添加'}}
    </button>
  </view>

  <!-- 分类选择器遮罩 -->
  <view wx:if="{{showCategoryPicker}}" class="picker-mask" bindtap="onCategoryCancel">
    <view class="picker-container" catchtap="">
      <view class="picker-header">
        <button class="picker-cancel" bindtap="onCategoryCancel">取消</button>
        <text class="picker-title">选择分类</text>
        <button class="picker-confirm" bindtap="onCategoryChange">确定</button>
      </view>
      <picker-view class="picker-content" bindchange="onCategoryPickerChange" value="{{[categoryIndex]}}">
        <picker-view-column>
          <view wx:for="{{categories}}" wx:key="id" class="picker-item">{{item.name}}</view>
        </picker-view-column>
      </picker-view>
    </view>
  </view>

  <!-- 单位选择器遮罩 -->
  <view wx:if="{{showUnitPicker}}" class="picker-mask" bindtap="onUnitCancel">
    <view class="picker-container" catchtap="">
      <view class="picker-header">
        <button class="picker-cancel" bindtap="onUnitCancel">取消</button>
        <text class="picker-title">选择单位</text>
        <button class="picker-confirm" bindtap="onUnitChange">确定</button>
      </view>
      <picker-view class="picker-content" bindchange="onUnitPickerChange" value="{{[unitIndex]}}">
        <picker-view-column>
          <view wx:for="{{units}}" wx:key="*this" class="picker-item">{{item}}</view>
        </picker-view-column>
      </picker-view>
    </view>
  </view>

  <!-- 加载遮罩 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{isEdit ? '更新中...' : '添加中...'}}</text>
    </view>
  </view>
</view>