<!--菜谱分析页面-->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">菜谱分析</text>
      <view class="header-actions">
        <image class="action-icon" src="/images/icons/refresh.png" bindtap="refreshData" />
        <image class="action-icon" src="📤" />
      </view>
    </view>
    
    <!-- 时间范围选择 -->
    <view class="time-range-tabs">
      <view 
        wx:for="{{timeRanges}}" 
        wx:key="id"
        class="time-tab {{timeRange === item.id ? 'active' : ''}}"
        data-range="{{item.id}}"
        bindtap="switchTimeRange"
      >
        {{item.name}}
      </view>
    </view>
  </view>

  <!-- 内容滚动区域 -->
  <scroll-view class="content-scroll" scroll-y refresher-enabled refresher-triggered="{{refreshing}}" bindrefresherrefresh="refreshData">
    <view class="analysis-content">
      
      <!-- 加载状态 -->
      <view wx:if="{{loading}}" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在分析菜谱数据...</text>
      </view>

      <!-- 分析内容 -->
      <view wx:else>
        
        <!-- 总体统计 -->
        <view class="overview-section">
          <view class="section-header">
            <text class="section-title">总体概览</text>
          </view>
          <view class="overview-card">
            <view class="overview-item">
              <text class="overview-number">{{stats.totalRecipes}}</text>
              <text class="overview-label">总菜谱数</text>
            </view>
            <view class="overview-divider"></view>
            <view class="overview-item">
              <text class="overview-number">{{stats.favoriteRecipes}}</text>
              <text class="overview-label">收藏菜谱</text>
            </view>
            <view class="overview-divider"></view>
            <view class="overview-item" bindtap="viewFavoriteRecipes">
              <text class="overview-number">{{calculatePercentage(stats.favoriteRecipes, stats.totalRecipes)}}%</text>
              <text class="overview-label">收藏率</text>
            </view>
          </view>
        </view>

        <!-- 图表类型切换 -->
        <view class="chart-tabs">
          <view 
            wx:for="{{chartTypes}}" 
            wx:key="id"
            class="chart-tab {{chartType === item.id ? 'active' : ''}}"
            data-type="{{item.id}}"
            bindtap="switchChartType"
          >
            {{item.name}}
          </view>
        </view>

        <!-- 分类统计 -->
        <view wx:if="{{chartType === 'category'}}" class="chart-section">
          <view class="section-header">
            <text class="section-title">菜谱分类统计</text>
          </view>
          <view class="chart-container">
            <view wx:if="{{stats.categoryStats.length === 0}}" class="empty-state">
              <image class="empty-icon" src="/images/icons/empty-chart.png" />
              <text class="empty-text">暂无分类数据</text>
            </view>
            <view wx:else>
              <view 
                wx:for="{{stats.categoryStats}}" 
                wx:key="category"
                class="category-item"
                data-category="{{item.category}}"
                bindtap="viewCategoryDetail"
              >
                <view class="category-info">
                  <image class="category-icon" src="{{item.icon}}" />
                  <view class="category-details">
                    <text class="category-name">{{item.name}}</text>
                    <text class="category-count">{{item.count}}道菜谱</text>
                  </view>
                </view>
                <view class="category-progress">
                  <view class="progress-bar">
                    <view class="progress-fill" style="width: {{calculatePercentage(item.count, stats.totalRecipes)}}%"></view>
                  </view>
                  <text class="progress-text">{{calculatePercentage(item.count, stats.totalRecipes)}}%</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 难度分布 -->
        <view wx:if="{{chartType === 'difficulty'}}" class="chart-section">
          <view class="section-header">
            <text class="section-title">难度分布</text>
          </view>
          <view class="chart-container">
            <view wx:if="{{stats.difficultyStats.length === 0}}" class="empty-state">
              <image class="empty-icon" src="/images/icons/empty-chart.png" />
              <text class="empty-text">暂无难度数据</text>
            </view>
            <view wx:else class="difficulty-chart">
              <view 
                wx:for="{{stats.difficultyStats}}" 
                wx:key="difficulty"
                class="difficulty-item"
              >
                <view class="difficulty-circle" style="background-color: {{getDifficultyColor(item.difficulty)}}">
                  <text class="difficulty-number">{{item.count}}</text>
                </view>
                <text class="difficulty-label">{{getDifficultyName(item.difficulty)}}</text>
                <text class="difficulty-percent">{{calculatePercentage(item.count, stats.totalRecipes)}}%</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 用时分析 -->
        <view wx:if="{{chartType === 'time'}}" class="chart-section">
          <view class="section-header">
            <text class="section-title">烹饪用时分析</text>
          </view>
          <view class="chart-container">
            <view wx:if="{{stats.cookingTimeStats.length === 0}}" class="empty-state">
              <image class="empty-icon" src="/images/icons/empty-chart.png" />
              <text class="empty-text">暂无用时数据</text>
            </view>
            <view wx:else>
              <view 
                wx:for="{{stats.cookingTimeStats}}" 
                wx:key="range"
                class="time-item"
              >
                <view class="time-info">
                  <text class="time-range">{{item.range}}</text>
                  <text class="time-count">{{item.count}}道菜谱</text>
                </view>
                <view class="time-progress">
                  <view class="progress-bar">
                    <view class="progress-fill" style="width: {{calculatePercentage(item.count, stats.totalRecipes)}}%"></view>
                  </view>
                  <text class="progress-text">{{calculatePercentage(item.count, stats.totalRecipes)}}%</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 热门菜谱 -->
        <view wx:if="{{chartType === 'popular'}}" class="chart-section">
          <view class="section-header">
            <text class="section-title">热门菜谱排行</text>
          </view>
          <view class="chart-container">
            <view wx:if="{{stats.popularRecipes.length === 0}}" class="empty-state">
              <image class="empty-icon" src="/images/icons/empty-recipe.png" />
              <text class="empty-text">暂无热门菜谱</text>
            </view>
            <view wx:else>
              <view 
                wx:for="{{stats.popularRecipes}}" 
                wx:key="id"
                class="popular-item"
                data-id="{{item.id}}"
                bindtap="viewRecipeDetail"
              >
                <view class="popular-rank">{{index + 1}}</view>
                <image class="popular-image" src="{{item.image}}" mode="aspectFill" />
                <view class="popular-info">
                  <text class="popular-name">{{item.name}}</text>
                  <view class="popular-meta">
                    <text class="popular-difficulty">{{getDifficultyName(item.difficulty)}}</text>
                    <text class="popular-time">{{formatCookingTime(item.cookingTime)}}</text>
                    <text class="popular-views">{{item.viewCount}}次查看</text>
                  </view>
                </view>
                <view class="popular-score">
                  <text class="score-number">{{item.rating}}</text>
                  <text class="score-label">评分</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 最近活动 -->
        <view class="activity-section">
          <view class="section-header">
            <text class="section-title">最近活动</text>
          </view>
          <view class="activity-container">
            <view wx:if="{{stats.recentActivity.length === 0}}" class="empty-state">
              <image class="empty-icon" src="/images/icons/empty-activity.png" />
              <text class="empty-text">暂无最近活动</text>
            </view>
            <view wx:else>
              <view 
                wx:for="{{stats.recentActivity}}" 
                wx:key="id"
                class="activity-item"
              >
                <view class="activity-icon">
                  <image src="{{item.icon}}" />
                </view>
                <view class="activity-content">
                  <text class="activity-title">{{item.title}}</text>
                  <text class="activity-desc">{{item.description}}</text>
                  <text class="activity-time">{{formatDate(item.createdAt)}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

      </view>
    </view>
  </scroll-view>
</view>