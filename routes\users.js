const express = require('express');
const bcrypt = require('bcryptjs');
const { query } = require('../config/database');
const { generateToken, authenticateToken } = require('../middleware/auth');
const { uploadSingle } = require('../middleware/upload');

const router = express.Router();

// 用户注册
router.post('/register', async (req, res) => {
  try {
    const { phone, password, nickname } = req.body;

    // 验证必填字段
    if (!phone || !password) {
      return res.status(400).json({
        success: false,
        message: '手机号和密码不能为空'
      });
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return res.status(400).json({
        success: false,
        message: '手机号格式不正确'
      });
    }

    // 检查手机号是否已存在
    const existingUsers = await query(
      'SELECT id FROM users WHERE phone = ? AND is_deleted = 0',
      [phone]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: '手机号已被注册'
      });
    }

    // 加密密码
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const result = await query(
      'INSERT INTO users (phone, password, nickname) VALUES (?, ?, ?)',
      [phone, hashedPassword, nickname || `用户${phone.slice(-4)}`]
    );

    // 生成token
    const token = generateToken(result.insertId);

    // 获取用户信息
    const newUser = await query(
      'SELECT id, phone, nickname, avatar, created_at FROM users WHERE id = ?',
      [result.insertId]
    );

    res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        token,
        user: newUser[0]
      }
    });
  } catch (error) {
    console.error('用户注册错误:', error);
    res.status(500).json({
      success: false,
      message: '注册失败'
    });
  }
});

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { phone, password } = req.body;

    // 开发环境支持：如果没有提供手机号和密码，使用默认测试用户
    if (process.env.NODE_ENV === 'development' && (!phone || !password)) {
      console.log('开发环境：使用默认测试用户登录');
      
      // 查找或创建默认测试用户
      let users = await query(
        'SELECT id, phone, nickname, avatar, status FROM users WHERE phone = ? AND is_deleted = 0',
        ['13800138000']
      );

      if (users.length === 0) {
        // 创建默认测试用户
        const hashedPassword = await bcrypt.hash('123456', 10);
        const result = await query(
          'INSERT INTO users (phone, password, nickname) VALUES (?, ?, ?)',
          ['13800138000', hashedPassword, '测试用户']
        );

        users = await query(
          'SELECT id, phone, nickname, avatar, status FROM users WHERE id = ?',
          [result.insertId]
        );
      }

      const user = users[0];
      const token = generateToken(user.id);

      return res.json({
        success: true,
        message: '开发环境登录成功',
        data: {
          token,
          user: user
        }
      });
    }

    if (!phone || !password) {
      return res.status(400).json({
        success: false,
        message: '手机号和密码不能为空'
      });
    }

    // 查找用户
    const users = await query(
      'SELECT id, phone, password, nickname, avatar, status FROM users WHERE phone = ? AND is_deleted = 0',
      [phone]
    );

    if (users.length === 0) {
      return res.status(401).json({
        success: false,
        message: '手机号或密码错误'
      });
    }

    const user = users[0];

    // 检查用户状态
    if (!user.status) {
      return res.status(403).json({
        success: false,
        message: '账号已被禁用'
      });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '手机号或密码错误'
      });
    }

    // 生成token
    const token = generateToken(user.id);

    // 更新最后登录时间
    await query(
      'UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [user.id]
    );

    // 返回用户信息（不包含密码）
    const { password: _, ...userInfo } = user;

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: userInfo
      }
    });
  } catch (error) {
    console.error('用户登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
});

// 微信登录
router.post('/wx-login', async (req, res) => {
  try {
    const { code, userInfo } = req.body;

    // 开发环境支持：如果没有提供code，使用模拟登录
    if (process.env.NODE_ENV === 'development' && !code) {
      console.log('开发环境：使用模拟微信登录');
      
      const mockOpenid = 'dev_wx_user_001';
      
      // 查找或创建默认微信用户
      let users = await query(
        'SELECT id, phone, nickname, avatar, wx_openid FROM users WHERE wx_openid = ? AND is_deleted = 0',
        [mockOpenid]
      );

      if (users.length === 0) {
        // 创建默认微信用户
        const result = await query(
          'INSERT INTO users (nickname, avatar, wx_openid) VALUES (?, ?, ?)',
          ['开发测试用户', null, mockOpenid]
        );

        users = await query(
          'SELECT id, phone, nickname, avatar, wx_openid FROM users WHERE id = ?',
          [result.insertId]
        );
      }

      const user = users[0];
      const token = generateToken(user.id);

      return res.json({
        success: true,
        message: '开发环境微信登录成功',
        data: {
          token,
          user: user,
          isNewUser: false
        }
      });
    }

    if (!code) {
      return res.status(400).json({
        success: false,
        message: '微信授权码不能为空'
      });
    }

    // 这里应该调用微信API获取openid
    // 为了演示，我们模拟一个openid
    const mockOpenid = `mock_openid_${Date.now()}`;

    // 查找是否已有该微信用户
    let users = await query(
      'SELECT id, phone, nickname, avatar, wx_openid FROM users WHERE wx_openid = ? AND is_deleted = 0',
      [mockOpenid]
    );

    let userId;
    let isNewUser = false;

    if (users.length === 0) {
      // 新用户，创建账号
      const result = await query(
        'INSERT INTO users (nickname, avatar, wx_openid) VALUES (?, ?, ?)',
        [
          userInfo?.nickName || '微信用户',
          userInfo?.avatarUrl || null,
          mockOpenid
        ]
      );
      userId = result.insertId;
      isNewUser = true;

      // 获取新创建的用户信息
      users = await query(
        'SELECT id, phone, nickname, avatar, wx_openid FROM users WHERE id = ?',
        [userId]
      );
    } else {
      userId = users[0].id;
      
      // 更新用户信息
      if (userInfo) {
        await query(
          'UPDATE users SET nickname = ?, avatar = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [userInfo.nickName || users[0].nickname, userInfo.avatarUrl || users[0].avatar, userId]
        );
        
        // 重新获取更新后的用户信息
        users = await query(
          'SELECT id, phone, nickname, avatar, wx_openid FROM users WHERE id = ?',
          [userId]
        );
      }
    }

    // 生成token
    const token = generateToken(userId);

    res.json({
      success: true,
      message: isNewUser ? '注册成功' : '登录成功',
      data: {
        token,
        user: users[0],
        isNewUser
      }
    });
  } catch (error) {
    console.error('微信登录错误:', error);
    res.status(500).json({
      success: false,
      message: '微信登录失败'
    });
  }
});

// 获取用户信息
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const users = await query(
      'SELECT id, phone, nickname, avatar, wx_openid, created_at FROM users WHERE id = ?',
      [req.user.id]
    );

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: users[0]
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    });
  }
});

// 更新用户信息
router.put('/profile', authenticateToken, async (req, res) => {
  try {
    const { nickname, phone } = req.body;
    const updateFields = [];
    const updateValues = [];

    if (nickname) {
      updateFields.push('nickname = ?');
      updateValues.push(nickname);
    }

    if (phone) {
      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(phone)) {
        return res.status(400).json({
          success: false,
          message: '手机号格式不正确'
        });
      }

      // 检查手机号是否已被其他用户使用
      const existingUsers = await query(
        'SELECT id FROM users WHERE phone = ? AND id != ? AND is_deleted = 0',
        [phone, req.user.id]
      );

      if (existingUsers.length > 0) {
        return res.status(400).json({
          success: false,
          message: '手机号已被其他用户使用'
        });
      }

      updateFields.push('phone = ?');
      updateValues.push(phone);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有需要更新的字段'
      });
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(req.user.id);

    await query(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // 获取更新后的用户信息
    const updatedUser = await query(
      'SELECT id, phone, nickname, avatar, created_at FROM users WHERE id = ?',
      [req.user.id]
    );

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: updatedUser[0]
    });
  } catch (error) {
    console.error('更新用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '更新用户信息失败'
    });
  }
});

// 上传头像
router.post('/avatar', authenticateToken, uploadSingle('avatar'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的头像文件'
      });
    }

    // 更新用户头像
    await query(
      'UPDATE users SET avatar = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [req.file.url, req.user.id]
    );

    res.json({
      success: true,
      message: '头像上传成功',
      data: {
        avatar: req.file.url
      }
    });
  } catch (error) {
    console.error('上传头像错误:', error);
    res.status(500).json({
      success: false,
      message: '头像上传失败'
    });
  }
});

// 修改密码
router.put('/password', authenticateToken, async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;

    if (!oldPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: '旧密码和新密码不能为空'
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: '新密码长度不能少于6位'
      });
    }

    // 获取用户当前密码
    const users = await query(
      'SELECT password FROM users WHERE id = ?',
      [req.user.id]
    );

    if (users.length === 0 || !users[0].password) {
      return res.status(400).json({
        success: false,
        message: '用户未设置密码，请联系管理员'
      });
    }

    // 验证旧密码
    const isOldPasswordValid = await bcrypt.compare(oldPassword, users[0].password);
    if (!isOldPasswordValid) {
      return res.status(400).json({
        success: false,
        message: '旧密码不正确'
      });
    }

    // 加密新密码
    const saltRounds = 10;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // 更新密码
    await query(
      'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [hashedNewPassword, req.user.id]
    );

    res.json({
      success: true,
      message: '密码修改成功'
    });
  } catch (error) {
    console.error('修改密码错误:', error);
    res.status(500).json({
      success: false,
      message: '密码修改失败'
    });
  }
});

// 注销账号
router.delete('/account', authenticateToken, async (req, res) => {
  try {
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({
        success: false,
        message: '请输入密码确认注销'
      });
    }

    // 获取用户密码
    const users = await query(
      'SELECT password FROM users WHERE id = ?',
      [req.user.id]
    );

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 验证密码
    if (users[0].password) {
      const isPasswordValid = await bcrypt.compare(password, users[0].password);
      if (!isPasswordValid) {
        return res.status(400).json({
          success: false,
          message: '密码不正确'
        });
      }
    }

    // 软删除用户账号
    await query(
      'UPDATE users SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [req.user.id]
    );

    res.json({
      success: true,
      message: '账号注销成功'
    });
  } catch (error) {
    console.error('注销账号错误:', error);
    res.status(500).json({
      success: false,
      message: '账号注销失败'
    });
  }
});

module.exports = router;