/* 购物清单编辑页面样式 - 精心设计版 */

:root {
  --primary-color: #4f46e5;
  --primary-light: #6366f1;
  --primary-dark: #3730a3;
  --secondary-color: #06b6d4;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --border-color: #e5e7eb;
  --border-focus: #3b82f6;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 24rpx;
  --spacing-xs: 8rpx;
  --spacing-sm: 12rpx;
  --spacing-md: 16rpx;
  --spacing-lg: 24rpx;
  --spacing-xl: 32rpx;
  --spacing-2xl: 48rpx;
}

.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  position: relative;
  overflow: hidden;
}

.page-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

/* 加载状态 - 精美设计 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: var(--spacing-xl);
  position: relative;
  z-index: 10;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  position: relative;
}

.loading-spinner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 6rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.loading-spinner::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 6rpx solid transparent;
  border-top: 6rpx solid #fff;
  border-radius: 50%;
  animation: spin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

.loading-text {
  color: rgba(255, 255, 255, 0.95);
  font-size: 30rpx;
  font-weight: 500;
  text-align: center;
  letter-spacing: 0.5rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 主要内容容器 */
.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

/* 页面头部 - 精美设计 */
.page-header {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  padding: 60rpx var(--spacing-xl) var(--spacing-2xl);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.header-content {
  text-align: center;
  position: relative;
  z-index: 2;
}

.header-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: var(--spacing-sm);
  display: block;
  letter-spacing: 0.5rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  animation: slideInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  font-weight: 400;
  letter-spacing: 0.3rpx;
  animation: slideInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.1s both;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单滚动区域 - 优雅设计 */
.form-scroll {
  flex: 1;
  padding: var(--spacing-lg);
  padding-top: 0;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  padding-bottom: 160rpx; /* 为底部操作栏留出空间 */
  animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

/* 信息卡片 - 精美设计 */
.info-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow:
    0 20rpx 40rpx rgba(0, 0, 0, 0.08),
    0 8rpx 16rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg,
    var(--primary-color) 0%,
    var(--secondary-color) 50%,
    var(--primary-color) 100%);
}

.info-card:hover {
  transform: translateY(-4rpx);
  box-shadow:
    0 32rpx 64rpx rgba(0, 0, 0, 0.12),
    0 16rpx 32rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.8) 0%,
    rgba(241, 245, 249, 0.8) 100%);
  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
  position: relative;
}

.card-icon {
  font-size: 36rpx;
  line-height: 1;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.card-title {
  font-size: 32rpx;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: 0.3rpx;
}

.card-content {
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.5);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 输入组件 - 精致设计 */
.input-group {
  margin-bottom: var(--spacing-xl);
  position: relative;
}

.input-group:last-child {
  margin-bottom: 0;
}

/* 标签水平对齐 */
.input-label {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
  gap: var(--spacing-xs);
  min-height: 44rpx;
  height: 44rpx; /* 固定高度确保对齐 */
}

.label-text {
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: 600;
  letter-spacing: 0.2rpx;
}

.required-mark {
  color: var(--error-color);
  font-size: 26rpx;
  font-weight: 700;
  animation: pulse 2s infinite;
  margin-left: 4rpx;
}

.label-hint {
  color: var(--text-muted);
  font-size: 26rpx;
  font-weight: 400;
  font-style: italic;
}

.input-wrapper {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-input {
  width: 100%;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(0, 0, 0, 0.08);
  border-radius: var(--radius-lg);
  padding: 0 var(--spacing-lg);
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: 500;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  position: relative;
  display: flex;
  align-items: center;
  line-height: 1;
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.modern-input:focus {
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 1);
  box-shadow:
    0 0 0 8rpx rgba(79, 70, 229, 0.08),
    0 8rpx 24rpx rgba(0, 0, 0, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 1);
  transform: translateY(-2rpx);
  outline: none;
}

.modern-input::placeholder {
  color: var(--text-muted);
  font-weight: 400;
  transition: all 0.3s ease;
}

.modern-input:focus::placeholder {
  color: transparent;
  transform: translateY(-4rpx);
}

.input-placeholder {
  color: var(--text-muted);
  font-weight: 400;
}

/* 选择器样式 - 精美设计 */
.selector-wrapper {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-selector {
  width: 100%;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(0, 0, 0, 0.08);
  border-radius: var(--radius-lg);
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  cursor: pointer;
  position: relative;
  line-height: 1;
  min-height: 96rpx;
  max-height: 96rpx;
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.modern-selector::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(0, 0, 0, 0.02) 100%);
  border-radius: var(--radius-lg);
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.modern-selector:active {
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 1);
  box-shadow:
    0 0 0 8rpx rgba(79, 70, 229, 0.08),
    0 8rpx 24rpx rgba(0, 0, 0, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 1);
  transform: translateY(-2rpx);
}

.modern-selector:active::before {
  opacity: 0;
}

.selector-text {
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: 500;
  flex: 1;
  letter-spacing: 0.2rpx;
}

.selector-text.placeholder {
  color: var(--text-muted);
  font-weight: 400;
  font-style: italic;
}

.selector-icon {
  font-size: 28rpx;
  color: var(--text-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.modern-selector:active .selector-icon {
  color: var(--primary-color);
  transform: rotate(180deg) scale(1.1);
}

/* 输入行布局 - 完美对齐 */
.input-row {
  display: flex;
  gap: var(--spacing-md);
  align-items: stretch; /* 改为拉伸对齐 */
  margin-bottom: var(--spacing-xl); /* 确保与单行输入组件间距一致 */
}

.half-width {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

/* 确保输入行中的标签高度一致 */
.input-row .input-group {
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.input-row .input-label {
  height: 44rpx; /* 固定标签高度 */
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

/* 输入框水平对齐 */
.input-wrapper, 
.selector-wrapper {
  flex: 1;
  height: 96rpx;
}

.input-row .modern-input,
.input-row .modern-selector {
  flex: 1;
  height: 96rpx !important; /* 强制统一高度 */
  min-height: 96rpx !important;
  max-height: 96rpx !important;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

/* 价格汇总样式 - 精美设计 */
.price-summary {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.08) 0%,
    rgba(6, 182, 212, 0.08) 100%);
  border: 2rpx solid rgba(16, 185, 129, 0.15);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  margin-top: var(--spacing-xl); /* 增加与上方元素的间距 */
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8rpx 32rpx rgba(16, 185, 129, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
}

.price-summary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg,
    var(--success-color) 0%,
    var(--secondary-color) 100%);
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  transition: all 0.3s ease;
  height: 60rpx; /* 统一高度 */
}

.price-row:last-child {
  margin-bottom: 0;
}

.price-row.total {
  padding-top: var(--spacing-lg);
  border-top: 1rpx solid rgba(16, 185, 129, 0.2);
  margin-top: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.4);
  margin-left: calc(-1 * var(--spacing-xl));
  margin-right: calc(-1 * var(--spacing-xl));
  padding-left: var(--spacing-xl);
  padding-right: var(--spacing-xl);
  padding-bottom: var(--spacing-sm);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  height: 70rpx; /* 总价行稍高一些 */
}

.price-label {
  font-size: 30rpx;
  color: var(--text-secondary);
  font-weight: 600;
  letter-spacing: 0.2rpx;
}

.price-row.total .price-label {
  font-weight: 700;
  color: var(--text-primary);
  font-size: 32rpx;
}

/* 总价显示样式 */
.total-price-display {
  display: flex;
  align-items: center;
}

.total-price {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: var(--spacing-lg);
  background: rgba(16, 185, 129, 0.05);
  border-color: rgba(16, 185, 129, 0.2);
}

.price-value {
  font-size: 32rpx;
  color: var(--success-color);
  font-weight: 700;
  font-family: 'SF Mono', 'Monaco', 'Consolas', 'Menlo', monospace;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  min-width: 120rpx; /* 确保价格显示区域宽度一致 */
  text-align: right; /* 价格右对齐 */
}

.price-value.total {
  font-size: 36rpx;
  color: var(--success-color);
  font-weight: 800;
  text-shadow: 0 2rpx 4rpx rgba(16, 185, 129, 0.2);
  min-width: 140rpx; /* 总价显示区域稍宽 */
}

/* 底部操作栏 - 精美设计 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(30rpx);
  -webkit-backdrop-filter: blur(30rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
  padding: var(--spacing-xl) var(--spacing-xl);
  padding-bottom: calc(var(--spacing-xl) + env(safe-area-inset-bottom));
  z-index: 1000;
  box-shadow:
    0 -8rpx 32rpx rgba(0, 0, 0, 0.08),
    0 -4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

/* 编辑模式下的按钮布局调整 */
.action-buttons .action-btn.secondary {
  flex: 0.8;
}

.action-buttons .action-btn.danger {
  flex: 0.8;
}

.action-buttons .action-btn.primary {
  flex: 1.4;
}

.action-btn {
  flex: 1;
  height: 100rpx;
  border-radius: var(--radius-xl);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  font-size: 32rpx;
  font-weight: 700;
  letter-spacing: 0.3rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  box-shadow: var(--shadow-md);
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 50%,
    rgba(0, 0, 0, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-btn:active::before {
  opacity: 1;
}

.action-btn:active::after {
  width: 300%;
  height: 300%;
}

.action-btn.secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: #ffffff;
  box-shadow:
    0 8rpx 24rpx rgba(107, 114, 128, 0.25),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
}

.action-btn.secondary:active {
  background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
  transform: translateY(2rpx) scale(0.98);
}

.action-btn.danger {
  background: linear-gradient(135deg, var(--error-color) 0%, #dc2626 100%);
  color: #ffffff;
  box-shadow:
    0 12rpx 32rpx rgba(239, 68, 68, 0.3),
    0 6rpx 16rpx rgba(239, 68, 68, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.action-btn.danger:active {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(2rpx) scale(0.98);
  box-shadow:
    0 6rpx 16rpx rgba(239, 68, 68, 0.4),
    0 3rpx 8rpx rgba(239, 68, 68, 0.3);
}

.action-btn.primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: #ffffff;
  box-shadow:
    0 12rpx 32rpx rgba(79, 70, 229, 0.3),
    0 6rpx 16rpx rgba(79, 70, 229, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.action-btn.primary:active {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
  transform: translateY(2rpx) scale(0.98);
  box-shadow:
    0 6rpx 16rpx rgba(79, 70, 229, 0.4),
    0 3rpx 8rpx rgba(79, 70, 229, 0.3);
}

.action-btn:disabled {
  opacity: 0.5;
  transform: none;
  cursor: not-allowed;
  box-shadow: var(--shadow-sm);
}

.action-btn:disabled::before,
.action-btn:disabled::after {
  display: none;
}

.btn-icon {
  font-size: 32rpx;
  line-height: 1;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.btn-text {
  font-size: 32rpx;
  font-weight: 700;
  letter-spacing: 0.3rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 模态框样式 - 精美设计 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
  -webkit-backdrop-filter: blur(8rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: modalFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.picker-modal {
  width: 92%;
  max-width: 640rpx;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow:
    0 32rpx 64rpx rgba(0, 0, 0, 0.2),
    0 16rpx 32rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  animation: modalSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-header {
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.9) 0%,
    rgba(241, 245, 249, 0.9) 100%);
  padding: var(--spacing-2xl) var(--spacing-xl) var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  position: relative;
}

.modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg,
    var(--primary-color) 0%,
    var(--secondary-color) 100%);
}

.modal-title {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: 0.3rpx;
}

.close-btn {
  width: 72rpx;
  height: 72rpx;
  background: rgba(0, 0, 0, 0.04);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow:
    0 4rpx 12rpx rgba(0, 0, 0, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
}

.close-btn:active {
  background: rgba(0, 0, 0, 0.08);
  transform: scale(0.92);
  box-shadow:
    0 2rpx 6rpx rgba(0, 0, 0, 0.12),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
}

.close-icon {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 600;
}

.picker-body {
  padding: var(--spacing-lg) 0;
  background: rgba(255, 255, 255, 0.5);
}

.modern-picker {
  height: 480rpx;
}

.picker-option {
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  padding: 0 var(--spacing-2xl);
  transition: all 0.3s ease;
}

.picker-option:active {
  background: rgba(79, 70, 229, 0.05);
}

.option-icon {
  font-size: 32rpx;
  line-height: 1;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.option-text {
  font-size: 32rpx;
  color: var(--text-primary);
  font-weight: 500;
  letter-spacing: 0.2rpx;
}

.modal-actions {
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.9) 0%,
    rgba(241, 245, 249, 0.9) 100%);
  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
  display: flex;
  gap: var(--spacing-md);
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: var(--radius-lg);
  border: none;
  font-size: 30rpx;
  font-weight: 600;
  letter-spacing: 0.2rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.modal-btn.secondary {
  background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
  color: var(--text-secondary);
  box-shadow:
    0 4rpx 12rpx rgba(0, 0, 0, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.modal-btn.secondary:active {
  background: linear-gradient(135deg, #d1d5db 0%, #9ca3af 100%);
  transform: translateY(1rpx) scale(0.98);
}

.modal-btn.primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: #ffffff;
  box-shadow:
    0 8rpx 24rpx rgba(79, 70, 229, 0.25),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.modal-btn.primary:active {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
  transform: translateY(1rpx) scale(0.98);
  box-shadow:
    0 4rpx 12rpx rgba(79, 70, 229, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
}

/* 动画效果 - 精美设计 */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8rpx);
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(60rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -6rpx, 0);
  }
  70% {
    transform: translate3d(0, -3rpx, 0);
  }
  90% {
    transform: translate3d(0, -1rpx, 0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2rpx);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2rpx);
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .form-container {
    gap: var(--spacing-md);
  }

  .info-card {
    border-radius: var(--radius-lg);
  }

  .card-content {
    padding: var(--spacing-lg);
  }

  .priority-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-btn {
    height: 88rpx;
    font-size: 28rpx;
  }

  .btn-icon {
    font-size: 28rpx;
  }

  .btn-text {
    font-size: 28rpx;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --bg-primary: #1f2937;
    --bg-secondary: #111827;
    --bg-tertiary: #374151;
    --border-color: #374151;
  }

  .page-container {
    background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #581c87 100%);
  }

  .info-card {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(55, 65, 81, 0.3);
  }

  .card-header {
    background: linear-gradient(135deg,
      rgba(55, 65, 81, 0.8) 0%,
      rgba(75, 85, 99, 0.8) 100%);
  }

  .modern-input,
  .modern-selector,
  .modern-textarea {
    background: rgba(55, 65, 81, 0.8);
    border-color: rgba(75, 85, 99, 0.5);
    color: var(--text-primary);
  }

  .modern-input:focus,
  .modern-selector:active,
  .modern-textarea:focus {
    background: rgba(55, 65, 81, 1);
    border-color: var(--primary-light);
  }

  .priority-option {
    background: rgba(55, 65, 81, 0.8);
    border-color: rgba(75, 85, 99, 0.5);
  }

  .priority-option.selected {
    background: rgba(79, 70, 229, 0.2);
    border-color: var(--primary-light);
  }

  .picker-modal {
    background: rgba(31, 41, 55, 0.98);
  }

  .modal-header,
  .modal-actions {
    background: linear-gradient(135deg,
      rgba(55, 65, 81, 0.9) 0%,
      rgba(75, 85, 99, 0.9) 100%);
  }
}

/* 购买数量和价格区域水平对齐 */
.quantity-price-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

/* 确保单价和总价水平对齐 */
.price-input-row {
  display: flex;
  gap: var(--spacing-md);
  align-items: stretch;
  margin-bottom: var(--spacing-xl);
}

.price-input-group {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

/* 标签水平对齐 */
.input-label {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
  gap: var(--spacing-xs);
  min-height: 44rpx;
  height: 44rpx; /* 固定高度确保对齐 */
}

/* 输入框水平对齐 */
.input-wrapper, 
.selector-wrapper {
  flex: 1;
  height: 96rpx;
}

/* 价格输入组样式 */
.price-input-group .input-wrapper,
.price-input-group .selector-wrapper {
  height: 96rpx;
  display: flex;
  align-items: center;
}

.price-input-group .modern-input,
.price-input-group .modern-selector {
  width: 100%;
  height: 96rpx;
  min-height: 96rpx;
  max-height: 96rpx;
  display: flex;
  align-items: center;
}

/* 总价显示样式 */
.total-price-display {
  display: flex;
  align-items: center;
}

.total-price {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: var(--spacing-lg);
  background: rgba(16, 185, 129, 0.05);
  border-color: rgba(16, 185, 129, 0.2);
}

/* 单价和总价区域水平对齐 */
.price-fields {
  display: flex;
  gap: var(--spacing-md);
}

.price-field {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 单价和总价标签水平对齐 */
.price-label-row {
  display: flex;
  justify-content: space-between;
  height: 44rpx;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

/* 单价和总价输入框水平对齐 */
.price-value-row {
  display: flex;
  justify-content: space-between;
  height: 96rpx;
  align-items: center;
}

/* 购买数量行样式 */
.quantity-row {
  display: flex;
  flex-direction: column;
  margin-bottom: 30rpx;
}

.quantity-label {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.quantity-input-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 96rpx;
}

.quantity-input {
  flex: 1;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(0, 0, 0, 0.08);
  border-radius: var(--radius-lg) 0 0 var(--radius-lg);
  padding: 0 var(--spacing-lg);
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: 500;
  box-sizing: border-box;
}

.unit-selector {
  width: 200rpx;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(0, 0, 0, 0.08);
  border-left: none;
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  box-sizing: border-box;
}

.unit-text {
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: 500;
}

.unit-icon {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 价格行样式 */
.price-row {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.price-column {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.price-label {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.price-input-container,
.total-price-container {
  height: 96rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(0, 0, 0, 0.08);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-lg);
  box-sizing: border-box;
}

.total-price-container {
  background: rgba(16, 185, 129, 0.05);
  border-color: rgba(16, 185, 129, 0.2);
}

.price-symbol {
  font-size: 30rpx;
  color: var(--text-primary);
  margin-right: 8rpx;
}

.price-input {
  flex: 1;
  height: 100%;
  border: none;
  background: transparent;
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: 500;
}

.total-price-value {
  font-size: 36rpx;
  color: var(--success-color);
  font-weight: 800;
  font-family: 'SF Mono', 'Monaco', 'Consolas', 'Menlo', monospace;
}
