/* 购物清单添加页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx;
  color: white;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
}

/* 表单滚动区域 */
.form-scroll {
  flex: 1;
  padding: 20rpx;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 表单区块 */
.form-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.required {
  color: #ff4757;
  font-size: 28rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.item-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
  display: block;
}

.item-input {
  width: 100%;
  height: 80rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.item-input:focus {
  border-color: #667eea;
  background-color: white;
}

.item-textarea {
  width: 100%;
  min-height: 120rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.item-textarea:focus {
  border-color: #667eea;
  background-color: white;
}

/* 表单行 */
.form-row {
  display: flex;
  gap: 20rpx;
}

.flex-item {
  flex: 1;
}

/* 选择器 */
.selector-item {
  width: 100%;
  height: 80rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.selector-item:active {
  border-color: #667eea;
  background-color: rgba(102, 126, 234, 0.05);
}

.selector-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.selector-arrow {
  font-size: 24rpx;
  color: #999;
  opacity: 0.8;
  transition: transform 0.3s ease;
  line-height: 1;
}

.selector-item:active .selector-arrow {
  transform: rotate(180deg);
}

/* 优先级选择器 */
.priority-selector {
  display: flex;
  gap: 20rpx;
}

.priority-item {
  flex: 1;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.priority-item:active {
  transform: scale(0.98);
}

.priority-item.active {
  border-color: #667eea;
  background-color: rgba(102, 126, 234, 0.1);
}

.priority-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
}

.priority-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 选择器遮罩 */
.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.picker-container {
  background-color: white;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  max-height: 60vh;
  overflow: hidden;
}

.picker-header {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-cancel,
.picker-confirm {
  background: none;
  border: none;
  font-size: 28rpx;
  padding: 0;
}

.picker-cancel {
  color: #6c757d;
}

.picker-confirm {
  color: #667eea;
  font-weight: 500;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-content {
  height: 400rpx;
}

.picker-item {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
}

/* 底部操作栏 */
.bottom-actions {
  background-color: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #e0e0e0;
  display: flex;
  gap: 20rpx;
}

.cancel-btn {
  flex: 1;
  background-color: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

.save-btn {
  flex: 2;
  background-color: #667eea;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.save-btn:active {
  background-color: #5a6fd8;
  transform: scale(0.98);
}

.save-btn[loading] {
  opacity: 0.7;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-container {
  background-color: white;
  border-radius: 16rpx;
  padding: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .page-header {
    padding: 15rpx 20rpx;
  }
  
  .header-title {
    font-size: 32rpx;
  }
  
  .form-scroll {
    padding: 15rpx;
  }
  
  .form-section {
    padding: 20rpx;
  }
  
  .section-title {
    font-size: 28rpx;
  }
  
  .form-row {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .priority-selector {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .priority-item {
    flex-direction: row;
    justify-content: center;
  }
  
  .bottom-actions {
    padding: 15rpx 20rpx;
    gap: 15rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .form-section,
  .bottom-actions,
  .loading-container,
  .picker-container {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .section-title,
  .item-label,
  .selector-value,
  .priority-text,
  .picker-title {
    color: white;
  }
  
  .item-input,
  .item-textarea,
  .selector-item,
  .priority-item {
    background-color: #404040;
    border-color: #555;
    color: white;
  }
  
  .item-input:focus,
  .item-textarea:focus {
    background-color: #555;
    border-color: #667eea;
  }
  
  .selector-item:active,
  .priority-item:active {
    background-color: rgba(102, 126, 234, 0.1);
  }
  
  .priority-item.active {
    background-color: rgba(102, 126, 234, 0.2);
  }
  
  .cancel-btn {
    background-color: #404040;
    color: #ccc;
    border-color: #555;
  }
  
  .cancel-btn:active {
    background-color: #555;
  }
  
  .picker-cancel {
    color: #999;
  }
  
  .picker-confirm {
    color: #667eea;
  }
  
  .picker-item {
    color: white;
  }
  
  .loading-text {
    color: #ccc;
  }
}

/* 动画效果 */
.form-section {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.priority-item {
  animation: fadeInScale 0.3s ease;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.picker-container {
  animation: slideInUp 0.3s ease;
}

.loading-container {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}