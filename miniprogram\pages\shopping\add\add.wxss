/* 购物清单编辑页面样式 */

.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: #fff;
  font-size: 28rpx;
  opacity: 0.9;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 主要内容 */
.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  padding: 40rpx 30rpx 30rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.header-content {
  text-align: center;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8rpx;
  display: block;
}

.header-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* 表单滚动区域 */
.form-scroll {
  flex: 1;
  padding: 20rpx;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 信息卡片 */
.info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.card-icon {
  font-size: 32rpx;
  line-height: 1;
}

.card-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.card-content {
  padding: 30rpx;
}

.required {
  color: #ff4757;
  font-size: 28rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.item-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
  display: block;
}

.item-input {
  width: 100%;
  height: 80rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.item-input:focus {
  border-color: #667eea;
  background-color: white;
}

.item-textarea {
  width: 100%;
  min-height: 120rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.item-textarea:focus {
  border-color: #667eea;
  background-color: white;
}

/* 表单行 */
.form-row {
  display: flex;
  gap: 20rpx;
}

.flex-item {
  flex: 1;
}

/* 选择器 */
.selector-item {
  width: 100%;
  height: 80rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.selector-item:active {
  border-color: #667eea;
  background-color: rgba(102, 126, 234, 0.05);
}

.selector-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.selector-arrow {
  font-size: 24rpx;
  color: #999;
  opacity: 0.8;
  transition: transform 0.3s ease;
  line-height: 1;
}

.selector-item:active .selector-arrow {
  transform: rotate(180deg);
}

/* 优先级选择器 */
.priority-selector {
  display: flex;
  gap: 20rpx;
}

.priority-item {
  flex: 1;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.priority-item:active {
  transform: scale(0.98);
}

.priority-item.active {
  border-color: #667eea;
  background-color: rgba(102, 126, 234, 0.1);
}

.priority-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
}

.priority-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 选择器遮罩 */
.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.picker-container {
  background-color: white;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  max-height: 60vh;
  overflow: hidden;
}

.picker-header {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-cancel,
.picker-confirm {
  background: none;
  border: none;
  font-size: 28rpx;
  padding: 0;
}

.picker-cancel {
  color: #6c757d;
}

.picker-confirm {
  color: #667eea;
  font-weight: 500;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-content {
  height: 400rpx;
}

.picker-item {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
}

/* 底部操作栏 */
.bottom-actions {
  background-color: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #e0e0e0;
  display: flex;
  gap: 20rpx;
}

.cancel-btn {
  flex: 1;
  background-color: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

.save-btn {
  flex: 2;
  background-color: #667eea;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.save-btn:active {
  background-color: #5a6fd8;
  transform: scale(0.98);
}

.save-btn[loading] {
  opacity: 0.7;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-container {
  background-color: white;
  border-radius: 16rpx;
  padding: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .page-header {
    padding: 15rpx 20rpx;
  }
  
  .header-title {
    font-size: 32rpx;
  }
  
  .form-scroll {
    padding: 15rpx;
  }
  
  .form-section {
    padding: 20rpx;
  }
  
  .section-title {
    font-size: 28rpx;
  }
  
  .form-row {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .priority-selector {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .priority-item {
    flex-direction: row;
    justify-content: center;
  }
  
  .bottom-actions {
    padding: 15rpx 20rpx;
    gap: 15rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .form-section,
  .bottom-actions,
  .loading-container,
  .picker-container {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .section-title,
  .item-label,
  .selector-value,
  .priority-text,
  .picker-title {
    color: white;
  }
  
  .item-input,
  .item-textarea,
  .selector-item,
  .priority-item {
    background-color: #404040;
    border-color: #555;
    color: white;
  }
  
  .item-input:focus,
  .item-textarea:focus {
    background-color: #555;
    border-color: #667eea;
  }
  
  .selector-item:active,
  .priority-item:active {
    background-color: rgba(102, 126, 234, 0.1);
  }
  
  .priority-item.active {
    background-color: rgba(102, 126, 234, 0.2);
  }
  
  .cancel-btn {
    background-color: #404040;
    color: #ccc;
    border-color: #555;
  }
  
  .cancel-btn:active {
    background-color: #555;
  }
  
  .picker-cancel {
    color: #999;
  }
  
  .picker-confirm {
    color: #667eea;
  }
  
  .picker-item {
    color: white;
  }
  
  .loading-text {
    color: #ccc;
  }
}

/* 动画效果 */
.form-section {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.priority-item {
  animation: fadeInScale 0.3s ease;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.picker-container {
  animation: slideInUp 0.3s ease;
}

.loading-container {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 现代化输入组件样式 */
.input-group {
  margin-bottom: 32rpx;
}

.input-group:last-child {
  margin-bottom: 0;
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  gap: 8rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.required-mark {
  color: #ff4757;
  font-size: 24rpx;
  font-weight: bold;
}

.label-hint {
  color: #999;
  font-size: 24rpx;
  font-weight: normal;
}

.input-wrapper {
  position: relative;
}

.modern-input {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
}

.modern-input:focus {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
  transform: translateY(-2rpx);
}

.input-placeholder {
  color: #adb5bd;
}

/* 选择器样式 */
.selector-wrapper {
  position: relative;
}

.modern-selector {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
}

.modern-selector:active {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
  transform: translateY(-2rpx);
}

.selector-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.selector-text.placeholder {
  color: #adb5bd;
}

.selector-icon {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s ease;
}

/* 输入行布局 */
.input-row {
  display: flex;
  gap: 20rpx;
}

.half-width {
  flex: 1;
}

/* 优先级网格 */
.priority-grid {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.priority-option {
  flex: 1;
  min-width: 0;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 20rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.priority-option:active {
  transform: scale(0.95);
}

.priority-option.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.2);
}

.priority-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}

.priority-label {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
}

/* 文本域样式 */
.textarea-wrapper {
  position: relative;
}

.modern-textarea {
  width: 100%;
  min-height: 120rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  resize: none;
}

.modern-textarea:focus {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.char-count {
  position: absolute;
  bottom: 12rpx;
  right: 16rpx;
  font-size: 22rpx;
  color: #999;
  background: rgba(255, 255, 255, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  z-index: 100;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 20rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-size: 30rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.action-btn:active::before {
  width: 200%;
  height: 200%;
}

.action-btn.secondary {
  background: #6c757d;
  color: #fff;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn:disabled {
  opacity: 0.5;
  transform: none;
}

.btn-icon {
  font-size: 28rpx;
  line-height: 1;
}

.btn-text {
  font-size: 30rpx;
  font-weight: 600;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.picker-modal {
  width: 90%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  animation: slideInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.05);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(0.9);
}

.close-icon {
  font-size: 24rpx;
  color: #666;
}

.picker-body {
  padding: 20rpx 0;
}

.modern-picker {
  height: 400rpx;
}

.picker-option {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 0 30rpx;
}

.option-icon {
  font-size: 28rpx;
  line-height: 1;
}

.option-text {
  font-size: 28rpx;
  color: #333;
}

.modal-actions {
  background: #f8f9fa;
  padding: 20rpx 30rpx;
  display: flex;
  gap: 20rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.modal-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.modal-btn.secondary {
  background: #e9ecef;
  color: #6c757d;
}

.modal-btn.primary {
  background: #667eea;
  color: #fff;
}

.modal-btn:active {
  transform: scale(0.95);
}