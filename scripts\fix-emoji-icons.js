// 修复小程序中emoji图标的显示问题
const fs = require('fs');
const path = require('path');

// emoji图标列表
const EMOJI_ICONS = [
  '⋯', '📅', '🛒', '📝', '⏰', '🔔', '📋', '📜', '✅', '⚠️',
  '🤍', '❤️', '📤', '✏️', '🥬', '📈', '📉', '➡️', '📊'
];

// 需要处理的文件扩展名
const FILE_EXTENSIONS = ['.wxml'];

// 需要处理的目录
const DIRECTORIES_TO_PROCESS = [
  'miniprogram/pages',
  'miniprogram/components'
];

// 递归获取所有需要处理的文件
function getAllFiles(dir, fileList = []) {
  if (!fs.existsSync(dir)) {
    return fileList;
  }
  
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      getAllFiles(filePath, fileList);
    } else {
      const ext = path.extname(file);
      if (FILE_EXTENSIONS.includes(ext)) {
        fileList.push(filePath);
      }
    }
  });
  
  return fileList;
}

// 检查字符串是否为emoji
function isEmoji(str) {
  return EMOJI_ICONS.includes(str);
}

// 处理单个文件
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 查找所有的 <image src="emoji" 模式
    const imageEmojiRegex = /<image\s+src="([^"]*?)"\s*[^>]*>/g;
    let match;
    
    while ((match = imageEmojiRegex.exec(content)) !== null) {
      const fullMatch = match[0];
      const srcValue = match[1];
      
      // 检查src值是否为emoji
      if (isEmoji(srcValue)) {
        // 提取其他属性
        const classMatch = fullMatch.match(/class="([^"]*?)"/);
        const modeMatch = fullMatch.match(/mode="([^"]*?)"/);
        
        let className = classMatch ? classMatch[1] : '';
        // 添加emoji-icon类
        if (className && !className.includes('emoji-icon')) {
          className += ' emoji-icon';
        } else if (!className) {
          className = 'emoji-icon';
        }
        
        // 替换为text标签
        const replacement = `<text class="${className}">${srcValue}</text>`;
        content = content.replace(fullMatch, replacement);
        modified = true;
        
        console.log(`  替换: <image src="${srcValue}"> -> <text class="${className}">${srcValue}</text>`);
      }
    }
    
    // 如果文件被修改，写回文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`处理文件失败: ${filePath}`, error.message);
    return false;
  }
}

// 添加CSS样式到wxss文件
function addEmojiStyles(wxssFilePath) {
  try {
    if (!fs.existsSync(wxssFilePath)) {
      return false;
    }
    
    let content = fs.readFileSync(wxssFilePath, 'utf8');
    
    // 检查是否已经有emoji-icon样式
    if (content.includes('.emoji-icon')) {
      return false;
    }
    
    // 添加emoji图标样式
    const emojiStyles = `
/* Emoji图标样式 */
.emoji-icon {
  font-size: 32rpx;
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
}

/* 小尺寸emoji图标 */
.emoji-icon.small {
  font-size: 24rpx;
}

/* 大尺寸emoji图标 */
.emoji-icon.large {
  font-size: 48rpx;
}

/* 图标与文字对齐 */
.emoji-icon.inline {
  margin-right: 8rpx;
}
`;
    
    content += emojiStyles;
    fs.writeFileSync(wxssFilePath, content, 'utf8');
    
    console.log(`  添加emoji样式到: ${wxssFilePath}`);
    return true;
  } catch (error) {
    console.error(`添加样式失败: ${wxssFilePath}`, error.message);
    return false;
  }
}

// 主处理函数
function fixEmojiIcons() {
  console.log('🔧 开始修复小程序emoji图标显示问题...\n');
  
  let totalFiles = 0;
  let modifiedFiles = 0;
  let styledFiles = 0;
  
  DIRECTORIES_TO_PROCESS.forEach(dir => {
    const fullDir = path.resolve(dir);
    
    if (!fs.existsSync(fullDir)) {
      console.log(`⚠️  目录不存在: ${dir}`);
      return;
    }
    
    console.log(`📁 处理目录: ${dir}`);
    const files = getAllFiles(fullDir);
    
    files.forEach(filePath => {
      totalFiles++;
      const relativePath = path.relative(process.cwd(), filePath);
      console.log(`  处理文件: ${relativePath}`);
      
      if (processFile(filePath)) {
        modifiedFiles++;
        
        // 尝试为对应的wxss文件添加样式
        const wxssPath = filePath.replace('.wxml', '.wxss');
        if (addEmojiStyles(wxssPath)) {
          styledFiles++;
        }
      }
    });
  });
  
  console.log(`\n📊 处理结果:`);
  console.log(`  总文件数: ${totalFiles}`);
  console.log(`  修改文件数: ${modifiedFiles}`);
  console.log(`  添加样式文件数: ${styledFiles}`);
  console.log(`  未修改文件数: ${totalFiles - modifiedFiles}`);
  
  if (modifiedFiles > 0) {
    console.log('\n✅ Emoji图标修复完成！');
    console.log('\n💡 建议:');
    console.log('1. 重新编译小程序项目');
    console.log('2. 测试所有页面的图标显示');
    console.log('3. 检查控制台是否还有图片加载错误');
    console.log('4. 验证emoji图标是否正确显示');
  } else {
    console.log('\n✅ 没有发现需要修复的emoji图标');
  }
}

// 验证修复结果
function verifyEmojiIcons() {
  console.log('\n🔍 验证emoji图标修复结果...\n');
  
  let foundIssues = 0;
  
  DIRECTORIES_TO_PROCESS.forEach(dir => {
    const fullDir = path.resolve(dir);
    if (!fs.existsSync(fullDir)) return;
    
    const files = getAllFiles(fullDir);
    
    files.forEach(filePath => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const relativePath = path.relative(process.cwd(), filePath);
        
        // 检查是否还有emoji在image标签中
        EMOJI_ICONS.forEach(emoji => {
          const pattern = new RegExp(`<image[^>]*src="${emoji.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"[^>]*>`, 'g');
          if (pattern.test(content)) {
            console.log(`❌ 发现未修复的emoji图标: ${relativePath} -> ${emoji}`);
            foundIssues++;
          }
        });
      } catch (error) {
        console.error(`验证文件失败: ${filePath}`, error.message);
      }
    });
  });
  
  if (foundIssues === 0) {
    console.log('✅ 验证通过，所有emoji图标已正确修复');
  } else {
    console.log(`⚠️  发现 ${foundIssues} 个未修复的emoji图标，请检查`);
  }
}

// 显示emoji图标列表
function showEmojiList() {
  console.log('📋 支持的Emoji图标列表:\n');
  
  EMOJI_ICONS.forEach((emoji, index) => {
    console.log(`${index + 1}. ${emoji}`);
  });
  
  console.log('\n💡 使用说明:');
  console.log('- 这些emoji会从 <image src="emoji"> 转换为 <text class="emoji-icon">emoji</text>');
  console.log('- 自动添加相应的CSS样式');
  console.log('- 支持small、large、inline等样式修饰符');
}

// 命令行参数处理
const args = process.argv.slice(2);
const command = args[0];

switch (command) {
  case 'list':
    showEmojiList();
    break;
  case 'verify':
    verifyEmojiIcons();
    break;
  case 'fix':
  default:
    fixEmojiIcons();
    break;
}

module.exports = {
  fixEmojiIcons,
  verifyEmojiIcons,
  showEmojiList,
  EMOJI_ICONS
};
