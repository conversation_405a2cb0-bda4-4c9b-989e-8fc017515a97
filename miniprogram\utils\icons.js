// 图标映射工具 - 使用emoji替代图片
const iconMap = {
  // 基础图标
  'add': '➕',
  'delete': '🗑️',
  'edit': '✏️',
  'search': '🔍',
  'filter': '🔽',
  'more': '⋯',
  'close': '✖️',
  'check': '✅',
  'arrow-right': '▶️',
  'arrow-left': '◀️',
  'arrow-up': '🔼',
  'arrow-down': '🔽',
  'camera': '📷',
  'image': '🖼️',
  
  // 用户相关
  'user': '👤',
  'email': '📧',
  'lock': '🔒',
  'eye-open': '👁️',
  'eye-close': '🙈',
  
  // 功能图标
  'heart': '🤍',
  'heart-filled': '❤️',
  'star': '⭐',
  'star-filled': '⭐',
  'share': '📤',
  'time': '⏰',
  'difficulty': '📊',
  'servings': '👥',
  'cook': '👨‍🍳',
  'stop': '⏹️',
  'cart': '🛒',
  'prev': '⬅️',
  'next': '➡️',
  
  // 状态图标
  'check-green': '✅',
  'close-red': '❌',
  'check-white': '✅',
  
  // 应用图标
  'wechat': '💬',
  'voice': '🎤',
  'scan': '📱',
  'bell': '🔔',
  'moon': '🌙',
  'sync': '🔄',
  'help': '❓',
  'feedback': '💬',
  'info': 'ℹ️',
  'shield': '🛡️',
  'update': '🔄',
  'trash': '🗑️',
  'backup': '💾',
  'restore': '📥',
  'chart': '📊',
  
  // 分类图标
  'ingredients': '🥬',
  'recipes': '🍳',
  'shopping': '🛒',
  'favorites': '❤️',
  
  // 默认图标
  'default': '📋'
};

// 获取图标
function getIcon(iconName) {
  return iconMap[iconName] || iconMap['default'];
}

// 获取图标HTML
function getIconHtml(iconName, className = '') {
  const icon = getIcon(iconName);
  return `<view class="emoji-icon ${className}">${icon}</view>`;
}

module.exports = {
  iconMap,
  getIcon,
  getIconHtml
};