// 图标映射工具 - 使用emoji替代图片
const iconMap = {
  // 基础图标
  'add': '➕',
  'delete': '🗑️',
  'edit': '✏️',
  'search': '🔍',
  'filter': '🔽',
  'more': '⋯',
  'close': '✖️',
  'check': '✅',
  'arrow-right': '▶️',
  'arrow-left': '◀️',
  'arrow-up': '🔼',
  'arrow-down': '🔽',
  'camera': '📷',
  'image': '🖼️',
  
  // 用户相关
  'user': '👤',
  'email': '📧',
  'lock': '🔒',
  'eye-open': '👁️',
  'eye-close': '🙈',
  
  // 功能图标
  'heart': '🤍',
  'heart-filled': '❤️',
  'star': '⭐',
  'star-filled': '⭐',
  'share': '📤',
  'time': '⏰',
  'difficulty': '📊',
  'servings': '👥',
  'cook': '👨‍🍳',
  'stop': '⏹️',
  'cart': '🛒',
  'prev': '⬅️',
  'next': '➡️',
  
  // 状态图标
  'check-green': '✅',
  'close-red': '❌',
  'check-white': '✅',
  
  // 应用图标
  'wechat': '💬',
  'voice': '🎤',
  'scan': '📱',
  'bell': '🔔',
  'moon': '🌙',
  'sync': '🔄',
  'help': '❓',
  'feedback': '💬',
  'info': 'ℹ️',
  'shield': '🛡️',
  'update': '🔄',
  'trash': '🗑️',
  'backup': '💾',
  'restore': '📥',
  'chart': '📊',
  
  // 分类图标
  'ingredients': '🥬',
  'recipes': '🍳',
  'shopping': '🛒',
  'favorites': '❤️',
  'ingredient': '🥬',
  'recipe': '📖',
  'analysis': '📊',
  'profile': '👤',
  'settings': '⚙️',

  // 新增图标
  'calendar': '📅',
  'note': '📝',
  'cart-add': '🛒',
  'copy': '📋',
  'history': '📜',
  'warning': '⚠️',
  'trend-up': '📈',
  'trend-down': '📉',
  'trend-flat': '➡️',
  'health-warning': '⚠️',
  'empty-list': '📋',

  // 默认图标
  'default': '📋'
};

// 获取图标
function getIcon(iconName) {
  return iconMap[iconName] || iconMap['default'];
}

// 获取图标HTML
function getIconHtml(iconName, className = '') {
  const icon = getIcon(iconName);
  return `<view class="emoji-icon ${className}">${icon}</view>`;
}

// 图片URL映射
const imageMap = {
  'default-avatar': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
  'default-ingredient': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=200&fit=crop&crop=center',
  'share-cover': 'https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=400&h=300&fit=crop&crop=center',
  'share-app': 'https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=400&h=300&fit=crop&crop=center'
};

// 本地图片路径替换映射
const localImageReplacements = {
  '/images/icons/more.png': '⋯',
  '/images/icons/calendar.png': '📅',
  '/images/icons/shopping.png': '🛒',
  '/images/icons/note.png': '📝',
  '/images/icons/time.png': '⏰',
  '/images/icons/cart-add.png': '🛒',
  '/images/icons/bell.png': '🔔',
  '/images/icons/copy.png': '📋',
  '/images/icons/history.png': '📜',
  '/images/icons/check.png': '✅',
  '/images/icons/warning.png': '⚠️',
  '/images/icons/heart.png': '🤍',
  '/images/icons/heart-filled.png': '❤️',
  '/images/icons/share.png': '📤',
  '/images/icons/edit.png': '✏️',
  '/images/icons/ingredient.png': '🥬',
  '/images/icons/trend-up.png': '📈',
  '/images/icons/trend-down.png': '📉',
  '/images/icons/trend-flat.png': '➡️',
  '/images/icons/health-warning.png': '⚠️',
  '/images/icons/empty-list.png': '📋',
  '/images/default-ingredient.png': imageMap['default-ingredient'],
  '/images/tab/default-avatar.png': imageMap['default-avatar'],
  '/images/share-cover.jpg': imageMap['share-cover'],
  '/images/share-app.png': imageMap['share-app']
};

// 获取图片URL
function getImageUrl(imageName) {
  return imageMap[imageName] || imageMap['default-ingredient'];
}

// 替换本地图片路径
function replaceLocalImagePath(imagePath) {
  return localImageReplacements[imagePath] || imagePath;
}

module.exports = {
  iconMap,
  imageMap,
  localImageReplacements,
  getIcon,
  getIconHtml,
  getImageUrl,
  replaceLocalImagePath
};