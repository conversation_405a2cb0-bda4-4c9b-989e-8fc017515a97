const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: process.env.DB_PORT || 3308,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'rfid',
  database: process.env.DB_NAME || 'smart_fridge',
  charset: 'utf8mb4',
  collation: 'utf8mb4_unicode_ci',
  multipleStatements: true
};

async function fixCharset() {
  let connection;
  
  try {
    console.log('🔧 开始修复数据库字符集...');
    
    // 创建连接
    connection = await mysql.createConnection(dbConfig);
    
    // 修改数据库默认字符集
    console.log('📝 修改数据库默认字符集...');
    await connection.execute(`ALTER DATABASE ${dbConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    
    // 获取所有表
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ?
    `, [dbConfig.database]);
    
    console.log(`📋 找到 ${tables.length} 个表需要修复`);
    
    // 修复每个表
    for (const table of tables) {
      const tableName = table.TABLE_NAME;
      console.log(`🔄 修复表: ${tableName}`);
      
      try {
        // 转换表字符集
        await connection.execute(`ALTER TABLE ${tableName} CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
        
        // 获取表的所有文本字段
        const [columns] = await connection.execute(`
          SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
          FROM information_schema.COLUMNS 
          WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
          AND DATA_TYPE IN ('varchar', 'char', 'text', 'mediumtext', 'longtext')
        `, [dbConfig.database, tableName]);
        
        // 修复每个文本字段
        for (const column of columns) {
          const columnName = column.COLUMN_NAME;
          const dataType = column.DATA_TYPE;
          const maxLength = column.CHARACTER_MAXIMUM_LENGTH;
          
          let columnDef;
          if (dataType === 'varchar' || dataType === 'char') {
            columnDef = `${dataType.toUpperCase()}(${maxLength || 255})`;
          } else {
            columnDef = dataType.toUpperCase();
          }
          
          try {
            await connection.execute(`
              ALTER TABLE ${tableName} 
              MODIFY COLUMN ${columnName} ${columnDef} 
              CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
            `);
            console.log(`  ✅ 修复字段: ${tableName}.${columnName}`);
          } catch (error) {
            console.warn(`  ⚠️  跳过字段: ${tableName}.${columnName} - ${error.message}`);
          }
        }
        
        console.log(`✅ 表 ${tableName} 修复完成`);
      } catch (error) {
        console.error(`❌ 表 ${tableName} 修复失败:`, error.message);
      }
    }
    
    // 检查修复结果
    console.log('\n📊 检查修复结果...');
    
    const [tableCollations] = await connection.execute(`
      SELECT TABLE_NAME, TABLE_COLLATION
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ?
    `, [dbConfig.database]);
    
    console.log('\n表字符集状态:');
    tableCollations.forEach(row => {
      const status = row.TABLE_COLLATION === 'utf8mb4_unicode_ci' ? '✅' : '❌';
      console.log(`  ${status} ${row.TABLE_NAME}: ${row.TABLE_COLLATION}`);
    });
    
    const [columnCollations] = await connection.execute(`
      SELECT TABLE_NAME, COLUMN_NAME, COLLATION_NAME
      FROM information_schema.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND COLLATION_NAME IS NOT NULL
      AND COLLATION_NAME != 'utf8mb4_unicode_ci'
    `, [dbConfig.database]);
    
    if (columnCollations.length > 0) {
      console.log('\n⚠️  仍有字段未修复:');
      columnCollations.forEach(row => {
        console.log(`  ❌ ${row.TABLE_NAME}.${row.COLUMN_NAME}: ${row.COLLATION_NAME}`);
      });
    } else {
      console.log('\n🎉 所有字段字符集修复完成！');
    }
    
  } catch (error) {
    console.error('❌ 修复过程中出现错误:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行修复脚本
if (require.main === module) {
  fixCharset().then(() => {
    console.log('🏁 字符集修复脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { fixCharset };
