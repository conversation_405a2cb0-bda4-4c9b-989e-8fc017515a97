// 最终语法检查 - 只检查真正的错误
const fs = require('fs');

// 需要检查的文件（之前有问题的文件）
const FILES_TO_CHECK = [
  'miniprogram/pages/ingredients/add/add.wxml',
  'miniprogram/pages/ingredients/detail/detail.wxml',
  'miniprogram/pages/shopping/add/add.wxml'
];

function checkRealErrors(filePath) {
  if (!fs.existsSync(filePath)) {
    return [`文件不存在: ${filePath}`];
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const errors = [];
    
    // 检查真正的语法错误
    
    // 1. 检查孤立的结束标签（前一行是自闭合标签或其他结束标签）
    lines.forEach((line, index) => {
      const trimmed = line.trim();
      if (trimmed === '</text>' || trimmed === '</image>') {
        const prevLine = index > 0 ? lines[index - 1].trim() : '';
        if (prevLine.endsWith('/>') || prevLine === '</text>' || prevLine === '</image>' || prevLine === '</view>') {
          errors.push(`第${index + 1}行: 孤立的结束标签 ${trimmed}`);
        }
      }
    });
    
    // 2. 检查明显的错误模式
    if (content.includes('</text></image>')) {
      errors.push('发现错误模式: </text></image>');
    }
    
    // 3. 检查真正的重复结束标签（排除正常的嵌套结构）
    const lines2 = content.split('\n');
    for (let i = 0; i < lines2.length - 1; i++) {
      const currentLine = lines2[i].trim();
      const nextLine = lines2[i + 1].trim();

      // 检查连续的独立结束标签
      if (currentLine === '</text>' && nextLine === '</text>') {
        errors.push(`第${i + 1}-${i + 2}行: 连续的独立</text>标签`);
      }
    }
    
    return errors;
  } catch (error) {
    return [`文件读取失败: ${error.message}`];
  }
}

function finalCheck() {
  console.log('🔍 最终语法检查...\n');
  
  let totalErrors = 0;
  let problemFiles = 0;
  
  FILES_TO_CHECK.forEach(filePath => {
    console.log(`📁 检查文件: ${filePath}`);
    
    const errors = checkRealErrors(filePath);
    if (errors.length > 0) {
      problemFiles++;
      totalErrors += errors.length;
      
      console.log('❌ 发现问题:');
      errors.forEach(error => {
        console.log(`  - ${error}`);
      });
    } else {
      console.log('✅ 语法正确');
    }
    console.log('');
  });
  
  console.log(`📊 最终检查结果:`);
  console.log(`  检查文件数: ${FILES_TO_CHECK.length}`);
  console.log(`  有问题文件数: ${problemFiles}`);
  console.log(`  总错误数: ${totalErrors}`);
  
  if (problemFiles === 0) {
    console.log('\n🎉 恭喜！所有检查的文件语法都正确！');
    console.log('✅ 小程序应该可以正常编译运行了');
    console.log('\n💡 建议:');
    console.log('1. 重新编译小程序项目');
    console.log('2. 检查开发者工具是否还有编译错误');
    console.log('3. 测试页面功能是否正常');
  } else {
    console.log('\n⚠️  仍有语法错误需要修复');
  }
}

// 运行检查
finalCheck();
