// 食材添加页面 - 重新设计
const app = getApp();

Page({
  data: {
    // 表单数据
    formData: {
      name: '',
      category: '',
      quantity: 1,
      unit: '',
      purchaseDate: '',
      expiryDate: '',
      price: '',
      notes: ''
    },

    // 选项数据
    categories: [
      '蔬菜', '水果', '肉类', '海鲜', '蛋奶', 
      '调料', '零食', '饮品', '主食', '其他'
    ],
    
    units: [
      '个', '包', '袋', '盒', '瓶', '罐', 
      '克', '千克', '斤', '毫升', '升', '杯'
    ],

    // 存储位置
    storageLocations: [
      '冷藏室', '冷冻室', '常温储存', '蔬菜室', '保鲜室'
    ],

    // 常用食材名称
    commonNames: [
      '苹果', '香蕉', '橙子', '牛奶', '鸡蛋', '土豆', 
      '西红柿', '黄瓜', '胡萝卜', '白菜', '大米', '面条',
      '鸡肉', '猪肉', '牛肉', '鱼', '虾', '豆腐',
      '酱油', '醋', '盐', '糖', '油', '面包'
    ],

    // 过滤后的食材名称
    filteredNames: [],

    // 选择器状态
    nameIndex: -1,
    categoryIndex: -1,
    unitIndex: -1,
    storageIndex: -1,

    // 页面状态
    canSubmit: false,
    submitting: false
  },

  onLoad(options) {
    console.log('食材添加页面加载');
    
    // 设置默认日期
    this.setDefaultDates();
    
    // 加载用户偏好
    this.loadUserPreferences();
    
    // 加载常用食材名称
    this.loadCommonNames();
    
    // 检查表单状态
    this.checkFormValidity();
  },

  // 加载常用食材名称
  loadCommonNames() {
    try {
      const savedNames = wx.getStorageSync('commonNames');
      if (savedNames && savedNames.length > 0) {
        this.setData({
          commonNames: savedNames,
          filteredNames: savedNames
        });
      } else {
        this.setData({
          filteredNames: this.data.commonNames
        });
      }
    } catch (error) {
      console.error('加载常用食材名称失败:', error);
    }
  },

  // 设置默认日期
  setDefaultDates() {
    const today = new Date();
    const purchaseDate = this.formatDate(today);
    
    // 默认保质期为7天后
    const expiryDate = new Date(today);
    expiryDate.setDate(today.getDate() + 7);
    
    this.setData({
      'formData.purchaseDate': purchaseDate,
      'formData.expiryDate': this.formatDate(expiryDate)
    });
  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 加载用户偏好
  loadUserPreferences() {
    try {
      const recentCategories = wx.getStorageSync('recentCategories') || [];
      const recentUnits = wx.getStorageSync('recentUnits') || [];
      
      // 将最近使用的选项排在前面
      if (recentCategories.length > 0) {
        const categories = [...new Set([...recentCategories, ...this.data.categories])];
        this.setData({ categories });
      }
      
      if (recentUnits.length > 0) {
        const units = [...new Set([...recentUnits, ...this.data.units])];
        this.setData({ units });
      }
    } catch (error) {
      console.error('加载用户偏好失败:', error);
    }
  },

  // 保存用户偏好
  saveUserPreference(type, value) {
    try {
      const key = `recent${type.charAt(0).toUpperCase() + type.slice(1)}s`;
      let recent = wx.getStorageSync(key) || [];
      
      // 移除已存在的项目
      recent = recent.filter(item => item !== value);
      
      // 添加到开头
      recent.unshift(value);
      
      // 最多保存5个
      if (recent.length > 5) {
        recent = recent.slice(0, 5);
      }
      
      wx.setStorageSync(key, recent);
    } catch (error) {
      console.error('保存用户偏好失败:', error);
    }
  },

  // 输入处理
  // 食材名称选择
  onNameChange(e) {
    const index = e.detail.value;
    const name = this.data.commonNames[index];
    this.setData({
      nameIndex: index,
      'formData.name': name
    });
    this.checkFormValidity();
  },

  onQuantityInput(e) {
    let quantity = parseInt(e.detail.value) || 1;
    if (quantity < 1) quantity = 1;
    if (quantity > 9999) quantity = 9999;
    
    this.setData({
      'formData.quantity': quantity
    });
    this.checkFormValidity();
  },

  onPriceInput(e) {
    let price = e.detail.value;
    // 只允许数字和小数点
    price = price.replace(/[^\d.]/g, '');
    
    // 确保只有一个小数点
    const parts = price.split('.');
    if (parts.length > 2) {
      price = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // 限制小数位数
    if (parts[1] && parts[1].length > 2) {
      price = parts[0] + '.' + parts[1].substring(0, 2);
    }
    
    this.setData({
      'formData.price': price
    });
  },

  onNotesInput(e) {
    this.setData({
      'formData.notes': e.detail.value
    });
  },

  // 数量调整
  decreaseQuantity() {
    const quantity = Math.max(1, this.data.formData.quantity - 1);
    this.setData({
      'formData.quantity': quantity
    });
    this.checkFormValidity();
  },

  increaseQuantity() {
    const quantity = Math.min(9999, this.data.formData.quantity + 1);
    this.setData({
      'formData.quantity': quantity
    });
    this.checkFormValidity();
  },

  // 分类选择
  onCategoryChange(e) {
    const index = e.detail.value;
    const category = this.data.categories[index];
    
    this.setData({
      categoryIndex: index,
      'formData.category': category
    });
    
    this.saveUserPreference('category', category);
    this.checkFormValidity();
  },

  // 单位选择
  onUnitChange(e) {
    const index = e.detail.value;
    const unit = this.data.units[index];
    
    this.setData({
      unitIndex: index,
      'formData.unit': unit
    });
    
    this.saveUserPreference('unit', unit);
    this.checkFormValidity();
  },

  // 存储位置选择
  onStorageChange(e) {
    const index = e.detail.value;
    const location = this.data.storageLocations[index];
    
    this.setData({
      storageIndex: index,
      'formData.location': location
    });
    
    this.saveUserPreference('location', location);
  },

  // 日期选择
  onPurchaseDateChange(e) {
    this.setData({
      'formData.purchaseDate': e.detail.value
    });
  },

  onExpiryDateChange(e) {
    this.setData({
      'formData.expiryDate': e.detail.value
    });
    this.checkFormValidity();
  },

  // 检查表单有效性
  checkFormValidity() {
    const { name, category, quantity, unit, expiryDate } = this.data.formData;
    
    const canSubmit = name.trim().length > 0 && 
                     category.length > 0 && 
                     quantity > 0 && 
                     unit.length > 0 && 
                     expiryDate.length > 0;
    
    this.setData({ canSubmit });
  },

  // 表单验证
  async validateForm() {
    const { name, category, quantity, unit, expiryDate } = this.data.formData;
    
    if (!name.trim()) {
      this.showError('请输入食材名称');
      return false;
    }
    
    if (!category) {
      this.showError('请选择食材分类');
      return false;
    }
    
    if (!quantity || quantity <= 0) {
      this.showError('请输入有效数量');
      return false;
    }
    
    if (!unit) {
      this.showError('请选择单位');
      return false;
    }
    
    if (!expiryDate) {
      this.showError('请选择保质期');
      return false;
    }
    
    // 检查保质期是否合理
    const today = new Date();
    const expiry = new Date(expiryDate);
    
    if (expiry < today) {
      return new Promise((resolve) => {
        wx.showModal({
          title: '提示',
          content: '保质期已过期，确定要添加吗？',
          success: (res) => {
            resolve(res.confirm);
          }
        });
      });
    }
    
    return true;
  },

  // 提交表单
  async onSubmit() {
    if (this.data.submitting) return;
    
    const isValid = await this.validateForm();
    if (!isValid) return;
    
    this.setData({ submitting: true });
    
    try {
      // 检查是否是新的食材名称
      const isNewName = !this.data.commonNames.includes(this.data.formData.name);
      
      // 准备提交数据
      const submitData = {
        ...this.data.formData,
        quantity: Number(this.data.formData.quantity),
        price: this.data.formData.price ? Number(this.data.formData.price) : null,
        userId: app.globalData.userInfo?.id || 'temp_user',
        isNewName: isNewName
      };
      
      console.log('提交数据:', submitData);
      
      // 如果是新的食材名称，记录到日志（后端会自动处理新食材名称）
      if (isNewName) {
        console.log('添加新食材名称:', this.data.formData.name);
      }
      
      // 保存食材数据
      await this.saveIngredientData(submitData);
      
      // 如果是新食材，添加到本地常用列表
      if (isNewName) {
        const newCommonNames = [this.data.formData.name, ...this.data.commonNames];
        this.setData({ commonNames: newCommonNames });
        
        // 保存到本地存储
        try {
          wx.setStorageSync('commonNames', newCommonNames);
        } catch (error) {
          console.error('保存常用食材失败:', error);
        }
      }
      
      // 保存成功
      wx.showToast({
        title: '添加成功',
        icon: 'success',
        duration: 2000
      });
      
      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      
    } catch (error) {
      console.error('提交失败:', error);
      this.showError('添加失败，请重试');
    } finally {
      this.setData({ submitting: false });
    }
  },



  // API调用封装
  async callApi(url, options = {}) {
    return new Promise((resolve, reject) => {
      // 获取存储的token
      const token = wx.getStorageSync('token');

      wx.request({
        url: `${app.globalData.apiBase || 'http://127.0.0.1:3000'}${url}`,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'content-type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          ...options.header
        },
        success: (res) => {
          // 2xx状态码都表示成功
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data);
          } else {
            console.error('API请求失败:', res);
            reject(new Error(`API错误: ${res.statusCode} - ${res.data?.message || '请求失败'}`));
          }
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  },

  // 保存食材数据
  async saveIngredientData(data) {
    try {
      // 尝试调用API保存
      const result = await this.callApi('/api/ingredients', {
        method: 'POST',
        data: data
      });
      
      return result;
    } catch (error) {
      console.warn('保存到服务器失败，使用本地存储:', error);
      
      // 如果API失败，保存到本地存储
      try {
        const localIngredients = wx.getStorageSync('localIngredients') || [];
        const newIngredient = {
          ...data,
          id: Date.now(),
          createdAt: new Date().toISOString(),
          offline: true
        };
        
        localIngredients.unshift(newIngredient);
        wx.setStorageSync('localIngredients', localIngredients);
        
        return { success: true, offline: true, data: newIngredient };
      } catch (storageError) {
        console.error('本地存储也失败:', storageError);
        throw new Error('保存失败');
      }
    }
  },

  // 取消操作
  onCancel() {
    // 检查是否有未保存的更改
    const hasChanges = this.data.formData.name || 
                      this.data.formData.category || 
                      this.data.formData.notes;
    
    if (hasChanges) {
      wx.showModal({
        title: '确认取消',
        content: '您有未保存的更改，确定要取消吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },



  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡，防止关闭下拉框
  },

  // 显示错误信息
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '智能冰箱管理 - 添加食材',
      path: '/pages/ingredients/add/add'
    };
  },

  // 页面分享到朋友圈
  onShareTimeline() {
    return {
      title: '智能冰箱管理 - 轻松管理您的食材'
    };
  }
});