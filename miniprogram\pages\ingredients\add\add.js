// 食材添加/编辑页面逻辑
const app = getApp();

Page({
  data: {
    isEdit: false,
    ingredientId: null,
    formData: {
      name: '',
      category: '',
      quantity: '',
      unit: '',
      expire_date: '',
      purchase_date: '',
      location: '',
      notes: '',
      image_url: ''
    },
    categories: ['蔬菜', '水果', '肉类', '海鲜', '蛋奶', '调料', '其他'],
    units: ['个', '斤', '克', '千克', '升', '毫升', '包', '袋', '盒', '瓶'],
    locations: ['冷藏室', '冷冻室', '常温储存', '蔬菜室', '保鲜室'],
    showCategoryPicker: false,
    showUnitPicker: false,
    showLocationPicker: false,
    loading: false,
    uploading: false
  },

  onLoad(options) {
    console.log('食材添加/编辑页面加载', options);
    
    if (options.id) {
      this.setData({
        isEdit: true,
        ingredientId: options.id
      });
      wx.setNavigationBarTitle({
        title: '编辑食材'
      });
      this.loadIngredientDetail(options.id);
    } else {
      wx.setNavigationBarTitle({
        title: '添加食材'
      });
      // 设置默认购买日期为今天
      const today = new Date().toISOString().split('T')[0];
      this.setData({
        'formData.purchase_date': today
      });
    }
  },

  // 加载食材详情
  async loadIngredientDetail(id) {
    try {
      const res = await app.request({
        url: `/ingredients/${id}`
      });

      const ingredient = res.data;
      this.setData({
        formData: {
          name: ingredient.name || '',
          category: ingredient.category || '',
          quantity: ingredient.quantity ? ingredient.quantity.toString() : '',
          unit: ingredient.unit || '',
          expire_date: ingredient.expire_date || '',
          purchase_date: ingredient.purchase_date || '',
          location: ingredient.location || '',
          notes: ingredient.notes || '',
          image_url: ingredient.image_url || ''
        }
      });
    } catch (error) {
      console.error('加载食材详情失败:', error);
      app.showError('加载失败');
      wx.navigateBack();
    }
  },

  // 输入框变化
  onInputChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 显示分类选择器
  showCategoryPicker() {
    this.setData({
      showCategoryPicker: true
    });
  },

  // 分类选择
  onCategoryChange(e) {
    const index = e.detail.value;
    this.setData({
      'formData.category': this.data.categories[index],
      showCategoryPicker: false
    });
  },

  // 分类项目选择
  onCategorySelect(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'formData.category': value,
      showCategoryPicker: false
    });
  },

  // 取消分类选择
  onCategoryCancel() {
    this.setData({
      showCategoryPicker: false
    });
  },

  // 显示单位选择器
  showUnitPicker() {
    this.setData({
      showUnitPicker: true
    });
  },

  // 单位选择
  onUnitChange(e) {
    const index = e.detail.value;
    this.setData({
      'formData.unit': this.data.units[index],
      showUnitPicker: false
    });
  },

  // 单位项目选择
  onUnitSelect(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'formData.unit': value,
      showUnitPicker: false
    });
  },

  // 取消单位选择
  onUnitCancel() {
    this.setData({
      showUnitPicker: false
    });
  },

  // 显示位置选择器
  showLocationPicker() {
    this.setData({
      showLocationPicker: true
    });
  },

  // 位置选择
  onLocationChange(e) {
    const index = e.detail.value;
    this.setData({
      'formData.location': this.data.locations[index],
      showLocationPicker: false
    });
  },

  // 位置项目选择
  onLocationSelect(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'formData.location': value,
      showLocationPicker: false
    });
  },

  // 取消位置选择
  onLocationCancel() {
    this.setData({
      showLocationPicker: false
    });
  },

  // 日期选择
  onDateChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 选择图片
  async chooseImage() {
    try {
      const res = await wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      });

      const tempFilePath = res.tempFilePaths[0];
      this.uploadImage(tempFilePath);
    } catch (error) {
      console.error('选择图片失败:', error);
    }
  },

  // 上传图片
  async uploadImage(filePath) {
    this.setData({ uploading: true });

    try {
      const uploadRes = await wx.uploadFile({
        url: `${app.globalData.baseUrl}/upload/image`,
        filePath: filePath,
        name: 'image',
        header: {
          'Authorization': `Bearer ${app.globalData.token}`
        }
      });

      const result = JSON.parse(uploadRes.data);
      if (result.success) {
        this.setData({
          'formData.image_url': result.data.url
        });
        app.showSuccess('图片上传成功');
      } else {
        app.showError(result.message || '上传失败');
      }
    } catch (error) {
      console.error('上传图片失败:', error);
      app.showError('上传失败');
    } finally {
      this.setData({ uploading: false });
    }
  },

  // 删除图片
  deleteImage() {
    this.setData({
      'formData.image_url': ''
    });
  },

  // 验证表单
  validateForm() {
    const { name, category, quantity, unit, expire_date } = this.data.formData;

    if (!name.trim()) {
      app.showError('请输入食材名称');
      return false;
    }

    if (!category) {
      app.showError('请选择食材分类');
      return false;
    }

    if (!quantity.trim()) {
      app.showError('请输入数量');
      return false;
    }

    if (isNaN(quantity) || parseFloat(quantity) <= 0) {
      app.showError('请输入有效的数量');
      return false;
    }

    if (!unit) {
      app.showError('请选择单位');
      return false;
    }

    if (!expire_date) {
      app.showError('请选择过期日期');
      return false;
    }

    // 检查过期日期是否合理
    const expireDate = new Date(expire_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (expireDate < today) {
      const confirmed = wx.showModal({
        title: '提示',
        content: '过期日期早于今天，确定要保存吗？',
        showCancel: true
      });
      
      return new Promise((resolve) => {
        confirmed.then(res => {
          resolve(res.confirm);
        });
      });
    }

    return true;
  },

  // 保存食材
  async saveIngredient() {
    const isValid = await this.validateForm();
    if (!isValid) return;

    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const formData = { ...this.data.formData };
      formData.quantity = parseFloat(formData.quantity);

      if (this.data.isEdit) {
        // 编辑食材
        await app.request({
          url: `/ingredients/${this.data.ingredientId}`,
          method: 'PUT',
          data: formData
        });
        app.showSuccess('更新成功');
      } else {
        // 添加食材
        await app.request({
          url: '/ingredients',
          method: 'POST',
          data: formData
        });
        app.showSuccess('添加成功');
      }

      // 返回上一页
      wx.navigateBack();
    } catch (error) {
      console.error('保存食材失败:', error);
      app.showError('保存失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 扫码添加
  async scanCode() {
    try {
      const res = await wx.scanCode({
        scanType: ['barCode', 'qrCode']
      });

      const code = res.result;
      console.log('扫码结果:', code);

      // 这里可以调用商品信息API获取食材信息
      app.showError('扫码功能开发中');
    } catch (error) {
      console.error('扫码失败:', error);
      if (error.errMsg !== 'scanCode:fail cancel') {
        app.showError('扫码失败');
      }
    }
  },

  // 语音输入
  async voiceInput() {
    try {
      const res = await wx.startRecord({
        duration: 10000
      });

      // 这里可以调用语音识别API
      console.log('录音结果:', res);
      app.showError('语音输入功能开发中');
    } catch (error) {
      console.error('语音输入失败:', error);
      app.showError('语音输入失败');
    }
  },

  // 重置表单
  resetForm() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有输入内容吗？',
      success: (res) => {
        if (res.confirm) {
          const today = new Date().toISOString().split('T')[0];
          this.setData({
            formData: {
              name: '',
              category: '',
              quantity: '',
              unit: '',
              expire_date: '',
              purchase_date: today,
              location: '',
              notes: '',
              image_url: ''
            }
          });
        }
      }
    });
  }
});