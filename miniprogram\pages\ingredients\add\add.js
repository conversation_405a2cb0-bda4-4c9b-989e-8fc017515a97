// 食材添加/编辑页面逻辑
const app = getApp();

Page({
  data: {
    isEdit: false,
    ingredientId: null,
    formData: {
      name: '',
      category: '',
      quantity: '',
      unit: '',
      expire_date: '',
      purchase_date: '',
      location: '',
      notes: '',
      image_url: ''
    },
    categories: ['蔬菜', '水果', '肉类', '海鲜', '蛋奶', '调料', '其他'],
    units: ['个', '斤', '克', '千克', '升', '毫升', '包', '袋', '盒', '瓶'],
    locations: ['冷藏室', '冷冻室', '常温储存', '蔬菜室', '保鲜室'],
    showCategoryPicker: false,
    showUnitPicker: false,
    showLocationPicker: false,
    loading: false,
    uploading: false,
    // 新增数据
    showScanTips: false,
    showVoiceTips: false,
    formErrors: {},
    isFormSubmitted: false,
    expireDateWarning: false,
    expireSoonDays: 3, // 过期提醒天数
    showFeatureMenu: false,
    recentCategories: [], // 最近使用的分类
    recentLocations: [], // 最近使用的位置
    recentUnits: [], // 最近使用的单位
    commonIngredients: [], // 常用食材快速添加
    pageAnimationClass: 'fade-in'
  },

  onLoad(options) {
    console.log('食材添加/编辑页面加载', options);
    
    // 页面加载动画
    setTimeout(() => {
      this.setData({ pageAnimationClass: '' });
    }, 500);
    
    if (options.id) {
      this.setData({
        isEdit: true,
        ingredientId: options.id
      });
      wx.setNavigationBarTitle({
        title: '编辑食材'
      });
      this.loadIngredientDetail(options.id);
    } else {
      wx.setNavigationBarTitle({
        title: '添加食材'
      });
      // 设置默认购买日期为今天
      const today = new Date().toISOString().split('T')[0];
      this.setData({
        'formData.purchase_date': today
      });
      
      // 加载常用食材和最近使用的选项
      this.loadRecentOptions();
      this.loadCommonIngredients();
      
      // 如果有扫码参数，直接调用扫码功能
      if (options.scan) {
        this.scanCode();
      }
    }
  },

  // 加载最近使用的选项
  loadRecentOptions() {
    try {
      const recentCategories = wx.getStorageSync('recentCategories') || [];
      const recentLocations = wx.getStorageSync('recentLocations') || [];
      const recentUnits = wx.getStorageSync('recentUnits') || [];
      
      this.setData({
        recentCategories,
        recentLocations,
        recentUnits
      });
    } catch (error) {
      console.error('加载最近选项失败:', error);
    }
  },

  // 保存最近使用的选项
  saveRecentOption(type, value) {
    if (!value) return;
    
    try {
      const key = `recent${type.charAt(0).toUpperCase() + type.slice(1)}s`;
      let recent = wx.getStorageSync(key) || [];
      
      // 如果已存在，移到第一位；否则添加到第一位
      const index = recent.indexOf(value);
      if (index > -1) {
        recent.splice(index, 1);
      }
      recent.unshift(value);
      
      // 最多保存5个最近选项
      if (recent.length > 5) {
        recent = recent.slice(0, 5);
      }
      
      wx.setStorageSync(key, recent);
      this.setData({
        [key]: recent
      });
    } catch (error) {
      console.error(`保存最近${type}失败:`, error);
    }
  },

  // 加载常用食材
  loadCommonIngredients() {
    try {
      // 这里可以从后端API获取常用食材，或者从本地存储获取
      // 示例数据
      const commonIngredients = [
        { name: '土豆', category: '蔬菜', unit: '个', image: '/images/ingredients/potato.png' },
        { name: '西红柿', category: '蔬菜', unit: '个', image: '/images/ingredients/tomato.png' },
        { name: '鸡蛋', category: '蛋奶', unit: '个', image: '/images/ingredients/egg.png' },
        { name: '牛奶', category: '蛋奶', unit: '盒', image: '/images/ingredients/milk.png' }
      ];
      
      this.setData({ commonIngredients });
    } catch (error) {
      console.error('加载常用食材失败:', error);
    }
  },

  // 快速添加常用食材
  quickAddIngredient(e) {
    const { index } = e.currentTarget.dataset;
    const ingredient = this.data.commonIngredients[index];
    
    // 填充表单数据
    this.setData({
      'formData.name': ingredient.name,
      'formData.category': ingredient.category,
      'formData.unit': ingredient.unit,
      'formData.image_url': ingredient.image
    });
    
    // 显示成功提示
    wx.showToast({
      title: '已填充食材信息',
      icon: 'success'
    });
  },

  // 加载食材详情
  async loadIngredientDetail(id) {
    this.setData({ loading: true });
    
    try {
      const res = await app.request({
        url: `/ingredients/${id}`
      });

      const ingredient = res.data;
      this.setData({
        formData: {
          name: ingredient.name || '',
          category: ingredient.category || '',
          quantity: ingredient.quantity ? ingredient.quantity.toString() : '',
          unit: ingredient.unit || '',
          expire_date: ingredient.expire_date || '',
          purchase_date: ingredient.purchase_date || '',
          location: ingredient.location || '',
          notes: ingredient.notes || '',
          image_url: ingredient.image_url || ''
        }
      });
      
      // 检查是否即将过期
      this.checkExpirationStatus();
    } catch (error) {
      console.error('加载食材详情失败:', error);
      app.showError('加载失败');
      wx.navigateBack();
    } finally {
      this.setData({ loading: false });
    }
  },

  // 检查过期状态
  checkExpirationStatus() {
    const { expire_date } = this.data.formData;
    if (!expire_date) return;
    
    const expireDate = new Date(expire_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const diffTime = expireDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      // 已过期
      this.setData({ 
        expireDateWarning: true,
        expireDateStatus: 'expired',
        expireDateMessage: '该食材已过期！'
      });
    } else if (diffDays <= this.data.expireSoonDays) {
      // 即将过期
      this.setData({ 
        expireDateWarning: true,
        expireDateStatus: 'expiring-soon',
        expireDateMessage: `该食材将在${diffDays}天后过期`
      });
    } else {
      this.setData({ 
        expireDateWarning: false,
        expireDateStatus: '',
        expireDateMessage: ''
      });
    }
  },

  // 输入框变化
  onInputChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value,
      [`formErrors.${field}`]: '' // 清除错误提示
    });
    
    // 对于特定字段，进行额外处理
    if (field === 'expire_date') {
      this.checkExpirationStatus();
    }
  },

  // 显示分类选择器
  showCategoryPicker() {
    this.setData({
      showCategoryPicker: true
    });
  },

  // 分类选择
  onCategoryChange(e) {
    const index = e.detail.value;
    const category = this.data.categories[index];
    this.setData({
      'formData.category': category,
      showCategoryPicker: false,
      'formErrors.category': ''
    });
    
    // 保存最近使用的分类
    this.saveRecentOption('category', category);
  },

  // 分类项目选择
  onCategorySelect(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'formData.category': value,
      showCategoryPicker: false,
      'formErrors.category': ''
    });
    
    // 保存最近使用的分类
    this.saveRecentOption('category', value);
  },

  // 取消分类选择
  onCategoryCancel() {
    this.setData({
      showCategoryPicker: false
    });
  },

  // 显示单位选择器
  showUnitPicker() {
    this.setData({
      showUnitPicker: true
    });
  },

  // 单位选择
  onUnitChange(e) {
    const index = e.detail.value;
    const unit = this.data.units[index];
    this.setData({
      'formData.unit': unit,
      showUnitPicker: false,
      'formErrors.unit': ''
    });
    
    // 保存最近使用的单位
    this.saveRecentOption('unit', unit);
  },

  // 单位项目选择
  onUnitSelect(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'formData.unit': value,
      showUnitPicker: false,
      'formErrors.unit': ''
    });
    
    // 保存最近使用的单位
    this.saveRecentOption('unit', value);
  },

  // 取消单位选择
  onUnitCancel() {
    this.setData({
      showUnitPicker: false
    });
  },

  // 显示位置选择器
  showLocationPicker() {
    this.setData({
      showLocationPicker: true
    });
  },

  // 位置选择
  onLocationChange(e) {
    const index = e.detail.value;
    const location = this.data.locations[index];
    this.setData({
      'formData.location': location,
      showLocationPicker: false
    });
    
    // 保存最近使用的位置
    this.saveRecentOption('location', location);
  },

  // 位置项目选择
  onLocationSelect(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'formData.location': value,
      showLocationPicker: false
    });
    
    // 保存最近使用的位置
    this.saveRecentOption('location', value);
  },

  // 取消位置选择
  onLocationCancel() {
    this.setData({
      showLocationPicker: false
    });
  },

  // 日期选择
  onDateChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value,
      [`formErrors.${field}`]: ''
    });
    
    // 如果是过期日期，检查状态
    if (field === 'expire_date') {
      this.checkExpirationStatus();
    }
  },

  // 选择图片
  async chooseImage() {
    try {
      const res = await wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      });

      const tempFilePath = res.tempFilePaths[0];
      this.uploadImage(tempFilePath);
    } catch (error) {
      console.error('选择图片失败:', error);
    }
  },

  // 上传图片
  async uploadImage(filePath) {
    this.setData({ uploading: true });

    try {
      const uploadRes = await wx.uploadFile({
        url: `${app.globalData.baseUrl}/upload/image`,
        filePath: filePath,
        name: 'image',
        header: {
          'Authorization': `Bearer ${app.globalData.token}`
        }
      });

      const result = JSON.parse(uploadRes.data);
      if (result.success) {
        this.setData({
          'formData.image_url': result.data.url
        });
        app.showSuccess('图片上传成功');
      } else {
        app.showError(result.message || '上传失败');
      }
    } catch (error) {
      console.error('上传图片失败:', error);
      app.showError('上传失败');
    } finally {
      this.setData({ uploading: false });
    }
  },

  // 删除图片
  deleteImage() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除当前图片吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            'formData.image_url': ''
          });
        }
      }
    });
  },

  // 验证表单
  validateForm() {
    const { name, category, quantity, unit, expire_date } = this.data.formData;
    let isValid = true;
    const formErrors = {};

    if (!name.trim()) {
      formErrors.name = '请输入食材名称';
      isValid = false;
    }

    if (!category) {
      formErrors.category = '请选择食材分类';
      isValid = false;
    }

    if (!quantity.trim()) {
      formErrors.quantity = '请输入数量';
      isValid = false;
    } else if (isNaN(quantity) || parseFloat(quantity) <= 0) {
      formErrors.quantity = '请输入有效的数量';
      isValid = false;
    }

    if (!unit) {
      formErrors.unit = '请选择单位';
      isValid = false;
    }

    if (!expire_date) {
      formErrors.expire_date = '请选择过期日期';
      isValid = false;
    }

    this.setData({ formErrors, isFormSubmitted: true });

    // 检查过期日期是否合理
    if (isValid && expire_date) {
      const expireDate = new Date(expire_date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (expireDate < today) {
        return new Promise((resolve) => {
          wx.showModal({
            title: '提示',
            content: '过期日期早于今天，确定要保存吗？',
            showCancel: true,
            success: (res) => {
              resolve(res.confirm);
            }
          });
        });
      }
    }

    return isValid;
  },

  // 保存食材
  async saveIngredient() {
    const isValid = await this.validateForm();
    if (!isValid) return;

    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const formData = { ...this.data.formData };
      formData.quantity = parseFloat(formData.quantity);

      if (this.data.isEdit) {
        // 编辑食材
        await app.request({
          url: `/ingredients/${this.data.ingredientId}`,
          method: 'PUT',
          data: formData
        });
        app.showSuccess('更新成功');
      } else {
        // 添加食材
        await app.request({
          url: '/ingredients',
          method: 'POST',
          data: formData
        });
        app.showSuccess('添加成功');
        
        // 保存最近使用的选项
        this.saveRecentOption('category', formData.category);
        this.saveRecentOption('unit', formData.unit);
        if (formData.location) {
          this.saveRecentOption('location', formData.location);
        }
      }

      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } catch (error) {
      console.error('保存食材失败:', error);
      app.showError('保存失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 扫码添加
  async scanCode() {
    try {
      const res = await wx.scanCode({
        scanType: ['barCode', 'qrCode']
      });

      const code = res.result;
      console.log('扫码结果:', code);
      
      this.setData({ loading: true });
      
      // 模拟调用商品信息API获取食材信息
      setTimeout(() => {
        // 这里应该是真实的API调用，现在用模拟数据
        const mockData = {
          name: '示例食材',
          category: '其他',
          unit: '个',
          image_url: '/images/ingredients/default.png'
        };
        
        this.setData({
          'formData.name': mockData.name,
          'formData.category': mockData.category,
          'formData.unit': mockData.unit,
          'formData.image_url': mockData.image_url,
          loading: false
        });
        
        wx.showToast({
          title: '扫码成功',
          icon: 'success'
        });
      }, 1500);
    } catch (error) {
      console.error('扫码失败:', error);
      if (error.errMsg !== 'scanCode:fail cancel') {
        app.showError('扫码失败');
      }
    }
  },

  // 显示/隐藏扫码提示
  toggleScanTips() {
    this.setData({
      showScanTips: !this.data.showScanTips,
      showVoiceTips: false
    });
  },

  // 语音输入
  async voiceInput() {
    try {
      // 提示用户正在录音
      wx.showLoading({
        title: '正在录音...',
        mask: true
      });
      
      // 实际开发中应该调用真实的语音识别API
      setTimeout(() => {
        wx.hideLoading();
        
        // 模拟语音识别结果
        this.setData({
          'formData.name': '语音识别食材'
        });
        
        wx.showToast({
          title: '语音识别成功',
          icon: 'success'
        });
      }, 2000);
    } catch (error) {
      console.error('语音输入失败:', error);
      app.showError('语音输入失败');
    }
  },

  // 显示/隐藏语音提示
  toggleVoiceTips() {
    this.setData({
      showVoiceTips: !this.data.showVoiceTips,
      showScanTips: false
    });
  },

  // 显示/隐藏功能菜单
  toggleFeatureMenu() {
    this.setData({
      showFeatureMenu: !this.data.showFeatureMenu
    });
  },

  // 重置表单
  resetForm() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有输入内容吗？',
      success: (res) => {
        if (res.confirm) {
          const today = new Date().toISOString().split('T')[0];
          this.setData({
            formData: {
              name: '',
              category: '',
              quantity: '',
              unit: '',
              expire_date: '',
              purchase_date: today,
              location: '',
              notes: '',
              image_url: ''
            },
            formErrors: {},
            isFormSubmitted: false,
            expireDateWarning: false
          });
          
          wx.showToast({
            title: '已重置',
            icon: 'success'
          });
        }
      }
    });
  },
  
  // 页面分享
  onShareAppMessage() {
    return {
      title: '添加食材到冰箱',
      path: '/pages/ingredients/add/add',
      imageUrl: '/images/share/add-ingredient.png'
    };
  }
});