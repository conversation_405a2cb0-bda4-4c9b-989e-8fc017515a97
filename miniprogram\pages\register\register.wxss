/* 注册页面样式 */

.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 60rpx 30rpx 40rpx;
  box-sizing: border-box;
  position: relative;
  align-items: center;
}

.register-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="80" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.register-header {
  text-align: center;
  margin-bottom: 50rpx;
}

.logo {
  width: 140rpx;
  height: 140rpx;
  margin: 0 auto 30rpx;
  font-size: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.logo::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.app-name {
  display: block;
  font-size: 44rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 12rpx;
}

.app-desc {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.register-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 28rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 580rpx;
  position: relative;
  z-index: 1;
}

.form-group {
  margin-bottom: 32rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  border: 2rpx solid rgba(102, 126, 234, 0.2);
  border-radius: 20rpx;
  background: linear-gradient(145deg, #f8f9ff, #ffffff);
  transition: all 0.3s ease;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  height: 96rpx;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1), inset 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  transform: translateY(-2rpx);
}

.input-icon {
  width: 44rpx;
  height: 44rpx;
  margin: 0 24rpx 0 20rpx;
  opacity: 0.7;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.input-wrapper:focus-within .input-icon {
  opacity: 1;
  transform: scale(1.1);
}

.input-wrapper .form-input {
  flex: 1;
  padding: 0 20rpx;
  font-size: 32rpx !important;
  background-color: transparent;
  border: none !important;
  color: #333;
  font-weight: 500;
  line-height: 96rpx;
  height: 96rpx;
  box-sizing: border-box;
  width: auto !important;
}

.input-wrapper .form-input::placeholder {
  color: #999;
  font-weight: 400;
  font-size: 32rpx;
}

.password-toggle {
  width: 44rpx;
  height: 44rpx;
  margin: 0 20rpx;
  opacity: 0.6;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 50%;
  flex-shrink: 0;
}

.password-toggle:hover {
  opacity: 1;
  background: rgba(102, 126, 234, 0.1);
}

.agreement-section {
  margin: 40rpx 0;
  padding: 24rpx;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 16rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  line-height: 1.6;
  flex-wrap: wrap;
}

.agreement-checkbox checkbox {
  margin-right: 16rpx;
  margin-top: 4rpx;
  transform: scale(1.2);
  flex-shrink: 0;
}

.agreement-content {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.agreement-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-right: 8rpx;
}

.agreement-link {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 500;
  text-decoration: underline;
  text-underline-offset: 2rpx;
  transition: all 0.3s ease;
  position: relative;
  margin-right: 8rpx;
}

.agreement-link:active {
  color: #764ba2;
  text-shadow: 0 0 4rpx rgba(102, 126, 234, 0.3);
}

.register-btn {
  margin-top: 30rpx;
  padding: 36rpx;
  font-size: 30rpx;
  font-weight: 600;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
  border: none;
  color: white;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.register-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.register-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.register-btn:active::before {
  left: 100%;
}

.register-btn.btn-disabled {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  color: #666;
  cursor: not-allowed;
  opacity: 0.6;
}

.register-btn.btn-disabled:active {
  transform: none;
  box-shadow: none;
}

.login-link-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40rpx;
  gap: 8rpx;
}

.login-prompt {
  font-size: 26rpx;
  color: #666;
}

.login-link {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 500;
  position: relative;
  transition: all 0.3s ease;
}

.login-link::after {
  content: '';
  position: absolute;
  bottom: -4rpx;
  left: 0;
  width: 0;
  height: 2rpx;
  background: #667eea;
  transition: width 0.3s ease;
}

.login-link:active::after {
  width: 100%;
}

.login-link:active {
  color: #764ba2;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .register-container {
    padding: 40rpx 20rpx 30rpx;
  }

  .register-form {
    padding: 40rpx 30rpx;
    max-width: 100%;
  }

  .register-header {
    margin-bottom: 40rpx;
  }

  .form-group {
    margin-bottom: 28rpx;
  }

  .input-icon {
    margin: 0 20rpx 0 16rpx;
  }

  .form-input {
    padding: 28rpx 16rpx;
    font-size: 30rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .register-form {
    background-color: #2d2d2d;
  }
  
  .input-wrapper {
    background-color: #404040;
    border-color: #555;
  }
  
  .input-wrapper:focus-within {
    background-color: #2d2d2d;
  }
  
  .form-input {
    color: white;
  }
  
  .agreement-text {
    color: #ccc;
  }
}