<!--菜谱添加/编辑页面-->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-title">{{isEdit ? '编辑菜谱' : '添加菜谱'}}</view>
    <view class="header-actions">
      <button class="action-btn" bindtap="resetForm">
        <image src="/images/icons/reset.png" mode="aspectFit"></image>
      </button>
      <button class="action-btn" bindtap="previewRecipe">
        <image src="/images/icons/preview.png" mode="aspectFit"></image>
      </button>
    </view>
  </view>

  <!-- 表单内容 -->
  <scroll-view class="form-scroll" scroll-y>
    <view class="form-container">
      
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>
        
        <!-- 菜谱名称 -->
        <view class="form-item">
          <view class="item-label">菜谱名称 <text class="required">*</text>
          <input 
            class="item-input" 
            placeholder="请输入菜谱名称"
            value="{{formData.name}}"
            bindinput="onNameInput"
            maxlength="50"
          />
        </view>
        
        <!-- 菜谱描述 -->
        <view class="form-item">
          <view class="item-label">菜谱描述</view>
          <textarea 
            class="item-textarea" 
            placeholder="请输入菜谱描述"
            value="{{formData.description}}"
            bindinput="onDescriptionInput"
            maxlength="200"
            show-confirm-bar="{{false}}"
          ></textarea>
        </view>
        
        <!-- 菜谱图片 -->
        <view class="form-item">
          <view class="item-label">菜谱图片</view>
          <view class="image-upload" bindtap="chooseImage">
            <image wx:if="{{formData.image}}" src="{{formData.image}}" class="uploaded-image" mode="aspectFill"></image>
            <view wx:else class="upload-placeholder">
              <text class="upload-icon">📷</text>
              <text class="upload-text">点击上传图片</text>
            </view>
          </view>
        </view>
        
        <!-- 选择器行 -->
        <view class="selector-row">
          <!-- 分类选择 -->
          <view class="selector-item" bindtap="showCategoryPicker">
            <view class="selector-label">分类 <text class="required">*</text>
            <view class="selector-value">{{getCategoryName(formData.category)}}</view>
            <image src="/images/icons/arrow-right.png" class="selector-arrow" mode="aspectFit"></image>
          </view>
          
          <!-- 难度选择 -->
          <view class="selector-item" bindtap="showDifficultyPicker">
            <view class="selector-label">难度</view>
            <view class="selector-value">{{getDifficultyName(formData.difficulty)}}</view>
            <image src="/images/icons/arrow-right.png" class="selector-arrow" mode="aspectFit"></image>
          </view>
        </view>
        
        <view class="selector-row">
          <!-- 时间选择 -->
          <view class="selector-item" bindtap="showTimePicker">
            <view class="selector-label">时间</view>
            <view class="selector-value">{{formData.cookTime}}分钟</view>
            <image src="/images/icons/arrow-right.png" class="selector-arrow" mode="aspectFit"></image>
          </view>
          
          <!-- 份数选择 -->
          <view class="selector-item" bindtap="showServingPicker">
            <view class="selector-label">份数</view>
            <view class="selector-value">{{formData.servings}}人份</view>
            <image src="/images/icons/arrow-right.png" class="selector-arrow" mode="aspectFit"></image>
          </view>
        </view>
      </view>
      
      <!-- 食材配料 -->
      <view class="form-section">
        <view class="section-title">
          食材配料 <text class="required">*</text>
          <text class="section-count">({{formData.ingredients.length}})</text>
        </view>
        
        <!-- 食材列表 -->
        <view class="ingredients-list">
          <view wx:for="{{formData.ingredients}}" wx:key="id" class="ingredient-item">
            <view class="ingredient-info">
              <view class="ingredient-name">{{item.name}}</view>
              <view class="ingredient-amount">{{item.amount}}{{item.unit}}</view>
            </view>
            <button class="remove-btn" bindtap="removeIngredient" data-index="{{index}}">
              <image src="/images/icons/delete.png" mode="aspectFit"></image>
            </button>
          </view>
        </view>
        
        <!-- 添加食材 -->
        <view class="add-ingredient">
          <view class="ingredient-inputs">
            <input 
              class="ingredient-name-input" 
              placeholder="食材名称"
              value="{{newIngredient.name}}"
              bindinput="onIngredientNameInput"
            />
            <input 
              class="ingredient-amount-input" 
              placeholder="用量"
              value="{{newIngredient.amount}}"
              bindinput="onIngredientAmountInput"
            />
            <input 
              class="ingredient-unit-input" 
              placeholder="单位"
              value="{{newIngredient.unit}}"
              bindinput="onIngredientUnitInput"
            />
            <button class="add-btn" bindtap="addIngredient">添加</button>
          </view>
          
          <!-- 可选食材 -->
          <view wx:if="{{availableIngredients.length > 0}}" class="available-ingredients">
            <view class="available-title">可选食材：</view>
            <view class="available-list">
              <view 
                wx:for="{{availableIngredients}}" 
                wx:key="id" 
                class="available-item"
                bindtap="selectAvailableIngredient"
                data-ingredient="{{item}}"
              >
                {{item.name}}
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 制作步骤 -->
      <view class="form-section">
        <view class="section-title">
          制作步骤 <text class="required">*</text>
          <text class="section-count">({{formData.steps.length}})</text>
        </view>
        
        <!-- 步骤列表 -->
        <view class="steps-list">
          <view wx:for="{{formData.steps}}" wx:key="id" class="step-item">
            <view class="step-number">{{item.order}}</view>
            <view class="step-content">
              <view class="step-description">{{item.description}}</view>
            </view>
            <view class="step-actions">
              <button wx:if="{{index > 0}}" class="step-action-btn" bindtap="moveStepUp" data-index="{{index}}">
                <image src="/images/icons/arrow-up.png" mode="aspectFit"></image>
              </button>
              <button wx:if="{{index < formData.steps.length - 1}}" class="step-action-btn" bindtap="moveStepDown" data-index="{{index}}">
                <image src="/images/icons/arrow-down.png" mode="aspectFit"></image>
              </button>
              <button class="step-action-btn delete" bindtap="removeStep" data-index="{{index}}">
                <image src="/images/icons/delete.png" mode="aspectFit"></image>
              </button>
            </view>
          </view>
        </view>
        
        <!-- 添加步骤 -->
        <view class="add-step">
          <textarea 
            class="step-input" 
            placeholder="请输入制作步骤"
            value="{{newStep}}"
            bindinput="onStepInput"
            maxlength="200"
            show-confirm-bar="{{false}}"
          ></textarea>
          <button class="add-step-btn" bindtap="addStep">添加步骤</button>
        </view>
      </view>
      
      <!-- 营养信息 -->
      <view class="form-section">
        <view class="section-title">营养信息</view>
        
        <view class="nutrition-grid">
          <view class="nutrition-item">
            <view class="nutrition-label">热量(kcal)</view>
            <input 
              class="nutrition-input" 
              type="digit"
              placeholder="0"
              value="{{formData.nutrition.calories}}"
              bindinput="onNutritionInput"
              data-field="calories"
            />
          </view>
          
          <view class="nutrition-item">
            <view class="nutrition-label">蛋白质(g)</view>
            <input 
              class="nutrition-input" 
              type="digit"
              placeholder="0"
              value="{{formData.nutrition.protein}}"
              bindinput="onNutritionInput"
              data-field="protein"
            />
          </view>
          
          <view class="nutrition-item">
            <view class="nutrition-label">碳水(g)</view>
            <input 
              class="nutrition-input" 
              type="digit"
              placeholder="0"
              value="{{formData.nutrition.carbs}}"
              bindinput="onNutritionInput"
              data-field="carbs"
            />
          </view>
          
          <view class="nutrition-item">
            <view class="nutrition-label">脂肪(g)</view>
            <input 
              class="nutrition-input" 
              type="digit"
              placeholder="0"
              value="{{formData.nutrition.fat}}"
              bindinput="onNutritionInput"
              data-field="fat"
            />
          </view>
          
          <view class="nutrition-item">
            <view class="nutrition-label">纤维(g)</view>
            <input 
              class="nutrition-input" 
              type="digit"
              placeholder="0"
              value="{{formData.nutrition.fiber}}"
              bindinput="onNutritionInput"
              data-field="fiber"
            />
          </view>
        </view>
      </view>
      
      <!-- 标签 -->
      <view class="form-section">
        <view class="section-title">
          标签
          <button class="add-tag-btn" bindtap="showTagInput">
            <image src="/images/icons/add.png" mode="aspectFit"></image>
          </button>
        </view>
        
        <!-- 标签列表 -->
        <view class="tags-container">
          <view wx:for="{{formData.tags}}" wx:key="*this" class="tag-item">
            <text class="tag-text">{{item}}</text>
            <button class="tag-remove" bindtap="removeTag" data-index="{{index}}">×</button>
          </view>
        </view>
        
        <!-- 标签输入 -->
        <view wx:if="{{showTagInput}}" class="tag-input-container">
          <input 
            class="tag-input" 
            placeholder="输入标签"
            value="{{newTag}}"
            bindinput="onTagInput"
            maxlength="10"
          />
          <button class="tag-confirm-btn" bindtap="addTag">确定</button>
          <button class="tag-cancel-btn" bindtap="cancelTagInput">取消</button>
        </view>
      </view>
    </view>
  </view>
  </scroll-view>
  
  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="cancel-btn" bindtap="resetForm">重置</button>
    <button class="save-btn" bindtap="saveRecipe" loading="{{loading}}">
      {{isEdit ? '更新菜谱' : '保存菜谱'}}
    </button>
  </view>
</view>

<!-- 分类选择器 -->
<picker 
  wx:if="{{showCategoryPicker}}"
  range="{{categories}}"
  range-key="name"
  bindchange="onCategoryChange"
  bindcancel="onCategoryCancel"
>
  <view></view>
</picker>

<!-- 难度选择器 -->
<picker 
  wx:if="{{showDifficultyPicker}}"
  range="{{difficulties}}"
  range-key="name"
  bindchange="onDifficultyChange"
  bindcancel="onDifficultyCancel"
>
  <view></view>
</picker>

<!-- 时间选择器 -->
<picker 
  wx:if="{{showTimePicker}}"
  range="{{timeOptions}}"
  bindchange="onTimeChange"
  bindcancel="onTimeCancel"
>
  <view></view>
</picker>

<!-- 份数选择器 -->
<picker 
  wx:if="{{showServingPicker}}"
  range="{{servingOptions}}"
  bindchange="onServingChange"
  bindcancel="onServingCancel"
>
  <view></view>
</picker>

<!-- 加载遮罩 -->
<view wx:if="{{loading}}" class="loading-overlay">
  <view class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{isEdit ? '更新中...' : '保存中...'}}</text>
  </view>
</view>