// 注册页面逻辑
const app = getApp();

Page({
  data: {
    phone: '',
    nickname: '',
    password: '',
    confirmPassword: '',
    loading: false,
    showPassword: false,
    showConfirmPassword: false,
    agreed: false
  },

  onLoad() {
    console.log('注册页面加载');
  },

  // 输入手机号
  onPhoneInput(e) {
    this.setData({
      phone: e.detail.value
    });
  },

  // 输入昵称
  onNicknameInput(e) {
    this.setData({
      nickname: e.detail.value
    });
  },

  // 输入密码
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 输入确认密码
  onConfirmPasswordInput(e) {
    this.setData({
      confirmPassword: e.detail.value
    });
  },

  // 切换密码显示
  togglePasswordShow() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 切换确认密码显示
  toggleConfirmPasswordShow() {
    this.setData({
      showConfirmPassword: !this.data.showConfirmPassword
    });
  },

  // 同意协议
  onAgreeChange(e) {
    const agreed = e.detail.value.length > 0; // checkbox-group返回数组
    this.setData({
      agreed: agreed
    });
    console.log('协议同意状态:', agreed);
  },

  // 验证表单
  validateForm() {
    const { phone, nickname, password, confirmPassword, agreed } = this.data;

    if (!phone.trim()) {
      app.showError('请输入手机号');
      return false;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone.trim())) {
      app.showError('请输入正确的手机号');
      return false;
    }

    if (!password.trim()) {
      app.showError('请输入密码');
      return false;
    }

    if (password.trim().length < 6) {
      app.showError('密码至少6个字符');
      return false;
    }

    if (password !== confirmPassword) {
      app.showError('两次输入的密码不一致');
      return false;
    }

    if (!agreed) {
      app.showError('请同意用户协议和隐私政策');
      return false;
    }

    return true;
  },

  // 注册
  async handleRegister() {
    // 首先检查是否同意协议
    if (!this.data.agreed) {
      wx.showModal({
        title: '提示',
        content: '请先阅读并同意《用户协议》和《隐私政策》',
        showCancel: false,
        confirmText: '我知道了'
      });
      return;
    }

    if (!this.validateForm()) {
      return;
    }

    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const { phone, nickname, password } = this.data;

      // 密码将在服务端进行加密和验证
      await app.register({
        phone: phone.trim(),
        nickname: nickname.trim() || null,
        password: password.trim()
      });

      // 注册成功，跳转到登录页面
      wx.showModal({
        title: '注册成功',
        content: '请使用您的手机号和密码登录',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    } catch (error) {
      console.error('注册失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },

  // 返回登录页面
  goToLogin() {
    wx.navigateBack();
  },

  // 查看用户协议
  viewUserAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议的内容...',
      showCancel: false
    });
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '这里是隐私政策的内容...',
      showCancel: false
    });
  }
});