// 注册页面逻辑
const app = getApp();

Page({
  data: {
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    loading: false,
    showPassword: false,
    showConfirmPassword: false,
    agreed: false
  },

  onLoad() {
    console.log('注册页面加载');
  },

  // 输入用户名
  onUsernameInput(e) {
    this.setData({
      username: e.detail.value
    });
  },

  // 输入邮箱
  onEmailInput(e) {
    this.setData({
      email: e.detail.value
    });
  },

  // 输入密码
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 输入确认密码
  onConfirmPasswordInput(e) {
    this.setData({
      confirmPassword: e.detail.value
    });
  },

  // 切换密码显示
  togglePasswordShow() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 切换确认密码显示
  toggleConfirmPasswordShow() {
    this.setData({
      showConfirmPassword: !this.data.showConfirmPassword
    });
  },

  // 同意协议
  onAgreeChange(e) {
    this.setData({
      agreed: e.detail.value
    });
  },

  // 验证表单
  validateForm() {
    const { username, email, password, confirmPassword, agreed } = this.data;

    if (!username.trim()) {
      app.showError('请输入用户名');
      return false;
    }

    if (username.trim().length < 3) {
      app.showError('用户名至少3个字符');
      return false;
    }

    if (!email.trim()) {
      app.showError('请输入邮箱');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      app.showError('请输入有效的邮箱地址');
      return false;
    }

    if (!password.trim()) {
      app.showError('请输入密码');
      return false;
    }

    if (password.trim().length < 6) {
      app.showError('密码至少6个字符');
      return false;
    }

    if (password !== confirmPassword) {
      app.showError('两次输入的密码不一致');
      return false;
    }

    if (!agreed) {
      app.showError('请同意用户协议和隐私政策');
      return false;
    }

    return true;
  },

  // 注册
  async handleRegister() {
    if (!this.validateForm()) {
      return;
    }

    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const { username, email, password } = this.data;
      
      await app.register({
        username: username.trim(),
        email: email.trim(),
        password: password.trim()
      });

      // 注册成功，跳转到登录页面
      wx.showModal({
        title: '注册成功',
        content: '请使用您的账号密码登录',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    } catch (error) {
      console.error('注册失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },

  // 返回登录页面
  goToLogin() {
    wx.navigateBack();
  },

  // 查看用户协议
  viewUserAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议的内容...',
      showCancel: false
    });
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '这里是隐私政策的内容...',
      showCancel: false
    });
  }
});