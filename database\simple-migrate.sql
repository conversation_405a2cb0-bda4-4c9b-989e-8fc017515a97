-- 简化的用户收藏表迁移脚本
USE smart_fridge;

-- 显示当前表结构
DESCRIBE user_favorites;

-- 添加缺失字段（忽略已存在的字段错误）
ALTER TABLE user_favorites ADD COLUMN type varchar(20) NOT NULL DEFAULT 'recipe' COMMENT '收藏类型' AFTER user_id;
ALTER TABLE user_favorites ADD COLUMN target_id int(11) NOT NULL DEFAULT 0 COMMENT '目标ID' AFTER type;
ALTER TABLE user_favorites ADD COLUMN title varchar(255) NULL COMMENT '收藏标题' AFTER recipe_id;
ALTER TABLE user_favorites ADD COLUMN description text NULL COMMENT '收藏描述' AFTER title;
ALTER TABLE user_favorites ADD COLUMN image_url varchar(500) NULL COMMENT '收藏图片URL' AFTER description;
ALTER TABLE user_favorites ADD COLUMN tags json NULL COMMENT '标签' AFTER image_url;
ALTER TABLE user_favorites ADD COLUMN notes text NULL COMMENT '用户备注' AFTER tags;
ALTER TABLE user_favorites ADD COLUMN is_deleted tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除' AFTER notes;
ALTER TABLE user_favorites ADD COLUMN updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER created_at;

-- 更新现有数据
UPDATE user_favorites SET target_id = recipe_id WHERE recipe_id IS NOT NULL AND target_id = 0;

-- 显示更新后的表结构
DESCRIBE user_favorites;
