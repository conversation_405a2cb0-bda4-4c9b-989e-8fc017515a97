/**app.wxss**/
/* 引入通用样式 */
@import "styles/common.wxss";

/* 全局样式 */

/* 重置样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 容器样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

.page-container {
  padding: 0 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-content {
  color: #666;
  line-height: 1.8;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.btn-primary {
  background-color: #4CAF50;
  color: #fff;
}

.btn-primary:hover {
  background-color: #45a049;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

.btn-danger {
  background-color: #ff4757;
  color: #fff;
}

.btn-danger:hover {
  background-color: #ff3742;
}

.btn-warning {
  background-color: #ffa502;
  color: #fff;
}

.btn-warning:hover {
  background-color: #ff9500;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 28rpx 56rpx;
  font-size: 32rpx;
}

.btn-block {
  width: 100%;
  display: block;
}

.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 表单样式 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #4CAF50;
  outline: none;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
  resize: vertical;
}

.form-textarea:focus {
  border-color: #4CAF50;
  outline: none;
}

.form-select {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

/* 列表样式 */
.list {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:hover {
  background-color: #f9f9f9;
}

.list-item-content {
  flex: 1;
  margin-right: 20rpx;
}

.list-item-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.list-item-action {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* 状态样式 */
.status {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-success {
  background-color: #e8f5e8;
  color: #2ed573;
}

.status-warning {
  background-color: #fff3e0;
  color: #ffa502;
}

.status-danger {
  background-color: #ffebee;
  color: #ff4757;
}

.status-info {
  background-color: #e3f2fd;
  color: #2196f3;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  background-color: #f0f0f0;
  color: #666;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.tag-primary {
  background-color: #e8f5e8;
  color: #4CAF50;
}

.tag-secondary {
  background-color: #f0f0f0;
  color: #666;
}

/* 图标样式 */
.icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}

.icon-small {
  width: 32rpx;
  height: 32rpx;
}

.icon-large {
  width: 48rpx;
  height: 48rpx;
}

/* 头像样式 */
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.avatar-small {
  width: 60rpx;
  height: 60rpx;
}

.avatar-large {
  width: 120rpx;
  height: 120rpx;
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: #e0e0e0;
  margin: 30rpx 0;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.loading-text {
  margin-left: 20rpx;
  color: #666;
  font-size: 26rpx;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: #4CAF50;
}

.text-secondary {
  color: #666;
}

.text-danger {
  color: #ff4757;
}

.text-warning {
  color: #ffa502;
}

.text-success {
  color: #2ed573;
}

.text-small {
  font-size: 24rpx;
}

.text-large {
  font-size: 32rpx;
}

.font-bold {
  font-weight: 600;
}

.font-normal {
  font-weight: 400;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: 10rpx; }
.m-2 { margin: 20rpx; }
.m-3 { margin: 30rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10rpx; }
.mt-2 { margin-top: 20rpx; }
.mt-3 { margin-top: 30rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10rpx; }
.mb-2 { margin-bottom: 20rpx; }
.mb-3 { margin-bottom: 30rpx; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 10rpx; }
.ml-2 { margin-left: 20rpx; }
.ml-3 { margin-left: 30rpx; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 10rpx; }
.mr-2 { margin-right: 20rpx; }
.mr-3 { margin-right: 30rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 10rpx; }
.p-2 { padding: 20rpx; }
.p-3 { padding: 30rpx; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 10rpx; }
.pt-2 { padding-top: 20rpx; }
.pt-3 { padding-top: 30rpx; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 10rpx; }
.pb-2 { padding-bottom: 20rpx; }
.pb-3 { padding-bottom: 30rpx; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 10rpx; }
.pl-2 { padding-left: 20rpx; }
.pl-3 { padding-left: 30rpx; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 10rpx; }
.pr-2 { padding-right: 20rpx; }
.pr-3 { padding-right: 30rpx; }

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

/* 显示隐藏 */
.hidden {
  display: none;
}

.visible {
  display: block;
}

/* 圆角 */
.rounded {
  border-radius: 12rpx;
}

.rounded-full {
  border-radius: 50%;
}

/* 阴影 */
.shadow {
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}

/* 边框 */
.border {
  border: 1rpx solid #e0e0e0;
}

.border-top {
  border-top: 1rpx solid #e0e0e0;
}

.border-bottom {
  border-bottom: 1rpx solid #e0e0e0;
}

.border-left {
  border-left: 1rpx solid #e0e0e0;
}

.border-right {
  border-right: 1rpx solid #e0e0e0;
}

/* 背景色 */
.bg-white {
  background-color: #fff;
}

.bg-gray {
  background-color: #f5f5f5;
}

.bg-primary {
  background-color: #4CAF50;
}

.bg-secondary {
  background-color: #f0f0f0;
}

/* 响应式 */
@media (max-width: 750rpx) {
  .container {
    padding: 15rpx;
  }
  
  .card {
    padding: 20rpx;
  }
  
  .btn {
    padding: 16rpx 32rpx;
    font-size: 26rpx;
  }
}