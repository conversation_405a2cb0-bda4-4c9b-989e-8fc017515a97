// 收藏列表页面
const app = getApp();

Page({
  data: {
    // 收藏数据
    favorites: [],
    
    // 分类筛选
    categories: [
      { id: 'all', name: '全部' },
      { id: 'recipes', name: '菜谱' },
      { id: 'ingredients', name: '食材' }
    ],
    currentCategory: 'all',
    
    // 页面状态
    loading: false,
    refreshing: false,
    loadingMore: false,
    hasMore: true,
    
    // 分页参数
    page: 1,
    pageSize: 20,
    
    // 搜索
    searchKeyword: '',
    showSearch: false,
    
    // 编辑模式
    editMode: false,
    selectedItems: []
  },

  onLoad() {
    console.log('收藏列表页面加载');
    this.loadFavorites();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadMore();
    }
  },

  // 加载收藏列表
  async loadFavorites(isRefresh = false) {
    try {
      if (isRefresh) {
        this.setData({ 
          refreshing: true,
          page: 1,
          hasMore: true
        });
      } else {
        this.setData({ loading: true });
      }

      const params = {
        page: isRefresh ? 1 : this.data.page,
        pageSize: this.data.pageSize,
        category: this.data.currentCategory === 'all' ? '' : this.data.currentCategory,
        keyword: this.data.searchKeyword
      };

      const result = await app.api.getFavorites(params);
      
      const newFavorites = isRefresh ? result.data : [...this.data.favorites, ...result.data];
      
      this.setData({
        favorites: newFavorites,
        hasMore: result.hasMore,
        loading: false,
        refreshing: false
      });

      if (isRefresh) {
        wx.stopPullDownRefresh();
      }

    } catch (error) {
      console.error('加载收藏列表失败:', error);
      app.showError('加载失败');
      this.setData({ 
        loading: false,
        refreshing: false
      });
      
      if (isRefresh) {
        wx.stopPullDownRefresh();
      }
    }
  },

  // 刷新数据
  refreshData() {
    this.loadFavorites(true);
  },

  // 加载更多
  async loadMore() {
    try {
      this.setData({ 
        loadingMore: true,
        page: this.data.page + 1
      });

      const params = {
        page: this.data.page,
        pageSize: this.data.pageSize,
        category: this.data.currentCategory === 'all' ? '' : this.data.currentCategory,
        keyword: this.data.searchKeyword
      };

      const result = await app.api.getFavorites(params);
      
      this.setData({
        favorites: [...this.data.favorites, ...result.data],
        hasMore: result.hasMore,
        loadingMore: false
      });

    } catch (error) {
      console.error('加载更多失败:', error);
      this.setData({ 
        loadingMore: false,
        page: this.data.page - 1
      });
    }
  },

  // 切换分类
  switchCategory(e) {
    const category = e.currentTarget.dataset.category;
    if (category === this.data.currentCategory) return;

    this.setData({
      currentCategory: category,
      page: 1,
      favorites: []
    });

    this.loadFavorites();
  },

  // 显示搜索
  showSearch() {
    this.setData({ showSearch: true });
  },

  // 隐藏搜索
  hideSearch() {
    this.setData({ 
      showSearch: false,
      searchKeyword: ''
    });
    
    if (this.data.searchKeyword) {
      this.refreshData();
    }
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value });
  },

  // 执行搜索
  onSearch() {
    this.setData({
      page: 1,
      favorites: []
    });
    this.loadFavorites();
  },

  // 清空搜索
  clearSearch() {
    this.setData({ searchKeyword: '' });
    this.onSearch();
  },

  // 切换编辑模式
  toggleEditMode() {
    this.setData({
      editMode: !this.data.editMode,
      selectedItems: []
    });
  },

  // 选择收藏项
  selectItem(e) {
    if (!this.data.editMode) return;

    const id = e.currentTarget.dataset.id;
    const selectedItems = [...this.data.selectedItems];
    const index = selectedItems.indexOf(id);

    if (index > -1) {
      selectedItems.splice(index, 1);
    } else {
      selectedItems.push(id);
    }

    this.setData({ selectedItems });
  },

  // 全选/取消全选
  toggleSelectAll() {
    const allSelected = this.data.selectedItems.length === this.data.favorites.length;
    
    this.setData({
      selectedItems: allSelected ? [] : this.data.favorites.map(item => item.id)
    });
  },

  // 批量删除收藏
  async batchDelete() {
    if (this.data.selectedItems.length === 0) {
      app.showError('请选择要删除的收藏');
      return;
    }

    const result = await wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的 ${this.data.selectedItems.length} 个收藏吗？`,
      confirmText: '删除',
      confirmColor: '#ff4757'
    });

    if (!result.confirm) return;

    try {
      await app.api.batchDeleteFavorites(this.data.selectedItems);
      
      app.showSuccess('删除成功');
      
      this.setData({
        editMode: false,
        selectedItems: []
      });
      
      this.refreshData();

    } catch (error) {
      console.error('批量删除失败:', error);
      app.showError('删除失败');
    }
  },

  // 点击收藏项
  onItemTap(e) {
    if (this.data.editMode) {
      this.selectItem(e);
      return;
    }

    const item = e.currentTarget.dataset.item;
    
    if (item.type === 'recipe') {
      wx.navigateTo({
        url: `/pages/recipes/detail/detail?id=${item.targetId}`
      });
    } else if (item.type === 'ingredient') {
      wx.navigateTo({
        url: `/pages/ingredients/detail/detail?id=${item.targetId}`
      });
    }
  },

  // 取消收藏
  async removeFavorite(e) {
    e.stopPropagation();
    
    const id = e.currentTarget.dataset.id;
    const item = this.data.favorites.find(fav => fav.id === id);
    
    const result = await wx.showModal({
      title: '取消收藏',
      content: `确定要取消收藏"${item.title}"吗？`,
      confirmText: '取消收藏',
      confirmColor: '#ff4757'
    });

    if (!result.confirm) return;

    try {
      await app.api.removeFavorite(id);
      
      app.showSuccess('已取消收藏');
      
      // 从列表中移除
      const favorites = this.data.favorites.filter(fav => fav.id !== id);
      this.setData({ favorites });

    } catch (error) {
      console.error('取消收藏失败:', error);
      app.showError('操作失败');
    }
  },

  // 格式化时间
  formatTime(timestamp) {
    return app.utils.formatTime(timestamp);
  },

  // 获取类型图标
  getTypeIcon(type) {
    const icons = {
      recipe: '🍳',
      ingredient: '🥬'
    };
    return icons[type] || '📋';
  },

  // 获取类型名称
  getTypeName(type) {
    const names = {
      recipe: '菜谱',
      ingredient: '食材'
    };
    return names[type] || '未知';
  }
});