const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 获取用户收藏列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      type,
      search,
      sort = 'created_at',
      order = 'DESC' 
    } = req.query;
    
    const offset = (page - 1) * limit;
    let whereConditions = ['f.user_id = ?', 'f.is_deleted = 0'];
    let queryParams = [req.user.id];

    // 按类型筛选
    if (type) {
      whereConditions.push('f.type = ?');
      queryParams.push(type);
    }

    // 搜索功能
    if (search) {
      whereConditions.push('f.title LIKE ?');
      queryParams.push(`%${search}%`);
    }

    const whereClause = whereConditions.join(' AND ');
    const orderClause = `ORDER BY f.${sort} ${order}`;

    const favorites = await query(`
      SELECT 
        f.*,
        CASE 
          WHEN f.type = 'recipe' THEN r.name
          WHEN f.type = 'ingredient' THEN ci.name
          ELSE f.title
        END as item_name,
        CASE 
          WHEN f.type = 'recipe' THEN r.image_url
          WHEN f.type = 'ingredient' THEN ci.image_url
          ELSE f.image_url
        END as item_image,
        CASE 
          WHEN f.type = 'recipe' THEN r.description
          WHEN f.type = 'ingredient' THEN ci.description
          ELSE f.description
        END as item_description
      FROM user_favorites f
      LEFT JOIN recipes r ON f.type = 'recipe' AND f.target_id = r.id AND r.is_deleted = 0
      LEFT JOIN common_ingredients ci ON f.type = 'ingredient' AND f.target_id = ci.id AND ci.is_deleted = 0
      WHERE ${whereClause}
      ${orderClause}
      LIMIT ? OFFSET ?
    `, [...queryParams, parseInt(limit), offset]);

    // 获取总数和分类统计
    const [countResult, statsResult] = await Promise.all([
      query(`
        SELECT COUNT(*) as total
        FROM user_favorites f
        WHERE ${whereClause}
      `, queryParams),
      query(`
        SELECT 
          type,
          COUNT(*) as count
        FROM user_favorites
        WHERE user_id = ? AND is_deleted = 0
        GROUP BY type
      `, [req.user.id])
    ]);

    const total = countResult[0].total;
    const typeStats = {};
    statsResult.forEach(stat => {
      typeStats[stat.type] = stat.count;
    });

    res.json({
      success: true,
      data: {
        favorites,
        statistics: {
          total_favorites: total,
          recipe_count: typeStats.recipe || 0,
          ingredient_count: typeStats.ingredient || 0,
          other_count: typeStats.other || 0
        },
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取收藏列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取收藏列表失败'
    });
  }
});

// 添加收藏
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      type,
      target_id,
      title,
      description,
      image_url,
      tags
    } = req.body;

    if (!type || !target_id) {
      return res.status(400).json({
        success: false,
        message: '收藏类型和目标ID不能为空'
      });
    }

    // 验证收藏类型
    const validTypes = ['recipe', 'ingredient', 'other'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({
        success: false,
        message: '无效的收藏类型'
      });
    }

    // 检查是否已经收藏
    const existingFavorites = await query(
      'SELECT id FROM user_favorites WHERE user_id = ? AND type = ? AND target_id = ? AND is_deleted = 0',
      [req.user.id, type, target_id]
    );

    if (existingFavorites.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该项目已在收藏列表中'
      });
    }

    // 验证目标是否存在
    let targetExists = false;
    let targetInfo = {};

    if (type === 'recipe') {
      const recipes = await query(
        'SELECT id, name, description, image_url FROM recipes WHERE id = ? AND is_deleted = 0',
        [target_id]
      );
      if (recipes.length > 0) {
        targetExists = true;
        targetInfo = {
          title: recipes[0].name,
          description: recipes[0].description,
          image_url: recipes[0].image_url
        };
      }
    } else if (type === 'ingredient') {
      const ingredients = await query(
        'SELECT id, name, description, image_url FROM common_ingredients WHERE id = ? AND is_deleted = 0',
        [target_id]
      );
      if (ingredients.length > 0) {
        targetExists = true;
        targetInfo = {
          title: ingredients[0].name,
          description: ingredients[0].description,
          image_url: ingredients[0].image_url
        };
      }
    } else {
      // 其他类型直接使用提供的信息
      targetExists = true;
      targetInfo = {
        title: title || '未命名收藏',
        description: description || '',
        image_url: image_url || null
      };
    }

    if (!targetExists && type !== 'other') {
      return res.status(404).json({
        success: false,
        message: '收藏目标不存在'
      });
    }

    // 添加收藏
    const result = await query(`
      INSERT INTO user_favorites 
      (user_id, type, target_id, title, description, image_url, tags)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      req.user.id,
      type,
      target_id,
      targetInfo.title,
      targetInfo.description,
      targetInfo.image_url,
      tags ? JSON.stringify(tags) : null
    ]);

    // 记录用户操作
    await query(
      'INSERT INTO user_records (user_id, type, content) VALUES (?, ?, ?)',
      [
        req.user.id,
        'favorite_add',
        JSON.stringify({
          favorite_id: result.insertId,
          favorite_type: type,
          target_id: target_id,
          title: targetInfo.title
        })
      ]
    );

    // 获取新添加的收藏信息
    const newFavorite = await query(`
      SELECT * FROM user_favorites WHERE id = ?
    `, [result.insertId]);

    res.status(201).json({
      success: true,
      message: '收藏添加成功',
      data: newFavorite[0]
    });
  } catch (error) {
    console.error('添加收藏错误:', error);
    res.status(500).json({
      success: false,
      message: '添加收藏失败'
    });
  }
});

// 更新收藏
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      description,
      image_url,
      tags,
      notes
    } = req.body;

    // 检查收藏是否存在且属于当前用户
    const existingFavorites = await query(
      'SELECT id FROM user_favorites WHERE id = ? AND user_id = ? AND is_deleted = 0',
      [id, req.user.id]
    );

    if (existingFavorites.length === 0) {
      return res.status(404).json({
        success: false,
        message: '收藏不存在'
      });
    }

    const updateFields = [];
    const updateValues = [];

    if (title !== undefined) {
      updateFields.push('title = ?');
      updateValues.push(title);
    }
    if (description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(description);
    }
    if (image_url !== undefined) {
      updateFields.push('image_url = ?');
      updateValues.push(image_url);
    }
    if (tags !== undefined) {
      updateFields.push('tags = ?');
      updateValues.push(tags ? JSON.stringify(tags) : null);
    }
    if (notes !== undefined) {
      updateFields.push('notes = ?');
      updateValues.push(notes);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有需要更新的字段'
      });
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id, req.user.id);

    await query(
      `UPDATE user_favorites SET ${updateFields.join(', ')} WHERE id = ? AND user_id = ?`,
      updateValues
    );

    // 获取更新后的收藏信息
    const updatedFavorite = await query(`
      SELECT * FROM user_favorites WHERE id = ?
    `, [id]);

    res.json({
      success: true,
      message: '收藏更新成功',
      data: updatedFavorite[0]
    });
  } catch (error) {
    console.error('更新收藏错误:', error);
    res.status(500).json({
      success: false,
      message: '更新收藏失败'
    });
  }
});

// 删除收藏
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // 检查收藏是否存在且属于当前用户
    const favorites = await query(
      'SELECT id, title FROM user_favorites WHERE id = ? AND user_id = ? AND is_deleted = 0',
      [id, req.user.id]
    );

    if (favorites.length === 0) {
      return res.status(404).json({
        success: false,
        message: '收藏不存在'
      });
    }

    // 软删除收藏
    await query(
      'UPDATE user_favorites SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
      [id, req.user.id]
    );

    res.json({
      success: true,
      message: '收藏删除成功'
    });
  } catch (error) {
    console.error('删除收藏错误:', error);
    res.status(500).json({
      success: false,
      message: '删除收藏失败'
    });
  }
});

// 批量删除收藏
router.post('/batch-delete', authenticateToken, async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的收藏ID列表'
      });
    }

    const placeholders = ids.map(() => '?').join(',');
    
    await query(
      `UPDATE user_favorites SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP 
       WHERE id IN (${placeholders}) AND user_id = ?`,
      [...ids, req.user.id]
    );

    res.json({
      success: true,
      message: '批量删除收藏成功'
    });
  } catch (error) {
    console.error('批量删除收藏错误:', error);
    res.status(500).json({
      success: false,
      message: '批量删除收藏失败'
    });
  }
});

// 检查是否已收藏
router.get('/check/:type/:target_id', authenticateToken, async (req, res) => {
  try {
    const { type, target_id } = req.params;

    // 目前只支持菜谱收藏，适配现有数据库结构
    if (type !== 'recipe') {
      return res.json({
        success: true,
        data: {
          is_favorited: false,
          favorite_id: null
        }
      });
    }

    const favorites = await query(
      'SELECT id FROM user_favorites WHERE user_id = ? AND recipe_id = ?',
      [req.user.id, target_id]
    );

    res.json({
      success: true,
      data: {
        is_favorited: favorites.length > 0,
        favorite_id: favorites.length > 0 ? favorites[0].id : null
      }
    });
  } catch (error) {
    console.error('检查收藏状态错误:', error);
    res.status(500).json({
      success: false,
      message: '检查收藏状态失败'
    });
  }
});

// 切换收藏状态
router.post('/toggle', authenticateToken, async (req, res) => {
  try {
    const {
      type,
      target_id
    } = req.body;

    if (!type || !target_id) {
      return res.status(400).json({
        success: false,
        message: '收藏类型和目标ID不能为空'
      });
    }

    // 目前只支持菜谱收藏，适配现有数据库结构
    if (type !== 'recipe') {
      return res.status(400).json({
        success: false,
        message: '目前只支持菜谱收藏'
      });
    }

    // 检查是否已经收藏
    const existingFavorites = await query(
      'SELECT id FROM user_favorites WHERE user_id = ? AND recipe_id = ?',
      [req.user.id, target_id]
    );

    if (existingFavorites.length > 0) {
      // 已收藏，取消收藏
      await query(
        'UPDATE user_favorites SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [existingFavorites[0].id]
      );

      res.json({
        success: true,
        message: '取消收藏成功',
        data: {
          action: 'removed',
          is_favorited: false
        }
      });
    } else {
      // 未收藏，添加收藏
      // 验证菜谱是否存在
      const recipes = await query(
        'SELECT id FROM recipes WHERE id = ? AND is_deleted = 0',
        [target_id]
      );

      if (recipes.length === 0) {
        return res.status(404).json({
          success: false,
          message: '菜谱不存在'
        });
      }

      const result = await query(`
        INSERT INTO user_favorites
        (user_id, recipe_id)
        VALUES (?, ?)
      `, [
        req.user.id,
        target_id
      ]);

      res.json({
        success: true,
        message: '添加收藏成功',
        data: {
          action: 'added',
          is_favorited: true,
          favorite_id: result.insertId
        }
      });
    }
  } catch (error) {
    console.error('切换收藏状态错误:', error);
    res.status(500).json({
      success: false,
      message: '操作失败'
    });
  }
});

// 获取收藏分类统计
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await query(`
      SELECT 
        type,
        COUNT(*) as count,
        DATE(created_at) as date
      FROM user_favorites
      WHERE user_id = ? AND is_deleted = 0
      GROUP BY type, DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `, [req.user.id]);

    // 按类型汇总
    const typeSummary = {};
    const dailyStats = {};

    stats.forEach(stat => {
      // 类型汇总
      if (typeSummary[stat.type]) {
        typeSummary[stat.type] += stat.count;
      } else {
        typeSummary[stat.type] = stat.count;
      }

      // 日期统计
      if (!dailyStats[stat.date]) {
        dailyStats[stat.date] = {};
      }
      dailyStats[stat.date][stat.type] = stat.count;
    });

    res.json({
      success: true,
      data: {
        type_summary: typeSummary,
        daily_stats: dailyStats,
        total_favorites: Object.values(typeSummary).reduce((sum, count) => sum + count, 0)
      }
    });
  } catch (error) {
    console.error('获取收藏统计错误:', error);
    res.status(500).json({
      success: false,
      message: '获取收藏统计失败'
    });
  }
});

module.exports = router;