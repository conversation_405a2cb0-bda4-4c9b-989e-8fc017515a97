<!--消费分析页面-->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">消费分析</text>
      <view class="header-actions">
        <image class="action-icon" src="/images/icons/refresh.png" bindtap="refreshData" />
        <image class="action-icon" src="/images/icons/export.png" />
      </view>
    </view>
    
    <!-- 时间范围选择 -->
    <view class="time-range-tabs">
      <view 
        wx:for="{{timeRanges}}" 
        wx:key="id"
        class="time-tab {{timeRange === item.id ? 'active' : ''}}"
        data-range="{{item.id}}"
        bindtap="switchTimeRange"
      >
        {{item.name}}
      </view>
    </view>
  </view>

  <!-- 内容滚动区域 -->
  <scroll-view class="content-scroll" scroll-y refresher-enabled refresher-triggered="{{refreshing}}" bindrefresherrefresh="refreshData">
    <view class="analysis-content">
      
      <!-- 加载状态 -->
      <view wx:if="{{loading}}" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在分析消费数据...</text>
      </view>

      <!-- 分析内容 -->
      <view wx:else>
        
        <!-- 总体统计 -->
        <view class="overview-section">
          <view class="section-header">
            <text class="section-title">消费概览</text>
          </view>
          <view class="overview-card">
            <view class="overview-item">
              <text class="overview-number">¥{{formatAmount(stats.totalExpense)}}</text>
              <text class="overview-label">总支出</text>
            </view>
            <view class="overview-divider"></view>
            <view class="overview-item">
              <text class="overview-number">¥{{formatAmount(stats.monthlyExpense)}}</text>
              <text class="overview-label">本月支出</text>
            </view>
            <view class="overview-divider"></view>
            <view class="overview-item" bindtap="viewShoppingRecords">
              <text class="overview-number">¥{{formatAmount(stats.averageDaily)}}</text>
              <text class="overview-label">日均支出</text>
            </view>
          </view>
        </view>

        <!-- 图表类型切换 -->
        <view class="chart-tabs">
          <view 
            wx:for="{{chartTypes}}" 
            wx:key="id"
            class="chart-tab {{chartType === item.id ? 'active' : ''}}"
            data-type="{{item.id}}"
            bindtap="switchChartType"
          >
            {{item.name}}
          </view>
        </view>

        <!-- 分类支出 -->
        <view wx:if="{{chartType === 'category'}}" class="chart-section">
          <view class="section-header">
            <text class="section-title">分类支出统计</text>
          </view>
          <view class="chart-container">
            <view wx:if="{{stats.categoryExpense.length === 0}}" class="empty-state">
              <image class="empty-icon" src="/images/icons/empty-chart.png" />
              <text class="empty-text">暂无支出数据</text>
            </view>
            <view wx:else>
              <view 
                wx:for="{{stats.categoryExpense}}" 
                wx:key="category"
                class="category-item"
                data-category="{{item.category}}"
                bindtap="viewCategoryDetail"
              >
                <view class="category-info">
                  <image class="category-icon" src="{{item.icon}}" />
                  <view class="category-details">
                    <text class="category-name">{{item.name}}</text>
                    <text class="category-amount">¥{{formatAmount(item.amount)}}</text>
                  </view>
                </view>
                <view class="category-progress">
                  <view class="progress-bar">
                    <view class="progress-fill" style="width: {{calculatePercentage(item.amount, stats.totalExpense)}}%"></view>
                  </view>
                  <text class="progress-text">{{calculatePercentage(item.amount, stats.totalExpense)}}%</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 趋势分析 -->
        <view wx:if="{{chartType === 'trend'}}" class="chart-section">
          <view class="section-header">
            <text class="section-title">消费趋势</text>
          </view>
          <view class="chart-container">
            <view wx:if="{{stats.monthlyTrend.length === 0}}" class="empty-state">
              <image class="empty-icon" src="/images/icons/empty-trend.png" />
              <text class="empty-text">暂无趋势数据</text>
            </view>
            <view wx:else class="trend-chart">
              <view 
                wx:for="{{stats.monthlyTrend}}" 
                wx:key="month"
                class="trend-item"
              >
                <view class="trend-bar">
                  <view class="trend-fill" style="height: {{(item.amount / stats.monthlyTrend[0].amount) * 100}}%"></view>
                </view>
                <text class="trend-label">{{formatMonth(item.month)}}</text>
                <text class="trend-value">¥{{formatAmount(item.amount)}}</text>
                <view class="trend-change" style="color: {{getTrendColor(item.trend)}}">
                  <image class="trend-icon" src="{{getTrendIcon(item.trend)}}" />
                  <text>{{item.trend > 0 ? '+' : ''}}{{item.trend}}%</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 支出排行 -->
        <view wx:if="{{chartType === 'top'}}" class="chart-section">
          <view class="section-header">
            <text class="section-title">支出排行榜</text>
          </view>
          <view class="chart-container">
            <view wx:if="{{stats.topExpenseItems.length === 0}}" class="empty-state">
              <image class="empty-icon" src="/images/icons/empty-list.png" />
              <text class="empty-text">暂无支出记录</text>
            </view>
            <view wx:else>
              <view 
                wx:for="{{stats.topExpenseItems}}" 
                wx:key="id"
                class="expense-item"
                data-id="{{item.id}}"
                bindtap="viewItemDetail"
              >
                <view class="expense-rank">{{index + 1}}</view>
                <image class="expense-image" src="{{item.image}}" mode="aspectFill" />
                <view class="expense-info">
                  <text class="expense-name">{{item.name}}</text>
                  <view class="expense-meta">
                    <text class="expense-category">{{item.category}}</text>
                    <text class="expense-count">购买{{item.count}}次</text>
                  </view>
                </view>
                <view class="expense-amount">
                  <text class="amount-number">¥{{formatAmount(item.totalAmount)}}</text>
                  <text class="amount-label">总支出</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 节省分析 -->
        <view wx:if="{{chartType === 'savings'}}" class="chart-section">
          <view class="section-header">
            <text class="section-title">节省分析</text>
          </view>
          <view class="chart-container">
            <!-- 节省统计 -->
            <view class="savings-overview">
              <view class="savings-item">
                <text class="savings-number">¥{{formatAmount(stats.savingsAnalysis.totalSavings)}}</text>
                <text class="savings-label">总节省金额</text>
              </view>
              <view class="savings-divider"></view>
              <view class="savings-item">
                <text class="savings-number">{{stats.savingsAnalysis.wasteReduction}}%</text>
                <text class="savings-label">浪费减少</text>
              </view>
            </view>
            
            <!-- 节省建议 -->
            <view class="suggestions-list">
              <view 
                wx:for="{{stats.savingsAnalysis.suggestions}}" 
                wx:key="id"
                class="suggestion-item {{item.type}}"
              >
                <image class="suggestion-icon" src="{{item.icon}}" />
                <view class="suggestion-content">
                  <text class="suggestion-title">{{item.title}}</text>
                  <text class="suggestion-desc">{{item.description}}</text>
                  <text class="suggestion-savings">预计节省: ¥{{formatAmount(item.potentialSavings)}}</text>
                </view>
                <view class="suggestion-action" bindtap="applySuggestion" data-id="{{item.id}}">
                  采纳
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 消费洞察 -->
        <view class="insights-section">
          <view class="section-header">
            <text class="section-title">消费洞察</text>
          </view>
          <view class="insights-container">
            <view class="insight-item">
              <view class="insight-icon">
                <image src="/images/icons/insight-peak.png" />
              </view>
              <view class="insight-content">
                <text class="insight-title">消费高峰</text>
                <text class="insight-desc">周末是您的主要购物时间，建议提前规划购物清单</text>
              </view>
            </view>
            
            <view class="insight-item">
              <view class="insight-icon">
                <image src="/images/icons/insight-category.png" />
              </view>
              <view class="insight-content">
                <text class="insight-title">主要支出</text>
                <text class="insight-desc">蔬菜类支出占比最高，建议关注时令蔬菜优惠</text>
              </view>
            </view>
            
            <view class="insight-item">
              <view class="insight-icon">
                <image src="/images/icons/insight-trend.png" />
              </view>
              <view class="insight-content">
                <text class="insight-title">消费趋势</text>
                <text class="insight-desc">本月支出较上月增长15%，注意控制预算</text>
              </view>
            </view>
          </view>
        </view>

      </view>
    </view>
  </scroll-view>
</view>