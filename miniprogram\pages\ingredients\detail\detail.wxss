/* 食材详情页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 食材头部 */
.ingredient-header {
  display: flex;
  align-items: flex-start;
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.ingredient-image {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.ingredient-img {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
  background-color: #f5f5f5;
}

.status-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: white;
}

.status-badge.status-1 {
  background-color: #2ed573;
}

.status-badge.status-2 {
  background-color: #ffa502;
}

.status-badge.status-3 {
  background-color: #ff4757;
}

.ingredient-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 160rpx;
}

.ingredient-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.3;
}

.ingredient-category {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  align-self: flex-start;
  margin-bottom: 20rpx;
}

.ingredient-quantity {
  margin-top: auto;
}

.quantity-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #4CAF50;
}

.header-actions {
  margin-left: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  background-color: #e0e0e0;
}

.action-btn image {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 详细信息区域 */
.detail-section,
.nutrition-section,
.recipes-section {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-more {
  font-size: 26rpx;
  color: #4CAF50;
}

.detail-list {
  padding: 0 30rpx 20rpx;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  width: 200rpx;
}

.detail-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  opacity: 0.6;
}

.detail-label text {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}

.expire-date {
  font-weight: 500;
}

.notes-text {
  line-height: 1.5;
  word-break: break-all;
}

/* 营养信息 */
.nutrition-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  padding: 30rpx;
}

.nutrition-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.nutrition-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #4CAF50;
  margin-bottom: 8rpx;
}

.nutrition-label {
  font-size: 22rpx;
  color: #999;
}

/* 相关菜谱 */
.recipes-scroll {
  white-space: nowrap;
}

.recipes-list {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
}

.recipe-item {
  flex-shrink: 0;
  width: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.recipe-img {
  width: 160rpx;
  height: 120rpx;
  border-radius: 12rpx;
  background-color: #f5f5f5;
  margin-bottom: 12rpx;
}

.recipe-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  margin-bottom: 6rpx;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recipe-time {
  font-size: 22rpx;
  color: #999;
}

/* 快捷操作 */
.quick-actions {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  padding: 30rpx;
}

.quick-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  transition: all 0.3s ease;
}

.quick-action-item:active {
  background-color: #f5f5f5;
  border-radius: 12rpx;
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 12rpx;
  opacity: 0.8;
}

.action-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.btn-half {
  flex: 1;
  padding: 28rpx;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 16rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .ingredient-header {
    padding: 24rpx;
  }
  
  .ingredient-image {
    width: 140rpx;
    height: 140rpx;
    margin-right: 24rpx;
  }
  
  .ingredient-name {
    font-size: 32rpx;
  }
  
  .quantity-text {
    font-size: 28rpx;
  }
  
  .section-title {
    font-size: 30rpx;
    padding: 24rpx 24rpx 16rpx;
  }
  
  .detail-list {
    padding: 0 24rpx 16rpx;
  }
  
  .detail-item {
    padding: 20rpx 0;
  }
  
  .detail-label {
    width: 160rpx;
  }
  
  .detail-label text,
  .detail-value {
    font-size: 26rpx;
  }
  
  .nutrition-grid {
    padding: 24rpx;
    gap: 16rpx;
  }
  
  .nutrition-value {
    font-size: 28rpx;
  }
  
  .recipes-list {
    padding: 24rpx;
    gap: 16rpx;
  }
  
  .recipe-item {
    width: 160rpx;
  }
  
  .recipe-img {
    width: 140rpx;
    height: 100rpx;
  }
  
  .action-grid {
    padding: 24rpx;
  }
  
  .quick-action-item {
    padding: 16rpx 8rpx;
  }
  
  .action-icon {
    width: 40rpx;
    height: 40rpx;
  }
  
  .action-text {
    font-size: 22rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .ingredient-header,
  .detail-section,
  .nutrition-section,
  .recipes-section,
  .quick-actions,
  .bottom-actions {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .ingredient-name,
  .section-title,
  .detail-value,
  .recipe-name {
    color: white;
  }
  
  .ingredient-category {
    background-color: #404040;
    color: #999;
  }
  
  .detail-label text,
  .action-text {
    color: #ccc;
  }
  
  .action-btn {
    background-color: #404040;
  }
  
  .action-btn:active {
    background-color: #555;
  }
  
  .quick-action-item:active {
    background-color: #404040;
  }
  
  .detail-item {
    border-color: #404040;
  }
  
  .loading-container {
    background-color: #1a1a1a;
  }
  
  .loading-text {
    color: #ccc;
  }
}

/* 动画效果 */
.ingredient-header,
.detail-section,
.nutrition-section,
.recipes-section,
.quick-actions {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 图片预览效果 */
.ingredient-image:active {
  transform: scale(0.98);
  transition: transform 0.2s ease;
}

/* Emoji图标样式 */
.emoji-icon {
  font-size: 32rpx;
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
}

/* 详情页面图标样式 */
.detail-icon.emoji-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  width: 28rpx;
  text-align: center;
}

/* 操作按钮图标样式 */
.action-icon.emoji-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
  display: block;
  text-align: center;
}

/* 头部操作按钮图标 */
.action-btn .emoji-icon {
  font-size: 24rpx;
  color: #666;
}