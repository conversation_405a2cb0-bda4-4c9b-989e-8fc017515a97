<!--个人中心页面-->
<view class="page-container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-avatar">
      <image 
        src="{{userInfo.avatar_url || '/images/default-avatar.png'}}" 
        mode="aspectFill"
        class="avatar-img"
      ></image>
      <view class="edit-avatar-btn" bindtap="editProfile">
        <image src="/images/icons/camera.png" mode="aspectFit"></image>
      </view>
    </view>
    
    <view class="user-info">
      <text class="user-name">{{userInfo.nickname || '未设置昵称'}}</text>
      <text class="user-phone">{{userInfo.phone}}</text>
      <text class="join-date">加入时间: {{userInfo.created_at}}</text>
    </view>
    
    <view class="edit-profile-btn" bindtap="editProfile">
      <image src="/images/icons/edit.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 数据统计 -->
  <view class="stats-section">
    <text class="section-title">我的数据</text>
    <view class="stats-grid">
      <view class="stats-item" bindtap="viewMyIngredients">
        <view class="stats-icon ingredients-icon">
          <image src="/images/icons/ingredients.png" mode="aspectFit"></image>
        </view>
        <text class="stats-number">{{stats.ingredientsCount}}</text>
        <text class="stats-label">食材</text>
      </view>
      
      <view class="stats-item" bindtap="viewMyRecipes">
        <view class="stats-icon recipes-icon">
          <image src="/images/icons/recipes.png" mode="aspectFit"></image>
        </view>
        <text class="stats-number">{{stats.recipesCount}}</text>
        <text class="stats-label">菜谱</text>
      </view>
      
      <view class="stats-item" bindtap="viewShoppingList">
        <view class="stats-icon shopping-icon">
          <image src="/images/icons/shopping.png" mode="aspectFit"></image>
        </view>
        <text class="stats-number">{{stats.shoppingCount}}</text>
        <text class="stats-label">购物清单</text>
      </view>
      
      <view class="stats-item" bindtap="viewFavorites">
        <view class="stats-icon favorites-icon">
          <image src="/images/icons/heart.png" mode="aspectFit"></image>
        </view>
        <text class="stats-number">{{stats.favoritesCount}}</text>
        <text class="stats-label">收藏</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <text class="section-title">功能菜单</text>
    <view class="menu-list">
      <view class="menu-item" bindtap="viewAnalysis">
        <view class="menu-icon analysis-icon">
          <image src="/images/icons/chart.png" mode="aspectFit"></image>
        </view>
        <view class="menu-content">
          <text class="menu-title">数据分析</text>
          <text class="menu-desc">查看消费趋势和健康分析</text>
        </view>
        <view class="menu-arrow">
          <image src="/images/icons/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="menu-item" bindtap="backupData">
        <view class="menu-icon backup-icon">
          <image src="/images/icons/backup.png" mode="aspectFit"></image>
        </view>
        <view class="menu-content">
          <text class="menu-title">数据备份</text>
          <text class="menu-desc">备份您的重要数据</text>
        </view>
        <view class="menu-arrow">
          <image src="/images/icons/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="menu-item" bindtap="restoreData">
        <view class="menu-icon restore-icon">
          <image src="/images/icons/restore.png" mode="aspectFit"></image>
        </view>
        <view class="menu-content">
          <text class="menu-title">数据恢复</text>
          <text class="menu-desc">从备份恢复数据</text>
        </view>
        <view class="menu-arrow">
          <image src="/images/icons/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 设置选项 -->
  <view class="settings-section">
    <text class="section-title">设置</text>
    <view class="settings-list">
      <view class="setting-item">
        <view class="setting-info">
          <view class="setting-icon notifications-icon">
            <image src="/images/icons/bell.png" mode="aspectFit"></image>
          </view>
          <view class="setting-content">
            <text class="setting-title">消息通知</text>
            <text class="setting-desc">接收过期提醒和推荐通知</text>
          </view>
        </view>
        <switch 
          class="setting-switch"
          checked="{{settings.notifications}}"
          bindchange="toggleNotifications"
        />
      </view>

      <view class="setting-item">
        <view class="setting-info">
          <view class="setting-icon dark-mode-icon">
            <image src="/images/icons/moon.png" mode="aspectFit"></image>
          </view>
          <view class="setting-content">
            <text class="setting-title">深色模式</text>
            <text class="setting-desc">启用深色主题</text>
          </view>
        </view>
        <switch 
          class="setting-switch"
          checked="{{settings.darkMode}}"
          bindchange="toggleDarkMode"
        />
      </view>

      <view class="setting-item">
        <view class="setting-info">
          <view class="setting-icon sync-icon">
            <image src="/images/icons/sync.png" mode="aspectFit"></image>
          </view>
          <view class="setting-content">
            <text class="setting-title">自动同步</text>
            <text class="setting-desc">自动同步数据到云端</text>
          </view>
        </view>
        <switch 
          class="setting-switch"
          checked="{{settings.autoSync}}"
          bindchange="toggleAutoSync"
        />
      </view>
    </view>
  </view>

  <!-- 其他选项 -->
  <view class="other-section">
    <text class="section-title">其他</text>
    <view class="other-list">
      <view class="other-item" bindtap="showHelp">
        <view class="other-icon help-icon">
          <image src="/images/icons/help.png" mode="aspectFit"></image>
        </view>
        <text class="other-title">帮助中心</text>
        <view class="other-arrow">
          <image src="/images/icons/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="other-item" bindtap="showFeedback">
        <view class="other-icon feedback-icon">
          <image src="/images/icons/feedback.png" mode="aspectFit"></image>
        </view>
        <text class="other-title">意见反馈</text>
        <view class="other-arrow">
          <image src="/images/icons/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="other-item" bindtap="showAbout">
        <view class="other-icon about-icon">
          <image src="/images/icons/info.png" mode="aspectFit"></image>
        </view>
        <text class="other-title">关于我们</text>
        <view class="other-arrow">
          <image src="/images/icons/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="other-item" bindtap="showPrivacy">
        <view class="other-icon privacy-icon">
          <image src="/images/icons/shield.png" mode="aspectFit"></image>
        </view>
        <text class="other-title">隐私政策</text>
        <view class="other-arrow">
          <image src="/images/icons/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="other-item" bindtap="checkUpdate">
        <view class="other-icon update-icon">
          <image src="/images/icons/update.png" mode="aspectFit"></image>
        </view>
        <text class="other-title">检查更新</text>
        <view class="other-arrow">
          <image src="/images/icons/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 危险操作区域 -->
  <view class="danger-section">
    <text class="section-title danger-title">危险操作</text>
    <view class="danger-list">
      <view class="danger-item" bindtap="clearAllData">
        <view class="danger-icon">
          <image src="/images/icons/trash.png" mode="aspectFit"></image>
        </view>
        <text class="danger-title">清空所有数据</text>
        <view class="danger-arrow">
          <image src="/images/icons/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="logout">
      退出登录
    </button>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">智能冰箱食材管理助手 v1.0.0</text>
    <text class="copyright-text">© 2024 All Rights Reserved</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</view>