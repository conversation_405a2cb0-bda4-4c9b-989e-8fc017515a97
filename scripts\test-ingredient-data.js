// 测试食材数据传输
const axios = require('axios');

const API_BASE = 'http://127.0.0.1:3000';

// 模拟前端数据格式
function createFrontendData() {
  const today = new Date();
  const expireDate = new Date(today);
  expireDate.setDate(today.getDate() + 7);
  
  return {
    // 前端格式
    name: '测试食材',
    category: '蔬菜',
    quantity: 2,
    unit: '个',
    purchaseDate: formatDate(today),
    expiryDate: formatDate(expireDate),
    location: '冰箱',
    price: 10.5,
    notes: '测试备注'
  };
}

// 模拟前端字段映射
function mapToBackendFormat(frontendData) {
  const categoryMap = {
    '蔬菜': 1,
    '水果': 2,
    '肉类': 3,
    '海鲜': 4,
    '蛋奶': 5,
    '调料': 6,
    '零食': 7,
    '饮品': 8,
    '其他': 9
  };

  return {
    name: frontendData.name,
    category_id: categoryMap[frontendData.category] || 9,
    quantity: Number(frontendData.quantity),
    unit: frontendData.unit,
    purchase_date: frontendData.purchaseDate,
    expire_date: frontendData.expiryDate,
    storage_location: frontendData.location,
    price: frontendData.price ? Number(frontendData.price) : null,
    notes: frontendData.notes,
    userId: 'test_user'
  };
}

function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

async function testIngredientSubmission() {
  try {
    console.log('🧪 测试食材数据传输...\n');
    
    // 1. 创建前端数据
    const frontendData = createFrontendData();
    console.log('📤 前端数据格式:');
    console.log(JSON.stringify(frontendData, null, 2));
    
    // 2. 映射到后端格式
    const backendData = mapToBackendFormat(frontendData);
    console.log('\n📥 后端数据格式:');
    console.log(JSON.stringify(backendData, null, 2));
    
    // 3. 验证字段映射
    console.log('\n🔄 字段映射验证:');
    console.log(`purchaseDate -> purchase_date: ${frontendData.purchaseDate} -> ${backendData.purchase_date}`);
    console.log(`expiryDate -> expire_date: ${frontendData.expiryDate} -> ${backendData.expire_date}`);
    console.log(`location -> storage_location: ${frontendData.location} -> ${backendData.storage_location}`);
    console.log(`category -> category_id: ${frontendData.category} -> ${backendData.category_id}`);
    
    // 4. 检查必填字段
    console.log('\n✅ 必填字段检查:');
    const requiredFields = ['name', 'category_id', 'quantity', 'unit'];
    requiredFields.forEach(field => {
      const value = backendData[field];
      const status = value !== undefined && value !== null && value !== '' ? '✅' : '❌';
      console.log(`${status} ${field}: ${value}`);
    });
    
    // 5. 检查日期格式
    console.log('\n📅 日期格式检查:');
    const dateFields = ['purchase_date', 'expire_date'];
    dateFields.forEach(field => {
      const value = backendData[field];
      const isValidDate = value && /^\d{4}-\d{2}-\d{2}$/.test(value);
      const status = isValidDate ? '✅' : '❌';
      console.log(`${status} ${field}: ${value} (格式: ${isValidDate ? 'YYYY-MM-DD' : '无效'})`);
    });
    
    console.log('\n🎉 数据格式验证完成！');
    
    // 如果需要测试实际API调用，取消下面的注释
    /*
    console.log('\n📡 发送API请求...');
    const response = await axios.post(`${API_BASE}/api/ingredients`, backendData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test_token'
      }
    });
    
    console.log('✅ API响应:', response.status);
    console.log('📥 响应数据:', response.data);
    */
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 测试日期计算
function testDateCalculation() {
  console.log('\n📅 测试日期计算...\n');
  
  const today = new Date();
  const testDates = [
    { days: -3, label: '3天前' },
    { days: -1, label: '1天前' },
    { days: 0, label: '今天' },
    { days: 1, label: '1天后' },
    { days: 7, label: '7天后' },
    { days: 30, label: '30天后' }
  ];
  
  testDates.forEach(test => {
    const date = new Date(today);
    date.setDate(today.getDate() + test.days);
    const formatted = formatDate(date);
    console.log(`${test.label}: ${formatted}`);
  });
}

// 运行测试
if (require.main === module) {
  console.log('🚀 开始食材数据测试...\n');
  
  testDateCalculation();
  testIngredientSubmission();
  
  console.log('\n' + '='.repeat(50));
  console.log('💡 修复建议:');
  console.log('1. 确保前端字段名正确映射到后端字段名');
  console.log('2. 检查日期格式是否为 YYYY-MM-DD');
  console.log('3. 验证分类ID映射是否正确');
  console.log('4. 确保必填字段都有值');
  console.log('='.repeat(50));
}

module.exports = { 
  createFrontendData, 
  mapToBackendFormat, 
  testIngredientSubmission,
  testDateCalculation 
};
