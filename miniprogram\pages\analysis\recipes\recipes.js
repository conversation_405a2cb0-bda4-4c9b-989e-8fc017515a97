// 菜谱分析页面
const app = getApp();

Page({
  data: {
    // 统计数据
    stats: {
      totalRecipes: 0,
      favoriteRecipes: 0,
      categoryStats: [],
      difficultyStats: [],
      cookingTimeStats: [],
      popularRecipes: [],
      recentActivity: []
    },
    
    // 页面状态
    loading: false,
    refreshing: false,
    
    // 时间范围
    timeRange: 'month',
    timeRanges: [
      { id: 'week', name: '本周' },
      { id: 'month', name: '本月' },
      { id: 'quarter', name: '本季度' },
      { id: 'year', name: '本年' }
    ],
    
    // 图表类型
    chartType: 'category',
    chartTypes: [
      { id: 'category', name: '分类统计' },
      { id: 'difficulty', name: '难度分布' },
      { id: 'time', name: '用时分析' },
      { id: 'popular', name: '热门菜谱' }
    ]
  },

  onLoad() {
    console.log('菜谱分析页面加载');
    this.loadAnalysisData();
  },

  onShow() {
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  // 加载分析数据
  async loadAnalysisData() {
    try {
      this.setData({ loading: true });

      const params = {
        timeRange: this.data.timeRange
      };

      const result = await app.api.getRecipesAnalysis(params);
      
      this.setData({
        stats: result,
        loading: false
      });

    } catch (error) {
      console.error('加载菜谱分析数据失败:', error);
      app.showError('加载失败');
      this.setData({ loading: false });
    }
  },

  // 刷新数据
  async refreshData() {
    try {
      this.setData({ refreshing: true });

      const params = {
        timeRange: this.data.timeRange
      };

      const result = await app.api.getRecipesAnalysis(params);
      
      this.setData({
        stats: result,
        refreshing: false
      });

      wx.stopPullDownRefresh();

    } catch (error) {
      console.error('刷新数据失败:', error);
      app.showError('刷新失败');
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  // 切换时间范围
  switchTimeRange(e) {
    const timeRange = e.currentTarget.dataset.range;
    if (timeRange === this.data.timeRange) return;

    this.setData({ timeRange });
    this.loadAnalysisData();
  },

  // 切换图表类型
  switchChartType(e) {
    const chartType = e.currentTarget.dataset.type;
    this.setData({ chartType });
  },

  // 查看分类详情
  viewCategoryDetail(e) {
    const category = e.currentTarget.dataset.category;
    wx.navigateTo({
      url: `/pages/recipes/list/list?category=${category}`
    });
  },

  // 查看菜谱详情
  viewRecipeDetail(e) {
    const recipeId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/recipes/detail/detail?id=${recipeId}`
    });
  },

  // 查看收藏菜谱
  viewFavoriteRecipes() {
    wx.navigateTo({
      url: '/pages/favorites/list/list?category=recipes'
    });
  },

  // 获取难度颜色
  getDifficultyColor(difficulty) {
    const colors = {
      easy: '#28a745',
      medium: '#ffc107',
      hard: '#dc3545'
    };
    return colors[difficulty] || '#6c757d';
  },

  // 获取难度名称
  getDifficultyName(difficulty) {
    const names = {
      easy: '简单',
      medium: '中等',
      hard: '困难'
    };
    return names[difficulty] || '未知';
  },

  // 格式化烹饪时间
  formatCookingTime(minutes) {
    if (minutes < 60) {
      return `${minutes}分钟`;
    } else {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
    }
  },

  // 计算百分比
  calculatePercentage(value, total) {
    if (total === 0) return 0;
    return Math.round((value / total) * 100);
  },

  // 格式化日期
  formatDate(dateStr) {
    const date = new Date(dateStr);
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return '今天';
    if (days === 1) return '昨天';
    if (days < 7) return `${days}天前`;
    if (days < 30) return `${Math.floor(days / 7)}周前`;
    return `${Math.floor(days / 30)}月前`;
  }
});