const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { uploadSingle } = require('../middleware/upload');

const router = express.Router();

// 获取食材列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category_id,
      status,
      search,
      sort = 'created_at',
      order = 'DESC'
    } = req.query;

    const pageNum = parseInt(page) || 1;
    const limitNum = parseInt(limit) || 20;
    const offset = (pageNum - 1) * limitNum;
    let whereConditions = ['ui.user_id = ?', 'ui.is_deleted = 0'];
    let queryParams = [req.user.id];
    let needsJoin = false;

    // 按分类筛选
    if (category_id && category_id !== 'all' && category_id.trim() !== '') {
      // 如果category_id是数字，直接使用
      if (!isNaN(category_id) && Number.isInteger(Number(category_id))) {
        whereConditions.push('ui.category_id = ?');
        queryParams.push(parseInt(category_id));
      } else {
        // 如果是分类名称，通过名称查找ID
        whereConditions.push('ic.name = ?');
        queryParams.push(String(category_id).trim());
        needsJoin = true;
      }
    }

    // 按状态筛选
    if (status) {
      whereConditions.push('ui.status = ?');
      queryParams.push(status);
    }

    // 搜索功能
    if (search) {
      whereConditions.push('ui.name LIKE ?');
      queryParams.push(`%${search}%`);
    }

    // 验证排序字段和排序方向
    const validSortFields = ['created_at', 'updated_at', 'name', 'quantity', 'expire_date', 'status'];
    const validSortOrders = ['ASC', 'DESC', 'asc', 'desc'];
    const sortField = validSortFields.includes(sort) ? sort : 'created_at';
    const sortOrder = validSortOrders.includes(order) ? order.toUpperCase() : 'DESC';

    // 构建查询语句
    const whereClause = whereConditions.join(' AND ');

    // 构建JOIN子句
    const joinClause = needsJoin || true ? 'LEFT JOIN ingredient_categories ic ON ui.category_id = ic.id' : '';

    const ingredients = await query(`
      SELECT
        ui.*,
        ic.name as category_name,
        CASE
          WHEN ui.expire_date < CURDATE() THEN 3
          WHEN ui.expire_date <= DATE_ADD(CURDATE(), INTERVAL 3 DAY) THEN 2
          ELSE 1
        END as calculated_status,
        DATEDIFF(ui.expire_date, CURDATE()) as days_until_expire
      FROM user_ingredients ui
      ${joinClause}
      WHERE ${whereClause}
      ORDER BY ui.${sortField} ${sortOrder}
      LIMIT ${limitNum} OFFSET ${offset}
    `, queryParams);

    // 更新状态
    for (const ingredient of ingredients) {
      if (ingredient.calculated_status !== ingredient.status) {
        await query(
          'UPDATE user_ingredients SET status = ? WHERE id = ?',
          [ingredient.calculated_status, ingredient.id]
        );
        ingredient.status = ingredient.calculated_status;
      }
    }

    // 获取总数
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM user_ingredients ui
      ${joinClause}
      WHERE ${whereClause}
    `, queryParams);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limitNum);
    const hasMore = pageNum < totalPages;

    res.json({
      success: true,
      data: {
        ingredients,
        has_more: hasMore,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: totalPages
        }
      }
    });
  } catch (error) {
    console.error('获取食材列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取食材列表失败'
    });
  }
});

// 获取食材详情
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const ingredients = await query(`
      SELECT 
        ui.*,
        ic.name as category_name
      FROM user_ingredients ui
      LEFT JOIN ingredient_categories ic ON ui.category_id = ic.id
      WHERE ui.id = ? AND ui.user_id = ? AND ui.is_deleted = 0
    `, [id, req.user.id]);

    if (ingredients.length === 0) {
      return res.status(404).json({
        success: false,
        message: '食材不存在'
      });
    }

    const ingredient = ingredients[0];

    // 计算过期状态
    const today = new Date();
    const expireDate = new Date(ingredient.expire_date);
    const diffTime = expireDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    let status = 1; // 正常
    if (diffDays < 0) {
      status = 3; // 已过期
    } else if (diffDays <= 3) {
      status = 2; // 即将过期
    }

    // 更新状态
    if (status !== ingredient.status) {
      await query(
        'UPDATE user_ingredients SET status = ? WHERE id = ?',
        [status, ingredient.id]
      );
      ingredient.status = status;
    }

    ingredient.days_until_expire = diffDays;

    res.json({
      success: true,
      data: ingredient
    });
  } catch (error) {
    console.error('获取食材详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取食材详情失败'
    });
  }
});

// 添加食材
router.post('/', authenticateToken, async (req, res) => {
  try {
    console.log('接收到的请求数据:', req.body);

    const {
      name,
      category_id,
      quantity = 1,
      unit = '个',
      purchase_date,
      expire_date,
      storage_location,
      price = 0,
      notes
    } = req.body;

    console.log('解析后的日期字段:', {
      purchase_date,
      expire_date,
      storage_location
    });

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '食材名称不能为空'
      });
    }

    // 计算初始状态
    let status = 1;
    if (expire_date) {
      const today = new Date();
      const expireDate = new Date(expire_date);
      const diffTime = expireDate - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 0) {
        status = 3; // 已过期
      } else if (diffDays <= 3) {
        status = 2; // 即将过期
      }
    }

    const result = await query(`
      INSERT INTO user_ingredients 
      (user_id, name, category_id, quantity, unit, purchase_date, expire_date, 
       storage_location, price, notes, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      req.user.id,
      name,
      category_id || null,
      quantity,
      unit,
      purchase_date || null,
      expire_date || null,
      storage_location || null,
      price,
      notes || null,
      status
    ]);

    // 记录用户操作
    await query(
      'INSERT INTO user_records (user_id, type, content) VALUES (?, ?, ?)',
      [
        req.user.id,
        'ingredient_add',
        JSON.stringify({
          ingredient_id: result.insertId,
          ingredient_name: name,
          quantity,
          unit
        })
      ]
    );

    // 获取新添加的食材信息
    const newIngredient = await query(`
      SELECT 
        ui.*,
        ic.name as category_name
      FROM user_ingredients ui
      LEFT JOIN ingredient_categories ic ON ui.category_id = ic.id
      WHERE ui.id = ?
    `, [result.insertId]);

    res.status(201).json({
      success: true,
      message: '食材添加成功',
      data: newIngredient[0]
    });
  } catch (error) {
    console.error('添加食材错误:', error);
    res.status(500).json({
      success: false,
      message: '添加食材失败'
    });
  }
});

// 更新食材
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      category_id,
      quantity,
      unit,
      purchase_date,
      expire_date,
      storage_location,
      price,
      notes
    } = req.body;

    // 检查食材是否存在且属于当前用户
    const existingIngredients = await query(
      'SELECT id, name FROM user_ingredients WHERE id = ? AND user_id = ? AND is_deleted = 0',
      [id, req.user.id]
    );

    if (existingIngredients.length === 0) {
      return res.status(404).json({
        success: false,
        message: '食材不存在'
      });
    }

    const updateFields = [];
    const updateValues = [];

    if (name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(name);
    }
    if (category_id !== undefined) {
      updateFields.push('category_id = ?');
      updateValues.push(category_id);
    }
    if (quantity !== undefined) {
      updateFields.push('quantity = ?');
      updateValues.push(quantity);
    }
    if (unit !== undefined) {
      updateFields.push('unit = ?');
      updateValues.push(unit);
    }
    if (purchase_date !== undefined) {
      updateFields.push('purchase_date = ?');
      updateValues.push(purchase_date);
    }
    if (expire_date !== undefined) {
      updateFields.push('expire_date = ?');
      updateValues.push(expire_date);
      
      // 重新计算状态
      let status = 1;
      if (expire_date) {
        const today = new Date();
        const expireDate = new Date(expire_date);
        const diffTime = expireDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays < 0) {
          status = 3;
        } else if (diffDays <= 3) {
          status = 2;
        }
      }
      updateFields.push('status = ?');
      updateValues.push(status);
    }
    if (storage_location !== undefined) {
      updateFields.push('storage_location = ?');
      updateValues.push(storage_location);
    }
    if (price !== undefined) {
      updateFields.push('price = ?');
      updateValues.push(price);
    }
    if (notes !== undefined) {
      updateFields.push('notes = ?');
      updateValues.push(notes);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有需要更新的字段'
      });
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id, req.user.id);

    await query(
      `UPDATE user_ingredients SET ${updateFields.join(', ')} WHERE id = ? AND user_id = ?`,
      updateValues
    );

    // 获取更新后的食材信息
    const updatedIngredient = await query(`
      SELECT 
        ui.*,
        ic.name as category_name
      FROM user_ingredients ui
      LEFT JOIN ingredient_categories ic ON ui.category_id = ic.id
      WHERE ui.id = ?
    `, [id]);

    res.json({
      success: true,
      message: '食材更新成功',
      data: updatedIngredient[0]
    });
  } catch (error) {
    console.error('更新食材错误:', error);
    res.status(500).json({
      success: false,
      message: '更新食材失败'
    });
  }
});

// 删除食材
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // 检查食材是否存在且属于当前用户
    const ingredients = await query(
      'SELECT id, name FROM user_ingredients WHERE id = ? AND user_id = ? AND is_deleted = 0',
      [id, req.user.id]
    );

    if (ingredients.length === 0) {
      return res.status(404).json({
        success: false,
        message: '食材不存在'
      });
    }

    // 软删除食材
    await query(
      'UPDATE user_ingredients SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
      [id, req.user.id]
    );

    res.json({
      success: true,
      message: '食材删除成功'
    });
  } catch (error) {
    console.error('删除食材错误:', error);
    res.status(500).json({
      success: false,
      message: '删除食材失败'
    });
  }
});

// 批量操作食材
router.post('/batch', authenticateToken, async (req, res) => {
  try {
    const { action, ids } = req.body;

    if (!action || !ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '参数错误'
      });
    }

    const placeholders = ids.map(() => '?').join(',');
    let message = '';

    switch (action) {
      case 'delete':
        await query(
          `UPDATE user_ingredients SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP 
           WHERE id IN (${placeholders}) AND user_id = ?`,
          [...ids, req.user.id]
        );
        message = '批量删除成功';
        break;

      case 'update_status':
        const { status } = req.body;
        if (!status) {
          return res.status(400).json({
            success: false,
            message: '状态参数缺失'
          });
        }
        await query(
          `UPDATE user_ingredients SET status = ?, updated_at = CURRENT_TIMESTAMP 
           WHERE id IN (${placeholders}) AND user_id = ?`,
          [status, ...ids, req.user.id]
        );
        message = '批量更新状态成功';
        break;

      default:
        return res.status(400).json({
          success: false,
          message: '不支持的操作类型'
        });
    }

    res.json({
      success: true,
      message
    });
  } catch (error) {
    console.error('批量操作食材错误:', error);
    res.status(500).json({
      success: false,
      message: '批量操作失败'
    });
  }
});

// 批量删除过期食材
router.post('/batch/delete-expired', authenticateToken, async (req, res) => {
  try {
    console.log('批量删除过期食材请求，用户ID:', req.user.id);

    // 查找所有过期的食材
    const expiredIngredients = await query(`
      SELECT id, name, expire_date
      FROM user_ingredients
      WHERE user_id = ?
        AND is_deleted = 0
        AND expire_date < CURDATE()
    `, [req.user.id]);

    if (expiredIngredients.length === 0) {
      return res.json({
        success: true,
        message: '没有找到过期的食材',
        data: {
          deletedCount: 0,
          deletedItems: []
        }
      });
    }

    console.log(`找到 ${expiredIngredients.length} 个过期食材:`, expiredIngredients.map(item => item.name));

    // 批量删除过期食材（软删除）
    const expiredIds = expiredIngredients.map(item => item.id);
    const placeholders = expiredIds.map(() => '?').join(',');

    await query(`
      UPDATE user_ingredients
      SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP
      WHERE id IN (${placeholders}) AND user_id = ?
    `, [...expiredIds, req.user.id]);

    // 记录用户操作
    await query(
      'INSERT INTO user_records (user_id, type, content) VALUES (?, ?, ?)',
      [
        req.user.id,
        'batch_delete_expired',
        JSON.stringify({
          deletedCount: expiredIngredients.length,
          deletedItems: expiredIngredients.map(item => ({
            id: item.id,
            name: item.name,
            expire_date: item.expire_date
          }))
        })
      ]
    );

    console.log(`成功删除 ${expiredIngredients.length} 个过期食材`);

    res.json({
      success: true,
      message: `成功删除 ${expiredIngredients.length} 个过期食材`,
      data: {
        deletedCount: expiredIngredients.length,
        deletedItems: expiredIngredients.map(item => ({
          id: item.id,
          name: item.name,
          expire_date: item.expire_date
        }))
      }
    });
  } catch (error) {
    console.error('批量删除过期食材错误:', error);
    res.status(500).json({
      success: false,
      message: '删除过期食材失败'
    });
  }
});

// 上传食材图片
router.post('/:id/image', authenticateToken, uploadSingle('ingredient_image'), async (req, res) => {
  try {
    const { id } = req.params;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的图片文件'
      });
    }

    // 检查食材是否存在且属于当前用户
    const ingredients = await query(
      'SELECT id FROM user_ingredients WHERE id = ? AND user_id = ? AND is_deleted = 0',
      [id, req.user.id]
    );

    if (ingredients.length === 0) {
      return res.status(404).json({
        success: false,
        message: '食材不存在'
      });
    }

    // 更新食材图片
    await query(
      'UPDATE user_ingredients SET image = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [req.file.url, id]
    );

    res.json({
      success: true,
      message: '图片上传成功',
      data: {
        image: req.file.url
      }
    });
  } catch (error) {
    console.error('上传食材图片错误:', error);
    res.status(500).json({
      success: false,
      message: '图片上传失败'
    });
  }
});

// 获取食材分类
router.get('/categories/list', async (req, res) => {
  try {
    const categories = await query(
      'SELECT * FROM ingredient_categories WHERE is_deleted = 0 ORDER BY sort_order ASC'
    );

    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('获取食材分类错误:', error);
    res.status(500).json({
      success: false,
      message: '获取食材分类失败'
    });
  }
});

// 获取常用食材
router.get('/common/list', async (req, res) => {
  try {
    const { category_id, search, limit = 50 } = req.query;
    let whereConditions = ['is_deleted = 0'];
    let queryParams = [];

    if (category_id) {
      whereConditions.push('category_id = ?');
      queryParams.push(category_id);
    }

    if (search) {
      whereConditions.push('name LIKE ?');
      queryParams.push(`%${search}%`);
    }

    const whereClause = whereConditions.join(' AND ');
    queryParams.push(parseInt(limit));

    const commonIngredients = await query(`
      SELECT 
        ci.*,
        ic.name as category_name
      FROM common_ingredients ci
      LEFT JOIN ingredient_categories ic ON ci.category_id = ic.id
      WHERE ${whereClause}
      ORDER BY ci.usage_count DESC, ci.name ASC
      LIMIT ?
    `, queryParams);

    res.json({
      success: true,
      data: commonIngredients
    });
  } catch (error) {
    console.error('获取常用食材错误:', error);
    res.status(500).json({
      success: false,
      message: '获取常用食材失败'
    });
  }
});

// 使用食材（减少数量）
router.post('/:id/use', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { quantity = 1, notes } = req.body;

    // 获取当前食材信息
    const ingredients = await query(
      'SELECT * FROM user_ingredients WHERE id = ? AND user_id = ? AND is_deleted = 0',
      [id, req.user.id]
    );

    if (ingredients.length === 0) {
      return res.status(404).json({
        success: false,
        message: '食材不存在'
      });
    }

    const ingredient = ingredients[0];
    const newQuantity = Math.max(0, parseFloat(ingredient.quantity) - parseFloat(quantity));

    // 更新食材数量
    await query(
      'UPDATE user_ingredients SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [newQuantity, id]
    );

    // 记录使用记录
    await query(
      'INSERT INTO user_records (user_id, type, content) VALUES (?, ?, ?)',
      [
        req.user.id,
        'ingredient_use',
        JSON.stringify({
          ingredient_id: id,
          ingredient_name: ingredient.name,
          used_quantity: quantity,
          remaining_quantity: newQuantity,
          notes: notes || null
        })
      ]
    );

    res.json({
      success: true,
      message: '食材使用记录成功',
      data: {
        remaining_quantity: newQuantity
      }
    });
  } catch (error) {
    console.error('使用食材错误:', error);
    res.status(500).json({
      success: false,
      message: '使用食材失败'
    });
  }
});

module.exports = router;