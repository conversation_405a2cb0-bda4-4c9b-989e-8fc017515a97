/* 购物清单列表页面样式 */

.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 头部统计 */
.header-stats {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
}

.stats-card {
  display: flex;
  gap: 60rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.action-btn image {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

/* 工具栏 */
.toolbar {
  background-color: white;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.sort-options {
  display: flex;
  gap: 20rpx;
}

.sort-btn {
  padding: 12rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 16rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.sort-btn.active {
  background-color: #4CAF50;
  color: white;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.toggle-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.toggle-btn.active {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.toggle-icon {
  width: 24rpx;
  height: 24rpx;
}

.toggle-text {
  font-size: 24rpx;
}

.edit-btn {
  padding: 12rpx 20rpx;
  background-color: #4CAF50;
  border-radius: 16rpx;
  color: white;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.edit-btn:active {
  background-color: #45a049;
}

.edit-text {
  color: white;
}

/* 购物清单内容 */
.shopping-content {
  padding: 0 30rpx;
}

/* 分类组 */
.category-group {
  margin-bottom: 40rpx;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
}

.category-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.category-count {
  font-size: 24rpx;
  color: #999;
}

/* 项目列表 */
.items-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.shopping-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
}

.shopping-item:active {
  transform: scale(0.98);
}

.shopping-item.completed {
  opacity: 0.7;
  background-color: #f8f9fa;
}

.shopping-item.edit-mode {
  padding-left: 80rpx;
}

/* 选择框 */
.item-checkbox {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background-color: #4CAF50;
  border-color: #4CAF50;
}

.checkbox image {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

/* 状态按钮 */
.item-status {
  flex-shrink: 0;
}

.status-circle {
  width: 48rpx;
  height: 48rpx;
  border: 3rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.status-circle.completed {
  background-color: #4CAF50;
  border-color: #4CAF50;
}

.status-circle image {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

/* 项目信息 */
.item-info,
.item-info-edit {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.item-main {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap;
}

.item-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
  min-width: 0;
}

.item-name.completed-text {
  text-decoration: line-through;
  color: #999;
}

.item-category {
  font-size: 22rpx;
  color: #4CAF50;
  background-color: rgba(76, 175, 80, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.item-quantity {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex-wrap: wrap;
}

.item-price {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: 600;
}

.item-notes {
  font-size: 24rpx;
  color: #999;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-time {
  margin-top: 8rpx;
}

.purchased-time {
  font-size: 22rpx;
  color: #4CAF50;
}

/* 项目操作 */
.item-actions {
  display: flex;
  gap: 12rpx;
  flex-shrink: 0;
}

.edit-item-btn,
.delete-item-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.edit-item-btn {
  background-color: rgba(76, 175, 80, 0.1);
}

.edit-item-btn:active {
  background-color: rgba(76, 175, 80, 0.2);
}

.delete-item-btn {
  background-color: rgba(255, 107, 53, 0.1);
}

.delete-item-btn:active {
  background-color: rgba(255, 107, 53, 0.2);
}

.edit-item-btn image,
.delete-item-btn image {
  width: 24rpx;
  height: 24rpx;
}

.edit-item-btn image {
  filter: hue-rotate(90deg);
}

.delete-item-btn image {
  filter: hue-rotate(10deg);
}

/* 编辑模式工具栏 */
.edit-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.edit-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.select-all-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.select-all-text {
  font-size: 28rpx;
  color: #333;
}

.edit-buttons {
  display: flex;
  gap: 16rpx;
}

.edit-action-btn {
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  border-radius: 12rpx;
  min-width: 120rpx;
}

/* 悬浮按钮 */
.fab-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(76, 175, 80, 0.4);
  z-index: 99;
  transition: all 0.3s ease;
}

.fab-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 12rpx rgba(76, 175, 80, 0.3);
}

.fab-btn image {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  width: 200rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 按钮样式 */
.btn {
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  padding: 20rpx 32rpx;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #4CAF50;
  color: white;
}

.btn-primary:active {
  background-color: #45a049;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
}

.btn-secondary:active {
  background-color: #e0e0e0;
}

.btn-danger {
  background-color: #ff6b35;
  color: white;
}

.btn-danger:active {
  background-color: #e55a2b;
}

.btn:disabled {
  opacity: 0.5;
  pointer-events: none;
}

.btn-block {
  width: 100%;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .header-stats {
    padding: 30rpx 20rpx;
  }
  
  .stats-card {
    gap: 40rpx;
  }
  
  .stats-number {
    font-size: 32rpx;
  }
  
  .toolbar {
    padding: 16rpx 20rpx;
    flex-direction: column;
    gap: 16rpx;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
  
  .shopping-content {
    padding: 0 20rpx;
  }
  
  .shopping-item {
    padding: 20rpx;
    gap: 16rpx;
  }
  
  .item-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }
  
  .item-name {
    font-size: 28rpx;
  }
  
  .item-quantity {
    font-size: 24rpx;
  }
  
  .item-meta {
    gap: 16rpx;
  }
  
  .item-price {
    font-size: 26rpx;
  }
  
  .edit-toolbar {
    padding: 16rpx 20rpx;
  }
  
  .edit-actions {
    flex-direction: column;
    gap: 16rpx;
  }
  
  .edit-buttons {
    justify-content: center;
  }
  
  .fab-btn {
    width: 80rpx;
    height: 80rpx;
    bottom: 30rpx;
    right: 30rpx;
  }
  
  .fab-btn image {
    width: 40rpx;
    height: 40rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .toolbar,
  .shopping-item,
  .edit-toolbar {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .category-name,
  .item-name,
  .select-all-text {
    color: white;
  }
  
  .item-name.completed-text {
    color: #666;
  }
  
  .item-quantity,
  .item-notes {
    color: #999;
  }
  
  .checkbox {
    border-color: #666;
  }
  
  .status-circle {
    border-color: #666;
  }
  
  .sort-btn {
    background-color: #404040;
    color: #ccc;
  }
  
  .edit-item-btn {
    background-color: rgba(76, 175, 80, 0.2);
  }
  
  .delete-item-btn {
    background-color: rgba(255, 107, 53, 0.2);
  }
}

/* 动画效果 */
.shopping-item {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 完成状态动画 */
.status-circle.completed {
  animation: checkBounce 0.6s ease;
}

@keyframes checkBounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

/* 选择动画 */
.checkbox.checked {
  animation: selectBounce 0.3s ease;
}

@keyframes selectBounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}